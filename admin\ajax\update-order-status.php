<?php
session_start();
require_once '../../config/dbconfig.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

if ($_POST && isset($_POST['order_id']) && isset($_POST['status'])) {
    $order_id = intval($_POST['order_id']);
    $status = $_POST['status'];
    $comment = trim($_POST['comment'] ?? '');
    
    // Validate status
    $valid_statuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'];
    if (!in_array($status, $valid_statuses)) {
        echo json_encode(['success' => false, 'message' => 'Invalid status']);
        exit();
    }
    
    try {
        $pdo->beginTransaction();
        
        // Update order status
        $updated = updateData("UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?", [$status, $order_id]);
        
        if ($updated) {
            // Add status history
            executeQuery("
                INSERT INTO order_status_history (order_id, status, comment, changed_by) 
                VALUES (?, ?, ?, ?)
            ", [$order_id, $status, $comment, $_SESSION['admin_id']]);
            
            $pdo->commit();
            echo json_encode(['success' => true, 'message' => 'Order status updated successfully']);
        } else {
            throw new Exception('Failed to update order status');
        }
    } catch (Exception $e) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
}
?>
