<?php
/**
 * Database Setup Script
 * Dr.Zia Naturals - Database Installation
 */

// Include database configuration
require_once '../config/dbconfig.php';

// Function to execute SQL file
function executeSQLFile($pdo, $sqlFile) {
    try {
        $sql = file_get_contents($sqlFile);
        if ($sql === false) {
            throw new Exception("Could not read SQL file: " . $sqlFile);
        }
        
        // Split SQL into individual statements
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        return true;
    } catch (Exception $e) {
        echo "Error executing SQL file: " . $e->getMessage() . "\n";
        return false;
    }
}

// Function to check if database exists
function databaseExists($host, $username, $password, $dbname) {
    try {
        $pdo = new PDO("mysql:host=$host", $username, $password);
        $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
        $stmt->execute([$dbname]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// Function to create database if it doesn't exist
function createDatabase($host, $username, $password, $dbname) {
    try {
        $pdo = new PDO("mysql:host=$host", $username, $password);
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        return true;
    } catch (PDOException $e) {
        echo "Error creating database: " . $e->getMessage() . "\n";
        return false;
    }
}

// Main installation process
function installDatabase() {
    echo "<h2>Dr.Zia Naturals - Database Setup</h2>\n";
    
    // Check if database exists
    if (!databaseExists(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_NAME)) {
        echo "<p>Database '" . DB_NAME . "' does not exist. Creating...</p>\n";
        if (!createDatabase(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_NAME)) {
            echo "<p style='color: red;'>Failed to create database!</p>\n";
            return false;
        }
        echo "<p style='color: green;'>Database created successfully!</p>\n";
    } else {
        echo "<p>Database '" . DB_NAME . "' already exists.</p>\n";
    }
    
    // Connect to the database
    try {
        global $pdo;
        echo "<p>Connecting to database...</p>\n";
        
        // Test connection
        if (!testConnection()) {
            throw new Exception("Could not connect to database");
        }
        
        echo "<p style='color: green;'>Connected to database successfully!</p>\n";
        
        // Execute SQL file
        $sqlFile = '../database/naturals_db.sql';
        if (file_exists($sqlFile)) {
            echo "<p>Executing SQL file...</p>\n";
            if (executeSQLFile($pdo, $sqlFile)) {
                echo "<p style='color: green;'>Database tables created successfully!</p>\n";
                
                // Insert sample data
                insertSampleData($pdo);
                
                echo "<p style='color: green;'><strong>Database setup completed successfully!</strong></p>\n";
                echo "<p><a href='../index.php'>Go to Website</a></p>\n";
                return true;
            } else {
                echo "<p style='color: red;'>Failed to execute SQL file!</p>\n";
                return false;
            }
        } else {
            echo "<p style='color: red;'>SQL file not found: $sqlFile</p>\n";
            return false;
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>\n";
        return false;
    }
}

// Function to insert sample data
function insertSampleData($pdo) {
    try {
        echo "<p>Inserting sample data...</p>\n";
        
        // Get product IDs
        $products = $pdo->query("SELECT id, product_code FROM products")->fetchAll();
        
        foreach ($products as $product) {
            $productId = $product['id'];
            $productCode = $product['product_code'];
            
            // Insert product volumes
            if ($productCode == 'PROD001') {
                // Hair Growth Serum volumes
                $pdo->prepare("INSERT INTO product_volumes (product_id, size, price, offer, stock_quantity) VALUES (?, ?, ?, ?, ?)")
                    ->execute([$productId, '15 ml bottle', 1299.00, '', 50]);
                $pdo->prepare("INSERT INTO product_volumes (product_id, size, price, offer, stock_quantity) VALUES (?, ?, ?, ?, ?)")
                    ->execute([$productId, '25 ml bottle', 1499.00, 'extra 25%', 30]);
            } else {
                // Face wash volumes
                $pdo->prepare("INSERT INTO product_volumes (product_id, size, price, offer, stock_quantity) VALUES (?, ?, ?, ?, ?)")
                    ->execute([$productId, '100 ml tube', 599.00, '', 100]);
                $pdo->prepare("INSERT INTO product_volumes (product_id, size, price, offer, stock_quantity) VALUES (?, ?, ?, ?, ?)")
                    ->execute([$productId, '150 ml tube', 699.00, 'extra 15%', 75]);
            }
            
            // Insert sample reviews
            $reviews = [
                ['Tomas Doe', 'Customer', 'assets/images/shop/product-details/comment1.webp', 4.5, 'Great product! Noticed results within 2 weeks.'],
                ['Jane Smith', 'Customer', 'assets/images/shop/product-details/comment2.webp', 4.5, 'Excellent quality and fast delivery.'],
                ['Alex Brown', 'Customer', 'assets/images/shop/product-details/comment3.webp', 4.5, 'Highly recommended!']
            ];
            
            foreach ($reviews as $review) {
                $pdo->prepare("INSERT INTO product_reviews (product_id, customer_name, customer_designation, customer_image, rating, comment, status) VALUES (?, ?, ?, ?, ?, ?, 'approved')")
                    ->execute([$productId, $review[0], $review[1], $review[2], $review[3], $review[4]]);
            }
        }
        
        echo "<p style='color: green;'>Sample data inserted successfully!</p>\n";
        
    } catch (Exception $e) {
        echo "<p style='color: orange;'>Warning: Could not insert sample data: " . $e->getMessage() . "</p>\n";
    }
}

// Check if this is being run from command line or web browser
if (php_sapi_name() === 'cli') {
    // Command line execution
    echo "Dr.Zia Naturals - Database Setup\n";
    echo "================================\n";
    installDatabase();
} else {
    // Web browser execution
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Database Setup - Dr.Zia Naturals</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .success { color: green; }
            .error { color: red; }
            .warning { color: orange; }
        </style>
    </head>
    <body>
        <div class="container">
            <?php installDatabase(); ?>
        </div>
    </body>
    </html>
    <?php
}
?>
