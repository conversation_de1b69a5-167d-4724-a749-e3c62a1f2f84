<?php
// Add sample reviews to demonstrate the reviews system
require_once 'config/dbconfig.php';

echo "<h2>🌟 Adding Sample Reviews</h2>";

// First, let's get some products to add reviews for
$products = fetchAll("SELECT id, title, product_code FROM products WHERE status = 'active' LIMIT 5");

if (empty($products)) {
    echo "<p style='color: red;'>❌ No active products found. Please add some products first.</p>";
    exit;
}

echo "<h3>📦 Available Products:</h3>";
echo "<ul>";
foreach ($products as $product) {
    echo "<li><strong>" . htmlspecialchars($product['title']) . "</strong> (Code: " . $product['product_code'] . ")</li>";
}
echo "</ul>";

// Sample reviews data
$sample_reviews = [
    [
        'customer_name' => '<PERSON>',
        'customer_designation' => 'Verified Buyer',
        'rating' => 5.0,
        'comment' => 'Amazing product! I have been using this for 3 months and can see visible results. My hair feels stronger and healthier. Highly recommend to everyone looking for natural hair care solutions.',
        'status' => 'approved'
    ],
    [
        'customer_name' => 'Fatima Khan',
        'customer_designation' => 'Regular Customer',
        'rating' => 4.0,
        'comment' => 'Good quality product. I noticed improvement in my hair texture after using it for 6 weeks. The natural ingredients make it safe for daily use. Will definitely purchase again.',
        'status' => 'approved'
    ],
    [
        'customer_name' => 'Aisha Ali',
        'customer_designation' => 'First Time Buyer',
        'rating' => 5.0,
        'comment' => 'Excellent results! This product exceeded my expectations. My friends have also noticed the positive changes. Dr. Zia Naturals has become my go-to brand for natural products.',
        'status' => 'approved'
    ],
    [
        'customer_name' => 'Zainab Malik',
        'customer_designation' => 'Loyal Customer',
        'rating' => 4.0,
        'comment' => 'Very satisfied with the quality. The packaging is also very professional. I have tried many products but this one actually works. Thank you Dr. Zia Naturals!',
        'status' => 'approved'
    ],
    [
        'customer_name' => 'Mariam Sheikh',
        'customer_designation' => 'Beauty Enthusiast',
        'rating' => 5.0,
        'comment' => 'Outstanding product! I love that it\'s made with natural ingredients. No side effects and great results. I have recommended this to all my family members.',
        'status' => 'approved'
    ],
    [
        'customer_name' => 'Hina Tariq',
        'customer_designation' => 'Verified Buyer',
        'rating' => 3.0,
        'comment' => 'Decent product. Takes time to show results but it does work. I would suggest being patient and consistent with the usage. Overall satisfied with the purchase.',
        'status' => 'pending'
    ]
];

echo "<h3>✨ Adding Sample Reviews:</h3>";

$added_count = 0;
foreach ($products as $index => $product) {
    if ($index < count($sample_reviews)) {
        $review = $sample_reviews[$index];
        
        try {
            // Check if review already exists for this product from this customer
            $existing = fetchSingle("
                SELECT id FROM product_reviews 
                WHERE product_id = ? AND customer_name = ?
            ", [$product['id'], $review['customer_name']]);
            
            if ($existing) {
                echo "<p style='color: orange;'>⚠ Review already exists for " . htmlspecialchars($product['title']) . " by " . htmlspecialchars($review['customer_name']) . "</p>";
                continue;
            }
            
            // Insert the review
            $sql = "INSERT INTO product_reviews (product_id, customer_name, customer_designation, rating, comment, status, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, NOW())";
            
            $review_id = insertData($sql, [
                $product['id'],
                $review['customer_name'],
                $review['customer_designation'],
                $review['rating'],
                $review['comment'],
                $review['status']
            ]);
            
            if ($review_id) {
                echo "<p style='color: green;'>✅ Added review for <strong>" . htmlspecialchars($product['title']) . "</strong> by " . htmlspecialchars($review['customer_name']) . " (Rating: " . $review['rating'] . "/5)</p>";
                $added_count++;
            } else {
                echo "<p style='color: red;'>❌ Failed to add review for " . htmlspecialchars($product['title']) . "</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error adding review for " . htmlspecialchars($product['title']) . ": " . $e->getMessage() . "</p>";
        }
    }
}

// Add a few more reviews for the first product to show multiple reviews
if (!empty($products)) {
    $first_product = $products[0];
    
    $additional_reviews = [
        [
            'customer_name' => 'Ayesha Siddiqui',
            'customer_designation' => 'Happy Customer',
            'rating' => 5.0,
            'comment' => 'Best investment I made for my hair care routine! Visible results within 4 weeks. The natural formula is gentle yet effective.',
            'status' => 'approved'
        ],
        [
            'customer_name' => 'Khadija Iqbal',
            'customer_designation' => 'Repeat Customer',
            'rating' => 4.0,
            'comment' => 'Great product quality. I have been using Dr. Zia products for over a year now and they never disappoint. Consistent results every time.',
            'status' => 'approved'
        ]
    ];
    
    echo "<h4>🔄 Adding Additional Reviews for " . htmlspecialchars($first_product['title']) . ":</h4>";
    
    foreach ($additional_reviews as $review) {
        try {
            // Check if review already exists
            $existing = fetchSingle("
                SELECT id FROM product_reviews 
                WHERE product_id = ? AND customer_name = ?
            ", [$first_product['id'], $review['customer_name']]);
            
            if ($existing) {
                echo "<p style='color: orange;'>⚠ Review already exists by " . htmlspecialchars($review['customer_name']) . "</p>";
                continue;
            }
            
            $sql = "INSERT INTO product_reviews (product_id, customer_name, customer_designation, rating, comment, status, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, NOW())";
            
            $review_id = insertData($sql, [
                $first_product['id'],
                $review['customer_name'],
                $review['customer_designation'],
                $review['rating'],
                $review['comment'],
                $review['status']
            ]);
            
            if ($review_id) {
                echo "<p style='color: green;'>✅ Added additional review by " . htmlspecialchars($review['customer_name']) . " (Rating: " . $review['rating'] . "/5)</p>";
                $added_count++;
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
        }
    }
}

echo "<hr>";
echo "<h3>📊 Summary:</h3>";
echo "<p><strong>Total reviews added:</strong> $added_count</p>";

// Show current review statistics
$stats = [
    'total' => fetchSingle("SELECT COUNT(*) as count FROM product_reviews")['count'] ?? 0,
    'approved' => fetchSingle("SELECT COUNT(*) as count FROM product_reviews WHERE status = 'approved'")['count'] ?? 0,
    'pending' => fetchSingle("SELECT COUNT(*) as count FROM product_reviews WHERE status = 'pending'")['count'] ?? 0,
];

echo "<p><strong>Current review statistics:</strong></p>";
echo "<ul>";
echo "<li>Total Reviews: " . $stats['total'] . "</li>";
echo "<li>Approved Reviews: " . $stats['approved'] . "</li>";
echo "<li>Pending Reviews: " . $stats['pending'] . "</li>";
echo "</ul>";

if ($stats['total'] > 0) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p><strong>🎉 Success! Reviews have been added to your database.</strong></p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ol>";
    echo "<li><a href='admin/reviews.php' style='color: #155724; font-weight: bold;'>🔧 Manage Reviews in Admin Panel</a></li>";
    echo "<li><a href='admin/products.php' style='color: #155724; font-weight: bold;'>📦 View Products with Reviews</a></li>";
    echo "<li><a href='index.php' target='_blank' style='color: #155724; font-weight: bold;'>🏠 Check Reviews on Website</a></li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p><strong>❌ No reviews were added.</strong></p>";
    echo "<p>This might be because reviews already exist or there was an error.</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4 { color: #333; }
h3, h4 { border-bottom: 2px solid #ddd; padding-bottom: 5px; }
p { margin: 5px 0; }
ul, ol { margin: 10px 0; }
hr { margin: 30px 0; border: none; border-top: 2px solid #eee; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
