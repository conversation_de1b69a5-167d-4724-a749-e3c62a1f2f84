<?php
echo "<h2>🔧 Fix Mobile Menu on All Pages</h2>";

// List of all PHP pages that might need mobile menu fixes
$pages_to_check = [
    'product-details.php',
    'cart.php',
    'checkout.php',
    'my-account.php',
    'login.php',
    'signup.php',
    'search.php',
    'hair-care.php',
    'skin-care.php',
    'health-care.php',
    'personal-care.php',
    'contact.php',
    'about-us.php',
    'product-left-sidebar.php',
    'product-four-columns.php',
    'order-success.php'
];

$fixed_count = 0;
$already_good_count = 0;
$error_count = 0;

echo "<h3>📋 Checking Pages for Mobile Menu Issues</h3>";

foreach ($pages_to_check as $page) {
    if (file_exists($page)) {
        echo "<div style='background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>📄 Checking: $page</h4>";
        
        $content = file_get_contents($page);
        $needs_fix = false;
        $fixes_applied = [];
        
        // Check 1: Does it have the mobile menu include?
        $has_offcanvas_include = strpos($content, "include 'includes/offcanvas-menu.php'") !== false ||
                                strpos($content, 'include("includes/offcanvas-menu.php")') !== false;
        
        // Check 2: Does it have the search modal include?
        $has_search_include = strpos($content, "include 'includes/search-modal.php'") !== false ||
                             strpos($content, 'include("includes/search-modal.php")') !== false;
        
        // Check 3: Does it have proper scripts include?
        $has_scripts_include = strpos($content, "include 'includes/scripts.php'") !== false ||
                              strpos($content, 'include("includes/scripts.php")') !== false;
        
        // Check 4: Does it have wrong script paths (./assets/ instead of assets/)?
        $has_wrong_paths = strpos($content, 'src="./assets/') !== false;
        
        echo "<p><strong>Mobile Menu Include:</strong> " . ($has_offcanvas_include ? "✅ Found" : "❌ Missing") . "</p>";
        echo "<p><strong>Search Modal Include:</strong> " . ($has_search_include ? "✅ Found" : "❌ Missing") . "</p>";
        echo "<p><strong>Scripts Include:</strong> " . ($has_scripts_include ? "✅ Found" : "❌ Missing") . "</p>";
        echo "<p><strong>Script Paths:</strong> " . ($has_wrong_paths ? "❌ Wrong paths (./assets/)" : "✅ Correct paths") . "</p>";
        
        // Apply fixes if needed
        $new_content = $content;
        
        // Fix 1: Add mobile menu include if missing
        if (!$has_offcanvas_include && strpos($content, '</body>') !== false) {
            $search_pattern = '</body>';
            $replacement = "    <?php include 'includes/offcanvas-menu.php'; ?>\n    <?php include 'includes/search-modal.php'; ?>\n\n</body>";
            $new_content = str_replace($search_pattern, $replacement, $new_content);
            $fixes_applied[] = "Added mobile menu includes";
            $needs_fix = true;
        }
        
        // Fix 2: Replace individual script tags with scripts include
        if ($has_wrong_paths || !$has_scripts_include) {
            // Pattern to match the script section
            $script_patterns = [
                '/<!-- JS Vendor, Plugins & Activation Script Files -->.*?<script src="\.\/assets\/js\/main\.js"><\/script>/s',
                '/<!-- Vendors JS -->.*?<script src="\.\/assets\/js\/main\.js"><\/script>/s',
                '/<script src="\.\/assets\/js\/vendor\/modernizr.*?<script src="\.\/assets\/js\/main\.js"><\/script>/s'
            ];
            
            foreach ($script_patterns as $pattern) {
                if (preg_match($pattern, $new_content)) {
                    $new_content = preg_replace($pattern, "<?php include 'includes/scripts.php'; ?>", $new_content);
                    $fixes_applied[] = "Replaced script tags with includes";
                    $needs_fix = true;
                    break;
                }
            }
        }
        
        // Apply the fixes
        if ($needs_fix && $new_content !== $content) {
            if (file_put_contents($page, $new_content)) {
                echo "<p style='color: green;'><strong>✅ FIXED!</strong> Applied fixes: " . implode(", ", $fixes_applied) . "</p>";
                $fixed_count++;
            } else {
                echo "<p style='color: red;'><strong>❌ ERROR!</strong> Failed to write changes to $page</p>";
                $error_count++;
            }
        } else if (!$needs_fix) {
            echo "<p style='color: green;'><strong>✅ GOOD!</strong> This page already has proper mobile menu setup</p>";
            $already_good_count++;
        } else {
            echo "<p style='color: orange;'><strong>⚠ WARNING!</strong> Fixes needed but couldn't apply automatically</p>";
            $error_count++;
        }
        
        echo "</div>";
    } else {
        echo "<p style='color: red;'>❌ File not found: $page</p>";
        $error_count++;
    }
}

// Summary
echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>📊 Summary</h3>";
echo "<p><strong>✅ Pages Fixed:</strong> $fixed_count</p>";
echo "<p><strong>✅ Pages Already Good:</strong> $already_good_count</p>";
echo "<p><strong>❌ Errors:</strong> $error_count</p>";
echo "</div>";

if ($fixed_count > 0) {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 Mobile Menu Fixed!</h3>";
    echo "<p><strong>What was fixed:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Added mobile menu includes (offcanvas-menu.php)</li>";
    echo "<li>✅ Added search modal includes (search-modal.php)</li>";
    echo "<li>✅ Fixed JavaScript includes (scripts.php)</li>";
    echo "<li>✅ Fixed script file paths (removed ./)</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h3>🧪 Test Your Mobile Menu</h3>";
echo "<p><strong>To test the mobile menu on all pages:</strong></p>";
echo "<ol>";
echo "<li><strong>Open each page</strong> in a browser</li>";
echo "<li><strong>Resize to mobile view</strong> (or use F12 developer tools)</li>";
echo "<li><strong>Look for hamburger menu</strong> (☰) in top right</li>";
echo "<li><strong>Click hamburger menu</strong> - should slide in from left</li>";
echo "<li><strong>Verify menu items:</strong> Home, About, Hair Care, Skin Care, Health Care, Personal Care, Contact, Account</li>";
echo "</ol>";

echo "<h3>🔗 Quick Test Links</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 20px 0;'>";
foreach ($pages_to_check as $page) {
    if (file_exists($page)) {
        $page_name = ucwords(str_replace(['-', '.php'], [' ', ''], $page));
        echo "<a href='$page' target='_blank' style='background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px; text-align: center;'>📱 Test $page_name</a>";
    }
}
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🔍 If Mobile Menu Still Doesn't Work:</h4>";
echo "<ol>";
echo "<li><strong>Check browser console:</strong> Press F12 → Console tab → Look for JavaScript errors</li>";
echo "<li><strong>Check file paths:</strong> Ensure assets/js/ files exist</li>";
echo "<li><strong>Clear browser cache:</strong> Ctrl+F5 or Ctrl+Shift+R</li>";
echo "<li><strong>Test on different browsers:</strong> Chrome, Firefox, Safari</li>";
echo "</ol>";
echo "</div>";
?>
