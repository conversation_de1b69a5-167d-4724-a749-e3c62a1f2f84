<?php
session_start();

$cart = $_SESSION['cart'] ?? [];
$total = 0;

// Handle order placement
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['place_order'])) {
    // For demo: just clear the cart and show message
    $_SESSION['cart'] = [];
    $order_placed = true;
}
?>

<h2>Checkout</h2>

<?php if (isset($order_placed)): ?>
    <p style="color: green; font-weight: bold;">✅ Thank you! Your order has been placed successfully.</p>
    <a href="index.php"><button>Back to Home</button></a>
<?php elseif (empty($cart)): ?>
    <p>Your cart is empty.</p>
    <a href="product.php"><button>Shop Now</button></a>
<?php else: ?>
    <table border="1" cellpadding="10" cellspacing="0">
        <tr>
            <th>Product</th>
            <th>Image</th>
            <th>Price</th>
            <th>Quantity</th>
            <th>Subtotal</th>
        </tr>

        <?php foreach ($cart as $item): 
            $subtotal = $item['price'] * $item['quantity'];
            $total += $subtotal;
        ?>
            <tr>
                <td><?= htmlspecialchars($item['name']) ?></td>
                <td><img src="<?= htmlspecialchars($item['image']) ?>" width="60"></td>
                <td><?= number_format($item['price'], 2) ?></td>
                <td><?= $item['quantity'] ?></td>
                <td><?= number_format($subtotal, 2) ?></td>
            </tr>
        <?php endforeach; ?>

        <tr>
            <td colspan="4" align="right"><strong>Total:</strong></td>
            <td><strong><?= number_format($total, 2) ?></strong></td>
        </tr>
    </table>

    <form method="POST">
        <br>
        <button type="submit" name="place_order">Place Order</button>
    </form>
<?php endif; ?>
