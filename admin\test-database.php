<?php
require_once '../config/dbconfig.php';

echo "<h2>Database Connection Test</h2>";

// Test database connection
try {
    $connection_test = testConnection();
    if ($connection_test) {
        echo "<p style='color: green;'>✅ Database connection successful!</p>";
    } else {
        echo "<p style='color: red;'>❌ Database connection failed!</p>";
        exit();
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection error: " . $e->getMessage() . "</p>";
    exit();
}

// Check if admin_users table exists
try {
    $tables = fetchAll("SHOW TABLES", []);
    echo "<h3>Available Tables:</h3>";
    echo "<ul>";
    foreach ($tables as $table) {
        $table_name = array_values($table)[0];
        echo "<li>$table_name</li>";
    }
    echo "</ul>";
    
    // Check if admin_users table exists
    $admin_table_exists = false;
    foreach ($tables as $table) {
        if (in_array('admin_users', array_values($table))) {
            $admin_table_exists = true;
            break;
        }
    }
    
    if ($admin_table_exists) {
        echo "<p style='color: green;'>✅ admin_users table exists</p>";
        
        // Get all admin users
        $admins = fetchAll("SELECT * FROM admin_users", []);
        echo "<h3>Admin Users in Database:</h3>";
        
        if (empty($admins)) {
            echo "<p style='color: orange;'>⚠️ No admin users found in database!</p>";
            echo "<p><a href='fix-admin-password.php'>Click here to create admin user</a></p>";
        } else {
            echo "<table border='1' cellpadding='5' cellspacing='0'>";
            echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Full Name</th><th>Role</th><th>Status</th><th>Password Hash</th></tr>";
            foreach ($admins as $admin) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($admin['id']) . "</td>";
                echo "<td>" . htmlspecialchars($admin['username']) . "</td>";
                echo "<td>" . htmlspecialchars($admin['email']) . "</td>";
                echo "<td>" . htmlspecialchars($admin['full_name']) . "</td>";
                echo "<td>" . htmlspecialchars($admin['role']) . "</td>";
                echo "<td>" . htmlspecialchars($admin['status']) . "</td>";
                echo "<td style='font-family: monospace; font-size: 10px;'>" . htmlspecialchars(substr($admin['password'], 0, 30)) . "...</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Test password for admin user
            $admin_user = fetchSingle("SELECT * FROM admin_users WHERE username = 'admin'", []);
            if ($admin_user) {
                echo "<h3>Password Test for 'admin' user:</h3>";
                $test_password = 'admin123';
                $verify_result = password_verify($test_password, $admin_user['password']);
                echo "<p>Testing password '$test_password': " . ($verify_result ? '<span style="color: green;">✅ CORRECT</span>' : '<span style="color: red;">❌ INCORRECT</span>') . "</p>";
                
                if (!$verify_result) {
                    echo "<p style='color: orange;'>⚠️ Password verification failed. <a href='fix-admin-password.php'>Click here to fix the password</a></p>";
                }
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ admin_users table does not exist!</p>";
        echo "<p>Please run the database setup script: <a href='../install/setup_database.php'>Setup Database</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking tables: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='login-debug.php'>← Back to Debug Login</a></p>";
echo "<p><a href='login.php'>← Back to Normal Login</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
