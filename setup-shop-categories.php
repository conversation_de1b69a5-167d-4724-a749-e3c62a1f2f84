<?php
// Setup script to populate categories and link products to categories
require_once 'config/dbconfig.php';

try {
    $pdo->beginTransaction();
    
    // Insert categories if they don't exist
    $categories = [
        ['Hair Care', 'Products for hair growth, hair fall control, and overall hair health'],
        ['Skin Care', 'Facial cleansers, moisturizers, and skincare products for healthy skin'],
        ['Anti-Acne', 'Specialized products for acne treatment and prevention'],
        ['Health Care', 'Natural health and wellness products for overall well-being'],
        ['Natural Products', 'All natural and organic products without harmful chemicals']
    ];
    
    $category_ids = [];
    
    foreach ($categories as $cat) {
        // Check if category exists
        $existing = fetchSingle("SELECT id FROM categories WHERE name = ?", [$cat[0]]);
        
        if ($existing) {
            $category_ids[$cat[0]] = $existing['id'];
            echo "Category '{$cat[0]}' already exists with ID: {$existing['id']}\n";
        } else {
            // Insert new category
            $category_id = insertData("
                INSERT INTO categories (name, description, status) 
                VALUES (?, ?, 'active')
            ", [$cat[0], $cat[1]]);
            
            $category_ids[$cat[0]] = $category_id;
            echo "Created category '{$cat[0]}' with ID: {$category_id}\n";
        }
    }
    
    // Get all products
    $products = fetchAll("SELECT id, product_code, title FROM products WHERE status = 'active'");
    
    // Link products to categories based on their titles/descriptions
    foreach ($products as $product) {
        $title_lower = strtolower($product['title']);
        $assigned_categories = [];
        
        // Determine categories based on product title
        if (strpos($title_lower, 'hair') !== false || strpos($title_lower, 'serum') !== false) {
            $assigned_categories[] = $category_ids['Hair Care'];
        }
        
        if (strpos($title_lower, 'face') !== false || strpos($title_lower, 'facial') !== false || 
            strpos($title_lower, 'skin') !== false || strpos($title_lower, 'moisturizer') !== false) {
            $assigned_categories[] = $category_ids['Skin Care'];
        }
        
        if (strpos($title_lower, 'acne') !== false || strpos($title_lower, 'anti-acne') !== false) {
            $assigned_categories[] = $category_ids['Anti-Acne'];
        }
        
        if (strpos($title_lower, 'health') !== false || strpos($title_lower, 'wellness') !== false || 
            strpos($title_lower, 'supplement') !== false) {
            $assigned_categories[] = $category_ids['Health Care'];
        }
        
        // All products are natural
        $assigned_categories[] = $category_ids['Natural Products'];
        
        // Remove duplicates
        $assigned_categories = array_unique($assigned_categories);
        
        // Insert product-category relationships
        foreach ($assigned_categories as $cat_id) {
            // Check if relationship already exists
            $existing_rel = fetchSingle("
                SELECT id FROM product_categories 
                WHERE product_id = ? AND category_id = ?
            ", [$product['id'], $cat_id]);
            
            if (!$existing_rel) {
                executeQuery("
                    INSERT INTO product_categories (product_id, category_id) 
                    VALUES (?, ?)
                ", [$product['id'], $cat_id]);
                
                $cat_name = array_search($cat_id, $category_ids);
                echo "Linked product '{$product['title']}' to category '{$cat_name}'\n";
            }
        }
    }
    
    $pdo->commit();
    echo "\nSetup completed successfully!\n";
    echo "Categories created and products linked to appropriate categories.\n";
    
} catch (Exception $e) {
    $pdo->rollBack();
    echo "Error: " . $e->getMessage() . "\n";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Shop Categories Setup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; }
        .error { color: red; }
        pre { background: #f5f5f5; padding: 20px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Shop Categories Setup</h1>
    <p>This script sets up categories and links products to appropriate categories.</p>
    
    <h2>Setup Results:</h2>
    <pre>
<?php
// The PHP output will be displayed here
?>
    </pre>
    
    <h2>Next Steps:</h2>
    <ol>
        <li>Visit the category pages to see products:
            <ul>
                <li><a href="hair-care.php">Hair Care Products</a></li>
                <li><a href="skin-care.php">Skin Care Products</a></li>
                <li><a href="health-care.php">Health Care Products</a></li>
            </ul>
        </li>
        <li>Use the <a href="admin/products.php">Admin Panel</a> to manage products and categories</li>
        <li>Test the category filters and search functionality</li>
    </ol>

    <div style="margin-top: 30px;">
        <a href="hair-care.php" class="btn" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            View Hair Care
        </a>
        <a href="skin-care.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
            View Skin Care
        </a>
        <a href="admin/products.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
            Admin Panel
        </a>
    </div>
</body>
</html>
