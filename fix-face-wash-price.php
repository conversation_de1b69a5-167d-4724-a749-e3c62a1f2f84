<?php
require_once 'config/dbconfig.php';

echo "<h2>🔧 Fix Face Wash Price</h2>";

if ($_POST) {
    $product_id = intval($_POST['product_id']);
    $sale_price = floatval($_POST['sale_price']);
    
    try {
        executeQuery("UPDATE products SET sale_price = ? WHERE id = ?", [$sale_price, $product_id]);
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>✅ Success!</h4>";
        echo "<p>Face Wash sale price updated to Rs. " . number_format($sale_price, 2) . "</p>";
        echo "</div>";
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>❌ Error!</h4>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
    }
}

try {
    // Get Face Wash products
    $face_wash_products = fetchAll("
        SELECT id, title, price, sale_price
        FROM products 
        WHERE (title LIKE '%face%' OR title LIKE '%Face%' OR title LIKE '%wash%' OR title LIKE '%Wash%')
        AND status = 'active'
        ORDER BY title ASC
    ");
    
    echo "<h3>📊 Face Wash Products</h3>";
    
    if ($face_wash_products) {
        foreach ($face_wash_products as $product) {
            echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 15px 0; border-radius: 5px;'>";
            echo "<h4>" . htmlspecialchars($product['title']) . "</h4>";
            echo "<p><strong>Current Price:</strong> Rs. " . number_format($product['price'], 2) . "</p>";
            echo "<p><strong>Current Sale Price:</strong> " . ($product['sale_price'] ? 'Rs. ' . number_format($product['sale_price'], 2) : '<span style="color: red;">NOT SET</span>') . "</p>";
            
            if (!$product['sale_price'] || $product['sale_price'] >= $product['price']) {
                echo "<form method='POST' style='margin-top: 15px;'>";
                echo "<input type='hidden' name='product_id' value='" . $product['id'] . "'>";
                echo "<label for='sale_price_" . $product['id'] . "'>Set Sale Price (Rs.):</label><br>";
                echo "<input type='number' step='0.01' name='sale_price' id='sale_price_" . $product['id'] . "' value='495' style='padding: 5px; margin: 5px 0;'><br>";
                echo "<button type='submit' style='background: #007bff; color: white; padding: 8px 15px; border: none; border-radius: 3px; cursor: pointer;'>Update Sale Price</button>";
                echo "</form>";
            } else {
                echo "<p style='color: green;'>✅ Sale price is already set correctly!</p>";
            }
            echo "</div>";
        }
    } else {
        echo "<p style='color: red;'>❌ No Face Wash products found!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Fix Face Wash Price</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h2, h3, h4 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
        input[type="number"] { width: 150px; }
    </style>
</head>
<body>

<div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <h4>📋 Steps to Fix Pricing Issue:</h4>
    <ol>
        <li><strong>Set sale price above</strong> (suggested: Rs. 495)</li>
        <li><strong>Clear your cart:</strong> <a href="clear-cart-and-redirect.php">Clear Cart</a></li>
        <li><strong>Add Face Wash to cart again</strong> from product page</li>
        <li><strong>Check cart.php</strong> - should show Rs. 495</li>
    </ol>
</div>

<div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <h4>⚠️ Important Notes:</h4>
    <ul>
        <li><strong>Sale price must be LOWER than original price</strong> to show as discount</li>
        <li><strong>Clear cart after updating prices</strong> - old cart items have cached prices</li>
        <li><strong>Use cart.php, not product-cart.php</strong> - the header links to cart.php</li>
    </ul>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="debug-face-wash-pricing.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🔍 Debug Pricing</a>
    <a href="cart.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🛒 View Cart</a>
    <a href="clear-cart-and-redirect.php" style="background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🗑️ Clear Cart</a>
    <a href="admin/products.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">⚙️ Admin Panel</a>
</div>

</body>
</html>
