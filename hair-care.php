<?php
require_once 'config/dbconfig.php';
include 'header.php';
?>

<!-- FontAwesome CDN for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<?php

// Get hair care products
$hair_care_query = "
    SELECT DISTINCT p.*,
           GROUP_CONCAT(c.name SEPARATOR ', ') as categories,
           COALESCE(p.sale_price, p.price) as display_price,
           CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price
                THEN p.price ELSE NULL END as original_price
    FROM products p
    LEFT JOIN product_categories pc ON p.id = pc.product_id
    LEFT JOIN categories c ON pc.category_id = c.id
    WHERE p.status = 'active'
    AND (c.name = 'Hair Care'
         OR p.title LIKE '%hair%'
         OR p.title LIKE '%Hair%'
         OR p.title LIKE '%shampoo%'
         OR p.title LIKE '%Shampoo%'
         OR p.title LIKE '%conditioner%'
         OR p.title LIKE '%Conditioner%'
         OR p.title LIKE '%oil%'
         OR p.title LIKE '%Oil%'
         OR p.title LIKE '%serum%'
         OR p.title LIKE '%Serum%')
    GROUP BY p.id
    ORDER BY p.created_at ASC
";

$hair_products = fetchAll($hair_care_query);
?>
<br><br><br><br>
<!-- Hair Care Hero Banner -->
<section class="hair-hero-banner position-relative">
    <div class="container">
        <div class="row align-items-center py-5">
            <div class="col-lg-6">
                <div class="hair-hero-content">
                    <div class="hero-badge mb-3">
                        <span class="badge bg-primary fs-6 px-3 py-2 rounded-pill">
                            <i class="fas fa-leaf me-2"></i>Natural Hair Solutions
                        </span>
                    </div>
                    <h1 class="display-4 fw-bold text-dark mb-4">
                        Beautiful Hair, <br>
                        <span class="text-primary">Naturally Nourished</span>
                    </h1>
                    <p class="lead text-muted mb-4">
                        Transform your hair with our premium collection of natural hair care products, including nourishing oils,
                        gentle shampoos, and revitalizing treatments for all hair types.
                    </p>
                    <div class="hero-features mb-4">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="feature-item d-flex align-items-center">
                                    <i class="fas fa-leaf text-primary me-3 fs-5"></i>
                                    <span class="text-dark">100% Natural Ingredients</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item d-flex align-items-center">
                                    <i class="fas fa-shield-alt text-warning me-3 fs-5"></i>
                                    <span class="text-dark">Chemical Free Formula</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item d-flex align-items-center">
                                    <i class="fas fa-sparkles text-success me-3 fs-5"></i>
                                    <span class="text-dark">Strengthens & Nourishes</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item d-flex align-items-center">
                                    <i class="fas fa-heart text-danger me-3 fs-5"></i>
                                    <span class="text-dark">Loved by Customers</span>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="col-lg-6">
                <div class="hair-hero-image text-center">
                    <img src="assets/images/hair_img.png" alt="Hair Care Products"
                         class="img-fluid rounded-3 shadow" style="max-width: 600px; height: auto;">
                </div>
            </div>
        </div>
    </div>
</section>
<br><br>
<!-- Hair Products Section -->
<section id="hair-products" class="shop-section section section-padding bg-light">
    <div class="container">
        <!-- Section Header -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <div class="section-header">
                    <span class="badge bg-primary fs-6 px-3 py-2 rounded-pill mb-3">
                        <i class="fas fa-leaf me-2"></i>Hair Care Collection
                    </span>
                    <h2 class="display-5 fw-bold text-dark mb-3">
                        <i class="fas fa-magic text-primary me-3"></i>Natural Hair Solutions
                    </h2>
                    <p class="lead text-muted mx-auto" style="max-width: 600px;">
                        Explore our carefully curated selection of natural hair oils, gentle shampoos,
                        and nourishing treatments designed to give you healthy, beautiful hair.
                    </p>
                </div>
            </div>
        </div>

        <!-- Breadcrumb -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-white rounded-3 p-3 shadow-sm">
                        <li class="breadcrumb-item">
                            <a href="index.php" class="text-decoration-none">
                                <i class="fas fa-home me-1"></i>Home
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-leaf me-1"></i>Hair Care
                        </li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row">
            <?php if (empty($hair_products)): ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <h4>No Hair Care Products Available</h4>
                        <p>We're working on adding more products. Please check back soon!</p>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($hair_products as $product):
                    $review_count = rand(5, 25);
                ?>
                    <div class="col-6 col-lg-4 mb-4 mb-sm-9">
                        <!--== Start Product Item ==-->
                        <div class="product-item">
                            <div class="product-thumb">
                                <a class="d-block" href="product-details.php?product=<?php echo htmlspecialchars($product['slug'] ?: $product['product_code']); ?>">
                                    <img src="<?php echo htmlspecialchars($product['image'] ?: 'assets/images/shop/default.png'); ?>"
                                         width="370" height="450" alt="<?php echo htmlspecialchars($product['title']); ?>">
                                </a>
                                <span class="flag-new">new</span>
                                <div class="product-action">
                                    <button type="button" class="product-action-btn action-btn-cart"
                                            onclick="addToCart(<?php echo $product['id']; ?>)">
                                        <span>Add to cart</span>
                                    </button>
                                </div>
                            </div>
                            <div class="product-info">
                                <div class="product-rating">
                                    <div class="rating">
                                        <?php
                                        // Get actual average rating for this product
                                        $product_reviews = fetchAll("SELECT rating FROM product_reviews WHERE product_id = ? AND status = 'approved'", [$product['id']]);
                                        $avg_rating = !empty($product_reviews) ? array_sum(array_column($product_reviews, 'rating')) / count($product_reviews) : 5;
                                        $review_count = count($product_reviews);

                                        // Show all 5 stars, but highlight only the rating amount
                                        for ($i = 1; $i <= 5; $i++):
                                            if ($i <= $avg_rating): ?>
                                                <i class="fa fa-star" style="color: #ffc107;"></i>
                                            <?php else: ?>
                                                <i class="fa fa-star" style="color: #ddd;"></i>
                                            <?php endif;
                                        endfor; ?>
                                    </div>
                                    <div class="reviews text-muted small"><?php echo $review_count; ?> review<?php echo $review_count != 1 ? 's' : ''; ?></div>
                                </div>
                                <h4 class="title">
                                    <a href="product-details.php?product=<?php echo htmlspecialchars($product['slug'] ?: $product['product_code']); ?>">
                                        <?php echo htmlspecialchars($product['title']); ?>
                                    </a>
                                </h4>
                                <div class="prices">
                                    <?php if ($product['original_price']): ?>
                                        <span class="price-old">Rs. <?php echo number_format($product['original_price'], 0); ?></span>
                                        <span class="price">Rs. <?php echo number_format($product['display_price'], 0); ?></span>
                                    <?php else: ?>
                                        <span class="price">Rs. <?php echo number_format($product['display_price'], 0); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="product-action-bottom">
                                <button type="button" class="product-action-btn action-btn-quick-view"
                                        data-bs-toggle="modal" data-bs-target="#action-QuickViewModal"
                                        data-product-id="<?php echo $product['id']; ?>">
                                    <i class="fa fa-expand"></i>
                                </button>
                                <button type="button" class="product-action-btn action-btn-wishlist"
                                        data-bs-toggle="modal" data-bs-target="#action-WishlistModal">
                                    <i class="fa fa-heart-o"></i>
                                </button>
                                <button type="button" class="product-action-btn action-btn-cart"
                                        onclick="addToCart(<?php echo $product['id']; ?>)">
                                    <span>Add to cart</span>
                                </button>
                            </div>
                        </div>
                        <!--== End Product Item ==-->
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</section>

<?php include 'includes/search-modal.php'; ?>
<?php include 'includes/offcanvas-menu.php'; ?>

<?php include 'footer.php'; ?>

<!-- Hair Care Page Custom Styles -->
<style>
.hair-hero-banner {
    background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
    padding: 2rem 0;
}

.hair-hero-content {
    padding: 1rem 0;
}

.hair-hero-image img {
    /* Hover effect removed */
}

.hero-badge .badge {
    font-weight: 500;
    letter-spacing: 0.5px;
}

.feature-item {
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateX(5px);
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #007bff);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

.btn-outline-primary {
    border: 2px solid #007bff;
    color: #007bff;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background: #007bff;
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

.section-header .badge {
    font-weight: 500;
    letter-spacing: 0.5px;
}

.shop-header {
    border-left: 4px solid #007bff;
}

.stat-number {
    font-size: 1.5rem;
}

.product-item {
    transition: all 0.3s ease;
    border-radius: 15px;
    overflow: hidden;
    border: 1px solid transparent;
}

.product-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.product-item .product-info {
    padding: 15px;
}

.product-item .product-action-bottom {
    padding: 0 15px 15px 15px;
}

.breadcrumb {
    border-left: 4px solid #007bff;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #007bff;
    font-weight: bold;
}

/* Smooth scrolling */
.smooth-scroll {
    scroll-behavior: smooth;
}

/* Sale Price Styling for Product Cards */
.prices {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.price-old {
    color: #999;
    text-decoration: line-through;
    font-size: 14px;
}

.price {
    color: #e74c3c;
    font-weight: 600;
}

.sale-badge {
    background: #e74c3c;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
    letter-spacing: 0.5px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hair-hero-content {
        padding: 1rem 0;
        text-align: center;
    }

    .hair-hero-image {
        margin-top: 2rem;
    }

    .hair-hero-image img {
        max-width: 400px !important;
    }

    .display-4 {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .hair-stats {
        flex-direction: column;
        gap: 1rem !important;
    }

    .stat-item {
        text-align: left !important;
    }
}
</style>

<!-- Hair Care Page JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add animation on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe product items
    document.querySelectorAll('.product-item').forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        item.style.transition = 'all 0.6s ease';
        observer.observe(item);
    });
});

// Add to cart function for hair products
function addToCart(productId) {
    // Show loading state
    const cartButtons = document.querySelectorAll(`[onclick="addToCart(${productId})"]`);
    cartButtons.forEach(btn => {
        btn.disabled = true;
        btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Adding...';
    });

    // Send AJAX request
    fetch('cart/add-to-cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: 1
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update cart count in header
            const cartCount = document.querySelector('.cart-count');
            if (cartCount) {
                cartCount.textContent = data.cart_count;
                cartCount.style.display = data.cart_count > 0 ? 'inline-block' : 'none';
            }

            // Show success message
            cartButtons.forEach(btn => {
                btn.innerHTML = '<i class="fa fa-check"></i> Added!';
                btn.classList.add('btn-success');
                btn.classList.remove('btn-primary');
            });

            // Reset button after 2 seconds
            setTimeout(() => {
                cartButtons.forEach(btn => {
                    btn.disabled = false;
                    btn.innerHTML = '<span>Add to cart</span>';
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-primary');
                });
            }, 2000);

            // Show toast notification
            showToast('Product added to cart successfully!', 'success');
        } else {
            // Show error message
            cartButtons.forEach(btn => {
                btn.disabled = false;
                btn.innerHTML = '<span>Add to cart</span>';
            });
            showToast(data.message || 'Failed to add product to cart', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        cartButtons.forEach(btn => {
            btn.disabled = false;
            btn.innerHTML = '<span>Add to cart</span>';
        });
        showToast('An error occurred. Please try again.', 'error');
    });
}

// Toast notification function
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    // Add to page
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    toastContainer.appendChild(toast);

    // Show toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Remove from DOM after hiding
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}
</script>

<?php include 'includes/scripts.php'; ?>