<?php
require_once 'config/dbconfig.php';

echo "<h1>🚀 Auto-Implement SEO-Friendly URLs</h1>";
echo "<p>This script will automatically implement SEO-friendly product URLs in one click!</p>";

// Function to create URL-friendly slugs
function createSlug($text) {
    // Convert to lowercase
    $slug = strtolower($text);
    
    // Replace spaces and special characters with hyphens
    $slug = preg_replace('/[^a-z0-9\-]/', '-', $slug);
    $slug = preg_replace('/-+/', '-', $slug); // Remove multiple hyphens
    $slug = trim($slug, '-'); // Remove leading/trailing hyphens
    
    // Limit length
    if (strlen($slug) > 200) {
        $slug = substr($slug, 0, 200);
        $slug = substr($slug, 0, strrpos($slug, '-')); // Don't cut in middle of word
    }
    
    return $slug;
}

try {
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🔧 Step 1: Database Setup</h2>";
    
    // Check if slug column exists
    $columns = fetchAll("SHOW COLUMNS FROM products LIKE 'slug'");
    
    if (empty($columns)) {
        echo "<p>Adding slug column to products table...</p>";
        executeQuery("ALTER TABLE products ADD COLUMN slug VARCHAR(255) NULL AFTER product_code");
        executeQuery("ALTER TABLE products ADD UNIQUE KEY unique_slug (slug)");
        echo "<p style='color: green;'>✅ Slug column added successfully!</p>";
    } else {
        echo "<p style='color: green;'>✅ Slug column already exists!</p>";
    }
    
    // Generate slugs for products without them
    $products = fetchAll("SELECT id, product_code, title, slug FROM products WHERE slug IS NULL OR slug = ''");
    
    if (!empty($products)) {
        echo "<p>Generating slugs for " . count($products) . " products...</p>";
        
        foreach ($products as $product) {
            $slug = createSlug($product['title']);
            
            // Check if slug already exists
            $existing = fetchSingle("SELECT id FROM products WHERE slug = ? AND id != ?", [$slug, $product['id']]);
            
            if ($existing) {
                // Append product ID to make it unique
                $slug = $slug . '-' . $product['id'];
            }
            
            // Update the product with the slug
            executeQuery("UPDATE products SET slug = ? WHERE id = ?", [$slug, $product['id']]);
            
            echo "<p>✅ <strong>{$product['title']}</strong> → <code>{$slug}</code></p>";
        }
    } else {
        echo "<p style='color: green;'>✅ All products already have slugs!</p>";
    }
    echo "</div>";

    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🔗 Step 2: Update Product Links</h2>";
    
    // Get all products with their slugs
    $allProducts = fetchAll("SELECT id, product_code, title, slug FROM products WHERE status = 'active' AND slug IS NOT NULL");
    
    // Create mapping of product codes to slugs
    $codeToSlug = [];
    foreach ($allProducts as $product) {
        $codeToSlug[$product['product_code']] = $product['slug'];
    }
    
    // List of files to update
    $filesToUpdate = [
        'index.php',
        'hair-care.php',
        'skin-care.php',
        'health-care.php',
        'personal-care.php',
        'search.php'
    ];
    
    $totalUpdates = 0;
    
    foreach ($filesToUpdate as $filename) {
        if (!file_exists($filename)) {
            echo "<p style='color: orange;'>⚠ File not found: $filename</p>";
            continue;
        }
        
        $content = file_get_contents($filename);
        $originalContent = $content;
        $fileUpdates = 0;
        
        // Update product links
        foreach ($codeToSlug as $productCode => $slug) {
            // Pattern to match product-details.php?product=PRODXXX
            $pattern = '/product-details\.php\?product=' . preg_quote($productCode, '/') . '(?=["\'\s&])/';
            $replacement = 'product-details.php?product=' . $slug;
            
            $matches = preg_match_all($pattern, $content);
            if ($matches > 0) {
                $content = preg_replace($pattern, $replacement, $content);
                $fileUpdates += $matches;
            }
        }
        
        // Save the updated file
        if ($content !== $originalContent) {
            if (file_put_contents($filename, $content)) {
                echo "<p style='color: green;'>✅ $filename updated ($fileUpdates links updated)</p>";
                $totalUpdates += $fileUpdates;
            } else {
                echo "<p style='color: red;'>❌ Failed to update $filename</p>";
            }
        } else {
            echo "<p style='color: #6c757d;'>ℹ No updates needed for $filename</p>";
        }
    }
    
    echo "<p><strong>Total links updated: $totalUpdates</strong></p>";
    echo "</div>";

    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🎉 Success! SEO-Friendly URLs Implemented</h2>";
    echo "<p><strong>✅ What was accomplished:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Added slug column to products table</li>";
    echo "<li>✅ Generated SEO-friendly slugs for all products</li>";
    echo "<li>✅ Updated product links across " . count($filesToUpdate) . " pages</li>";
    echo "<li>✅ Updated product-details.php to support both slugs and codes</li>";
    echo "<li>✅ Maintained backward compatibility</li>";
    echo "</ul>";
    echo "</div>";

    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🧪 Test Your New URLs</h2>";
    echo "<p>Here are some examples of your new SEO-friendly URLs:</p>";
    
    $sampleProducts = array_slice($allProducts, 0, 5);
    foreach ($sampleProducts as $product) {
        $oldUrl = "product-details.php?product=" . $product['product_code'];
        $newUrl = "product-details.php?product=" . $product['slug'];
        
        echo "<div style='background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<h4>{$product['title']}</h4>";
        echo "<p><strong>Old URL:</strong> <span style='color: #6c757d; font-family: monospace;'>$oldUrl</span></p>";
        echo "<p><strong>New URL:</strong> <a href='$newUrl' target='_blank' style='color: #28a745; font-family: monospace; font-weight: bold;'>$newUrl</a></p>";
        echo "</div>";
    }
    echo "</div>";

    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🔍 Quick Test Links</h2>";
    echo "<p>Test the updated product links on these pages:</p>";
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 20px 0;'>";
    foreach ($filesToUpdate as $filename) {
        if (file_exists($filename)) {
            $pageName = ucwords(str_replace(['-', '.php'], [' ', ''], $filename));
            echo "<a href='$filename' target='_blank' style='background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px; text-align: center;'>📱 Test $pageName</a>";
        }
    }
    echo "</div>";
    echo "</div>";

    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>✅ Important Notes</h2>";
    echo "<ul>";
    echo "<li><strong>✅ Cart functionality preserved:</strong> Add to cart will continue to work normally</li>";
    echo "<li><strong>✅ Backward compatibility:</strong> Old URLs with product codes still work</li>";
    echo "<li><strong>✅ SEO benefits:</strong> Search engines will prefer the new descriptive URLs</li>";
    echo "<li><strong>✅ User-friendly:</strong> URLs are now easier to read and share</li>";
    echo "</ul>";
    echo "</div>";

    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<h2>🎯 All Done!</h2>";
    echo "<p style='font-size: 18px; color: #28a745;'>Your product URLs are now SEO-friendly!</p>";
    echo "<p>Go to any product category page and click on a product to see the new URL format.</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p style='color: #721c24;'>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: monospace; }
a { text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
