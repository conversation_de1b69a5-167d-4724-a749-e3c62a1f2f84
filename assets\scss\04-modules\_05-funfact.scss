/*----------------------------------------*/
/*  Funfact CSS
/*----------------------------------------*/

.funfact-area {
  background-color: #FFF6F5;
  border-radius: 30px;
  max-width: 1703px;
  margin: 0 auto;
}

.funfact-item {
  background-color: #FFF6F5;
  border: 3px solid #F27E75;
  border-radius: 30px;
  text-align: center;
  padding: 22px 44px 30px;
  @media #{$desktop-device, $tablet-device, $large-mobile} {
    padding: 22px 44px 28px;
  }

  .icon {
    border-bottom: 1px solid #FFB0B0;
    padding-bottom: 12px;
    margin-bottom: 30px;
    @media #{$desktop-device, $tablet-device, $large-mobile} {
      margin-bottom: 15px;
    }
    img {
      width: 80px;
      @media #{$desktop-device, $tablet-device, $large-mobile} {
        width: 70px;
      }
    }
  }

  .funfact-number {
    color: #364958;
    font-size: 50px;
    font-weight: $font-weight-medium;
    margin-bottom: 4px;
    @media #{$desktop-device, $tablet-device, $large-mobile} {
      font-size: 38px;
    }
  }

  .funfact-title {
    color: #231942;
    font-size: 21px;
    font-weight: $font-weight-normal;
    margin-bottom: 0;
    text-transform: uppercase;
    @media #{$desktop-device, $tablet-device, $large-mobile} {
      font-size: 15px;
    }
  }
}