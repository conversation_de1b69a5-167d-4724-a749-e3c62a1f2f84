<!-- J<PERSON> Vendor, Plugins & Activation Script Files -->

<!-- Vendors JS -->
<script src="assets/js/vendor/modernizr-3.11.7.min.js"></script>
<script src="assets/js/vendor/jquery-3.6.0.min.js"></script>
<script src="assets/js/vendor/jquery-migrate-3.3.2.min.js"></script>
<script src="assets/js/vendor/bootstrap.bundle.min.js"></script>

<!-- Plugins JS -->
<script src="assets/js/plugins/swiper-bundle.min.js"></script>
<script src="assets/js/plugins/fancybox.min.js"></script>
<script src="assets/js/plugins/jquery.nice-select.min.js"></script>

<!-- Custom Main JS -->
<script src="assets/js/main.js"></script>

<script>
// Function to show cart notifications
function showCartNotification(message, type = 'success') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.cart-notification');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `cart-notification alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
    notification.innerHTML = `
        <i class="fa fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// Function to update cart count
function updateCartCount(count) {
    const cartCountElement = document.querySelector('.cart-count');

    if (count > 0) {
        if (cartCountElement) {
            cartCountElement.textContent = count;
            cartCountElement.style.display = 'inline-block';
        } else {
            // Create cart count badge if it doesn't exist
            const cartButton = document.querySelector('a[href="cart.php"]');
            if (cartButton) {
                const badge = document.createElement('span');
                badge.className = 'cart-count badge bg-danger position-absolute top-0 start-100 translate-middle rounded-pill';
                badge.textContent = count;
                cartButton.appendChild(badge);
            }
        }
    } else {
        if (cartCountElement) {
            cartCountElement.style.display = 'none';
        }
    }
}

// Add to cart function
function addToCart(productId) {
    // Show loading state
    const cartButtons = document.querySelectorAll(`[onclick="addToCart(${productId})"]`);
    cartButtons.forEach(btn => {
        btn.disabled = true;
        btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Adding...';
    });

    // Send AJAX request
    fetch('cart/add-to-cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: 1
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success notification
            showCartNotification('Product added to cart successfully!', 'success');

            // Update cart count
            updateCartCount(data.cart_count);
        } else {
            showCartNotification('Error adding to cart: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showCartNotification('Error adding to cart. Please try again.', 'error');
    })
    .finally(() => {
        // Reset button state
        cartButtons.forEach(btn => {
            btn.disabled = false;
            btn.innerHTML = '<span>Add to cart</span>';
        });
    });
}

// Load cart count on page load
document.addEventListener('DOMContentLoaded', function() {
    fetch('cart/get-cart-count.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateCartCount(data.cart_count);
            }
        })
        .catch(error => console.log('Error loading cart count:', error));
});
</script>
