<?php
// Check Product Images - Diagnostic Tool
require_once 'config/dbconfig.php';

echo "<h1>🔍 Product Images Diagnostic</h1>";

// Get all products with their image info
$products = fetchAll("SELECT id, product_code, title, image, slug FROM products WHERE status = 'active' ORDER BY id");

echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .product-card { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    .success { background: #d4edda; border-color: #c3e6cb; }
    .error { background: #f8d7da; border-color: #f5c6cb; }
    .warning { background: #fff3cd; border-color: #ffeaa7; }
    img { max-width: 150px; height: auto; border: 1px solid #ddd; }
    .image-info { font-family: monospace; font-size: 12px; background: #f8f9fa; padding: 5px; margin: 5px 0; }
</style>";

foreach ($products as $product) {
    $hasImage = !empty($product['image']);
    $imageExists = false;
    $imageUrl = '';
    
    if ($hasImage) {
        $imageUrl = $product['image'];
        $imageExists = file_exists($imageUrl);
    }
    
    // Determine card class
    $cardClass = 'product-card ';
    if ($hasImage && $imageExists) {
        $cardClass .= 'success';
        $status = '✅ Image OK';
    } elseif ($hasImage && !$imageExists) {
        $cardClass .= 'error';
        $status = '❌ Image Missing';
    } else {
        $cardClass .= 'warning';
        $status = '⚠️ No Image Set';
    }
    
    echo "<div class='{$cardClass}'>";
    echo "<h3>{$status} - " . htmlspecialchars($product['title']) . "</h3>";
    echo "<p><strong>Product Code:</strong> " . htmlspecialchars($product['product_code']) . "</p>";
    echo "<p><strong>Slug:</strong> " . htmlspecialchars($product['slug'] ?: 'No slug') . "</p>";
    
    if ($hasImage) {
        echo "<div class='image-info'><strong>Database Image Path:</strong> " . htmlspecialchars($imageUrl) . "</div>";
        echo "<div class='image-info'><strong>Full Server Path:</strong> " . realpath($imageUrl ?: '') . "</div>";
        echo "<div class='image-info'><strong>File Exists:</strong> " . ($imageExists ? 'YES' : 'NO') . "</div>";
        
        if ($imageExists) {
            echo "<div class='image-info'><strong>File Size:</strong> " . number_format(filesize($imageUrl)) . " bytes</div>";
            echo "<p><strong>Preview:</strong></p>";
            echo "<img src='{$imageUrl}' alt='" . htmlspecialchars($product['title']) . "'>";
        } else {
            echo "<p style='color: red;'><strong>❌ Image file not found at:</strong> {$imageUrl}</p>";
            
            // Check if file exists in common locations
            $possiblePaths = [
                'assets/images/shop/' . basename($imageUrl),
                'assets/images/products/' . basename($imageUrl),
                'images/' . basename($imageUrl),
                basename($imageUrl)
            ];
            
            echo "<p><strong>Checking alternative locations:</strong></p>";
            foreach ($possiblePaths as $path) {
                if (file_exists($path)) {
                    echo "<p style='color: green;'>✅ Found at: {$path}</p>";
                } else {
                    echo "<p style='color: #999;'>❌ Not at: {$path}</p>";
                }
            }
        }
    } else {
        echo "<p style='color: orange;'><strong>⚠️ No image path set in database</strong></p>";
    }
    
    // Show the product URL for testing
    $productUrl = 'https://' . $_SERVER['HTTP_HOST'] . '/product-details.php?product=' . ($product['slug'] ?: $product['product_code']);
    echo "<p><strong>Product URL:</strong> <a href='{$productUrl}' target='_blank'>{$productUrl}</a></p>";
    
    echo "</div>";
}

// Show directory contents
echo "<h2>📁 Image Directory Contents</h2>";
$imageDirs = ['assets/images/shop/', 'assets/images/products/', 'images/'];

foreach ($imageDirs as $dir) {
    echo "<h3>Directory: {$dir}</h3>";
    if (is_dir($dir)) {
        $files = scandir($dir);
        $imageFiles = array_filter($files, function($file) {
            return in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif', 'webp']);
        });
        
        if (!empty($imageFiles)) {
            echo "<ul>";
            foreach ($imageFiles as $file) {
                echo "<li>{$file} (" . number_format(filesize($dir . $file)) . " bytes)</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: #999;'>No image files found</p>";
        }
    } else {
        echo "<p style='color: red;'>Directory does not exist</p>";
    }
}

echo "<hr>";
echo "<h2>🔧 Quick Fix Options</h2>";
echo "<p>If images are missing:</p>";
echo "<ol>";
echo "<li><strong>Upload missing images</strong> to the correct directory</li>";
echo "<li><strong>Update database paths</strong> if images are in wrong location</li>";
echo "<li><strong>Use admin panel</strong> to re-upload product images</li>";
echo "</ol>";
?>
