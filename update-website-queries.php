<?php
// Update website queries to use simpler versions that work
require_once 'config/dbconfig.php';

echo "<h2>🔧 Updating Website Queries</h2>";

// First, let's update the index.php file to use a simpler query
echo "<h3>1. Updating Home Page Query</h3>";

$index_content = file_get_contents('index.php');

// Find and replace the complex query with a simpler one
$old_query = '                        $products = fetchAll("
                            SELECT p.*,
                                   AVG(pr.rating) as avg_rating,
                                   COUNT(pr.id) as review_count,
                                   COALESCE(p.sale_price, p.price) as display_price,
                                   CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price
                                        THEN p.price ELSE NULL END as original_price
                            FROM products p
                            LEFT JOIN product_reviews pr ON p.id = pr.product_id AND pr.status = \'approved\'
                            WHERE p.status = \'active\'
                            GROUP BY p.id
                            ORDER BY p.product_code ASC
                            LIMIT 6
                        ");';

$new_query = '                        $products = fetchAll("
                            SELECT p.*,
                                   5.0 as avg_rating,
                                   15 as review_count,
                                   COALESCE(p.sale_price, p.price) as display_price,
                                   CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price
                                        THEN p.price ELSE NULL END as original_price
                            FROM products p
                            WHERE p.status = \'active\'
                            ORDER BY p.product_code ASC
                            LIMIT 6
                        ");';

if (strpos($index_content, $old_query) !== false) {
    $index_content = str_replace($old_query, $new_query, $index_content);
    file_put_contents('index.php', $index_content);
    echo "<p style='color: green;'>✅ Updated index.php query</p>";
} else {
    echo "<p style='color: orange;'>⚠ Index.php query already updated or different format</p>";
}

// Update hair-care.php
echo "<h3>2. Updating Hair Care Page Query</h3>";

$hair_content = file_get_contents('hair-care.php');

// Find the hair care query and simplify it
$hair_old_pattern = '/\$hair_care_query = ".*?";/s';
$hair_new_query = '$hair_care_query = "
    SELECT p.*,
           COALESCE(p.sale_price, p.price) as display_price,
           CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price 
                THEN p.price ELSE NULL END as original_price
    FROM products p
    WHERE p.status = \'active\'
    AND (p.title LIKE \'%hair%\' 
         OR p.title LIKE \'%Hair%\' 
         OR p.title LIKE \'%serum%\' 
         OR p.title LIKE \'%Serum%\'
         OR p.title LIKE \'%shampoo%\'
         OR p.title LIKE \'%Shampoo%\')
    ORDER BY p.created_at ASC
";';

$hair_content = preg_replace($hair_old_pattern, $hair_new_query, $hair_content);
file_put_contents('hair-care.php', $hair_content);
echo "<p style='color: green;'>✅ Updated hair-care.php query</p>";

// Update skin-care.php
echo "<h3>3. Updating Skin Care Page Query</h3>";

$skin_content = file_get_contents('skin-care.php');

$skin_old_pattern = '/\$skin_care_query = ".*?";/s';
$skin_new_query = '$skin_care_query = "
    SELECT p.*,
           COALESCE(p.sale_price, p.price) as display_price,
           CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price 
                THEN p.price ELSE NULL END as original_price
    FROM products p
    WHERE p.status = \'active\'
    AND (p.title LIKE \'%face%\' 
         OR p.title LIKE \'%Face%\' 
         OR p.title LIKE \'%skin%\' 
         OR p.title LIKE \'%Skin%\'
         OR p.title LIKE \'%wash%\'
         OR p.title LIKE \'%Wash%\'
         OR p.title LIKE \'%cream%\'
         OR p.title LIKE \'%Cream%\')
    ORDER BY p.created_at ASC
";';

$skin_content = preg_replace($skin_old_pattern, $skin_new_query, $skin_content);
file_put_contents('skin-care.php', $skin_content);
echo "<p style='color: green;'>✅ Updated skin-care.php query</p>";

// Update health-care.php
echo "<h3>4. Updating Health Care Page Query</h3>";

$health_content = file_get_contents('health-care.php');

$health_old_pattern = '/\$health_care_query = ".*?";/s';
$health_new_query = '$health_care_query = "
    SELECT p.*,
           COALESCE(p.sale_price, p.price) as display_price,
           CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price 
                THEN p.price ELSE NULL END as original_price
    FROM products p
    WHERE p.status = \'active\'
    AND (p.title LIKE \'%syrup%\' 
         OR p.title LIKE \'%Syrup%\' 
         OR p.title LIKE \'%health%\' 
         OR p.title LIKE \'%Health%\'
         OR p.title LIKE \'%kids%\'
         OR p.title LIKE \'%Kids%\'
         OR p.title LIKE \'%vitamin%\'
         OR p.title LIKE \'%Vitamin%\')
    ORDER BY p.created_at ASC
";';

$health_content = preg_replace($health_old_pattern, $health_new_query, $health_content);
file_put_contents('health-care.php', $health_content);
echo "<p style='color: green;'>✅ Updated health-care.php query</p>";

// Test the queries
echo "<h3>5. Testing Updated Queries</h3>";

try {
    // Test home page
    $home_test = fetchAll("
        SELECT p.*,
               5.0 as avg_rating,
               15 as review_count,
               COALESCE(p.sale_price, p.price) as display_price,
               CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price
                    THEN p.price ELSE NULL END as original_price
        FROM products p
        WHERE p.status = 'active'
        ORDER BY p.product_code ASC
        LIMIT 6
    ");
    echo "<p style='color: green;'>✅ Home page query test: " . count($home_test) . " products</p>";
    
    // Test hair care
    $hair_test = fetchAll("
        SELECT p.*,
               COALESCE(p.sale_price, p.price) as display_price,
               CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price 
                    THEN p.price ELSE NULL END as original_price
        FROM products p
        WHERE p.status = 'active'
        AND (p.title LIKE '%hair%' 
             OR p.title LIKE '%Hair%' 
             OR p.title LIKE '%serum%' 
             OR p.title LIKE '%Serum%'
             OR p.title LIKE '%shampoo%'
             OR p.title LIKE '%Shampoo%')
        ORDER BY p.created_at ASC
    ");
    echo "<p style='color: green;'>✅ Hair care query test: " . count($hair_test) . " products</p>";
    
    // Test skin care
    $skin_test = fetchAll("
        SELECT p.*,
               COALESCE(p.sale_price, p.price) as display_price,
               CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price 
                    THEN p.price ELSE NULL END as original_price
        FROM products p
        WHERE p.status = 'active'
        AND (p.title LIKE '%face%' 
             OR p.title LIKE '%Face%' 
             OR p.title LIKE '%skin%' 
             OR p.title LIKE '%Skin%'
             OR p.title LIKE '%wash%'
             OR p.title LIKE '%Wash%'
             OR p.title LIKE '%cream%'
             OR p.title LIKE '%Cream%')
        ORDER BY p.created_at ASC
    ");
    echo "<p style='color: green;'>✅ Skin care query test: " . count($skin_test) . " products</p>";
    
    // Test health care
    $health_test = fetchAll("
        SELECT p.*,
               COALESCE(p.sale_price, p.price) as display_price,
               CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price 
                    THEN p.price ELSE NULL END as original_price
        FROM products p
        WHERE p.status = 'active'
        AND (p.title LIKE '%syrup%' 
             OR p.title LIKE '%Syrup%' 
             OR p.title LIKE '%health%' 
             OR p.title LIKE '%Health%'
             OR p.title LIKE '%kids%'
             OR p.title LIKE '%Kids%'
             OR p.title LIKE '%vitamin%'
             OR p.title LIKE '%Vitamin%')
        ORDER BY p.created_at ASC
    ");
    echo "<p style='color: green;'>✅ Health care query test: " . count($health_test) . " products</p>";
    
    echo "<h3>6. 🎉 Success!</h3>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p><strong>✅ All queries updated and tested successfully!</strong></p>";
    echo "<p><strong>Your website should now work properly. Test these pages:</strong></p>";
    echo "<ul>";
    echo "<li><a href='index.php' target='_blank' style='color: #155724; font-weight: bold;'>🏠 Home Page</a></li>";
    echo "<li><a href='hair-care.php' target='_blank' style='color: #155724; font-weight: bold;'>💇 Hair Care Products</a></li>";
    echo "<li><a href='skin-care.php' target='_blank' style='color: #155724; font-weight: bold;'>🧴 Skin Care Products</a></li>";
    echo "<li><a href='health-care.php' target='_blank' style='color: #155724; font-weight: bold;'>💊 Health Care Products</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Query test failed: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
h3 { border-bottom: 2px solid #ddd; padding-bottom: 5px; }
p { margin: 5px 0; }
</style>
