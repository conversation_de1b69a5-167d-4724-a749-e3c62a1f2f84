<?php
require_once 'config/dbconfig.php';

echo "<h1>🔗 Update Product Links to Use SEO-Friendly URLs</h1>";
echo "<p>This script will update all product links across your website to use product slugs instead of product codes.</p>";

// Get all products with their slugs
$products = fetchAll("SELECT id, product_code, title, slug FROM products WHERE status = 'active' AND slug IS NOT NULL");

if (empty($products)) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px;'>";
    echo "<h3>❌ No Product Slugs Found</h3>";
    echo "<p>Please run the <a href='implement-product-slugs.php'>implement-product-slugs.php</a> script first to generate product slugs.</p>";
    echo "</div>";
    exit;
}

// Create mapping of product codes to slugs
$codeToSlug = [];
foreach ($products as $product) {
    $codeToSlug[$product['product_code']] = $product['slug'];
}

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>📋 Found " . count($products) . " Products with Slugs</h2>";
echo "<p>Ready to update product links in the following files:</p>";
echo "</div>";

// List of files to update
$filesToUpdate = [
    'index.php',
    'hair-care.php',
    'skin-care.php',
    'health-care.php',
    'personal-care.php',
    'search.php',
    'product.php'
];

$updatedFiles = 0;
$totalUpdates = 0;

foreach ($filesToUpdate as $filename) {
    if (!file_exists($filename)) {
        echo "<p style='color: orange;'>⚠ File not found: $filename</p>";
        continue;
    }
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>📄 Updating: $filename</h3>";
    
    $content = file_get_contents($filename);
    $originalContent = $content;
    $fileUpdates = 0;
    
    // Update product links
    foreach ($codeToSlug as $productCode => $slug) {
        // Pattern to match product-details.php?product=PRODXXX
        $pattern = '/product-details\.php\?product=' . preg_quote($productCode, '/') . '(?=["\'\s&])/';
        $replacement = 'product-details.php?product=' . $slug;
        
        $newContent = preg_replace($pattern, $replacement, $content);
        
        if ($newContent !== $content) {
            $matches = preg_match_all($pattern, $content);
            $fileUpdates += $matches;
            $content = $newContent;
            echo "<p>✅ Updated <code>$productCode</code> → <code>$slug</code></p>";
        }
    }
    
    // Save the updated file
    if ($content !== $originalContent) {
        if (file_put_contents($filename, $content)) {
            echo "<p style='color: green;'><strong>✅ $filename updated successfully! ($fileUpdates links updated)</strong></p>";
            $updatedFiles++;
            $totalUpdates += $fileUpdates;
        } else {
            echo "<p style='color: red;'>❌ Failed to update $filename</p>";
        }
    } else {
        echo "<p style='color: #6c757d;'>ℹ No updates needed for $filename</p>";
    }
    
    echo "</div>";
}

echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>📊 Update Summary</h2>";
echo "<p><strong>Files Updated:</strong> $updatedFiles</p>";
echo "<p><strong>Total Links Updated:</strong> $totalUpdates</p>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>🧪 Test Your Updated Links</h2>";
echo "<p>Test the updated product links on these pages:</p>";

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 20px 0;'>";
foreach ($filesToUpdate as $filename) {
    if (file_exists($filename)) {
        $pageName = ucwords(str_replace(['-', '.php'], [' ', ''], $filename));
        echo "<a href='$filename' target='_blank' style='background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px; text-align: center;'>📱 Test $pageName</a>";
    }
}
echo "</div>";
echo "</div>";

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>🔍 Sample New URLs</h2>";
echo "<p>Here are some examples of your new SEO-friendly URLs:</p>";

$sampleProducts = array_slice($products, 0, 5);
foreach ($sampleProducts as $product) {
    $oldUrl = "product-details.php?product=" . $product['product_code'];
    $newUrl = "product-details.php?product=" . $product['slug'];
    
    echo "<div style='background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff;'>";
    echo "<h4>{$product['title']}</h4>";
    echo "<p><strong>Old URL:</strong> <span style='color: #6c757d; font-family: monospace;'>$oldUrl</span></p>";
    echo "<p><strong>New URL:</strong> <a href='$newUrl' target='_blank' style='color: #28a745; font-family: monospace;'>$newUrl</a></p>";
    echo "</div>";
}
echo "</div>";

echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>✅ Next Steps</h2>";
echo "<ol>";
echo "<li><strong>Update .htaccess:</strong> <a href='update-htaccess-for-slugs.php' style='color: #007bff;'>Add URL redirects for SEO</a></li>";
echo "<li><strong>Test Cart Function:</strong> <a href='test-cart-with-slugs.php' style='color: #007bff;'>Verify add to cart still works</a></li>";
echo "<li><strong>Update Sitemap:</strong> Update your sitemap with new URLs</li>";
echo "<li><strong>Google Search Console:</strong> Submit new URLs to Google</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>🔄 Rollback Option</h2>";
echo "<p>If you need to revert the changes:</p>";
echo "<a href='rollback-product-links.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔄 Rollback to Product Codes</a>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: monospace; }
a { text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
