<?php
require_once 'config/dbconfig.php';

echo "<h1>🔍 Face Wash Database Check</h1>";

// Check all Face Wash products
$face_wash_products = fetchAll("
    SELECT id, product_code, title, slug, image, status
    FROM products 
    WHERE title LIKE '%face%wash%' OR title LIKE '%Face%Wash%' OR title LIKE '%anti%acne%'
    ORDER BY id ASC
");

echo "<h2>📊 Face Wash Products in Database</h2>";

if (empty($face_wash_products)) {
    echo "<p style='color: red;'>❌ No Face Wash products found!</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>ID</th><th>Product Code</th><th>Title</th><th>Slug</th><th>Image</th><th>Status</th>";
    echo "</tr>";
    
    foreach ($face_wash_products as $product) {
        echo "<tr>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . htmlspecialchars($product['product_code']) . "</td>";
        echo "<td>" . htmlspecialchars($product['title']) . "</td>";
        echo "<td><strong>" . htmlspecialchars($product['slug'] ?: 'NO SLUG') . "</strong></td>";
        echo "<td>" . htmlspecialchars($product['image']) . "</td>";
        echo "<td>" . $product['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h2>🔗 Test URLs</h2>";

foreach ($face_wash_products as $product) {
    echo "<div style='background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3>" . htmlspecialchars($product['title']) . "</h3>";
    
    // Test by product code
    $url_by_code = "https://drzianaturals.com/product-details.php?product=" . $product['product_code'];
    echo "<p><strong>By Product Code:</strong> <a href='$url_by_code' target='_blank'>$url_by_code</a></p>";
    
    // Test by slug (if exists)
    if ($product['slug']) {
        $url_by_slug = "https://drzianaturals.com/product-details.php?product=" . $product['slug'];
        echo "<p><strong>By Slug:</strong> <a href='$url_by_slug' target='_blank'>$url_by_slug</a></p>";
    }
    
    // Test the URL you were using
    if (strpos(strtolower($product['title']), 'anti') !== false && strpos(strtolower($product['title']), 'acne') !== false) {
        $test_url = "https://drzianaturals.com/product-details.php?product=face-wash-anti-acne-100ml";
        echo "<p><strong>Your Test URL:</strong> <a href='$test_url' target='_blank'>$test_url</a></p>";
        echo "<p style='color: orange;'>⚠️ This URL might not work if the slug doesn't match exactly!</p>";
    }
    
    echo "</div>";
}

echo "<h2>🛠️ Facebook Debugger Links</h2>";

foreach ($face_wash_products as $product) {
    if ($product['slug']) {
        $product_url = "https://drzianaturals.com/product-details.php?product=" . $product['slug'];
        $fb_debug_url = "https://developers.facebook.com/tools/debug/?q=" . urlencode($product_url);
        
        echo "<div style='background: #e3f2fd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>" . htmlspecialchars($product['title']) . "</h4>";
        echo "<p><strong>Product URL:</strong> <code>$product_url</code></p>";
        echo "<p><a href='$fb_debug_url' target='_blank' style='background: #1877f2; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>🔍 Debug in Facebook</a></p>";
        echo "</div>";
    }
}

echo "<h2>🎯 Recommended Actions</h2>";
echo "<ol>";
echo "<li><strong>Use the correct slug</strong> from the table above in your URLs</li>";
echo "<li><strong>Test the product page</strong> by clicking the links above</li>";
echo "<li><strong>Use Facebook Debugger</strong> with the correct URL</li>";
echo "<li><strong>If slug is missing,</strong> run the slug generation script</li>";
echo "</ol>";
?>
