<?php
session_start();
require_once '../config/dbconfig.php';

// Redirect if already logged in
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    header('Location: dashboard.php');
    exit();
}

$error_message = '';
$debug_info = [];

if ($_POST) {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    $debug_info[] = "Username entered: '$username'";
    $debug_info[] = "Password entered: '$password'";
    
    if (empty($username) || empty($password)) {
        $error_message = 'Please enter both username and password.';
    } else {
        // Check admin credentials
        $sql = "SELECT id, username, email, password, full_name, role, status FROM admin_users WHERE username = ? AND status = 'active'";
        $admin = fetchSingle($sql, [$username]);
        
        $debug_info[] = "SQL Query: $sql";
        $debug_info[] = "Admin found: " . ($admin ? 'YES' : 'NO');
        
        if ($admin) {
            $debug_info[] = "Admin ID: " . $admin['id'];
            $debug_info[] = "Admin Status: " . $admin['status'];
            $debug_info[] = "Stored Hash: " . $admin['password'];
            
            $password_verify = password_verify($password, $admin['password']);
            $debug_info[] = "Password Verify Result: " . ($password_verify ? 'SUCCESS' : 'FAILED');
            
            if ($password_verify) {
                // Login successful
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['username'];
                $_SESSION['admin_name'] = $admin['full_name'];
                $_SESSION['admin_role'] = $admin['role'];
                
                // Update last login
                updateData("UPDATE admin_users SET last_login = NOW() WHERE id = ?", [$admin['id']]);
                
                $debug_info[] = "Login successful - redirecting to dashboard";
                header('Location: dashboard.php');
                exit();
            } else {
                $error_message = 'Invalid password.';
            }
        } else {
            $error_message = 'Username not found or account inactive.';
        }
    }
}

// Test database connection
$db_test = testConnection();
$debug_info[] = "Database Connection: " . ($db_test ? 'SUCCESS' : 'FAILED');

// Check if admin_users table exists
try {
    $table_check = fetchSingle("SHOW TABLES LIKE 'admin_users'", []);
    $debug_info[] = "admin_users table exists: " . ($table_check ? 'YES' : 'NO');
    
    if ($table_check) {
        $admin_count = fetchSingle("SELECT COUNT(*) as count FROM admin_users", [])['count'] ?? 0;
        $debug_info[] = "Total admin users: $admin_count";
        
        $active_admin_count = fetchSingle("SELECT COUNT(*) as count FROM admin_users WHERE status = 'active'", [])['count'] ?? 0;
        $debug_info[] = "Active admin users: $active_admin_count";
    }
} catch (Exception $e) {
    $debug_info[] = "Error checking admin_users table: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login Debug - Dr.Zia Naturals</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 1rem;
            margin-top: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h2><i class="fas fa-bug"></i></h2>
            <h4>Admin Login Debug</h4>
            <p class="mb-0">Dr.Zia Naturals</p>
        </div>
        <div class="login-body">
            <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="mb-3">
                    <label for="username" class="form-label">
                        <i class="fas fa-user"></i> Username
                    </label>
                    <input type="text" class="form-control" id="username" name="username" 
                           value="<?php echo htmlspecialchars($_POST['username'] ?? 'admin'); ?>" required>
                </div>
                
                <div class="mb-4">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock"></i> Password
                    </label>
                    <input type="password" class="form-control" id="password" name="password" 
                           value="<?php echo htmlspecialchars($_POST['password'] ?? 'admin123'); ?>" required>
                </div>
                
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-sign-in-alt"></i> Login
                </button>
            </form>
            
            <div class="text-center mt-3">
                <a href="fix-admin-password.php" class="btn btn-warning btn-sm">
                    <i class="fas fa-wrench"></i> Fix Admin Password
                </a>
                <a href="login.php" class="btn btn-secondary btn-sm">
                    <i class="fas fa-arrow-left"></i> Normal Login
                </a>
            </div>
            
            <?php if (!empty($debug_info)): ?>
                <div class="debug-info">
                    <h6><i class="fas fa-info-circle"></i> Debug Information:</h6>
                    <?php foreach ($debug_info as $info): ?>
                        <div><?php echo htmlspecialchars($info); ?></div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
