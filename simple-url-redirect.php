<?php
require_once 'config/dbconfig.php';

echo "<h1>🔧 Simple URL Redirect Setup</h1>";
echo "<p>This will create .htaccess redirects to show SEO-friendly URLs without breaking your existing pages.</p>";

try {
    // Step 1: Ensure database has slugs
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>📊 Step 1: Database Setup</h2>";
    
    // Check if slug column exists
    $columns = fetchAll("SHOW COLUMNS FROM products LIKE 'slug'");
    
    if (empty($columns)) {
        echo "<p>Adding slug column...</p>";
        executeQuery("ALTER TABLE products ADD COLUMN slug VARCHAR(255) NULL AFTER product_code");
        executeQuery("ALTER TABLE products ADD UNIQUE KEY unique_slug (slug)");
        echo "<p style='color: green;'>✅ Slug column added!</p>";
    } else {
        echo "<p style='color: green;'>✅ Slug column exists!</p>";
    }
    
    // Generate slugs for products without them
    $products = fetchAll("SELECT id, product_code, title, slug FROM products WHERE slug IS NULL OR slug = ''");
    
    if (!empty($products)) {
        echo "<p>Generating slugs for " . count($products) . " products...</p>";
        
        foreach ($products as $product) {
            // Create slug from title
            $slug = strtolower($product['title']);
            $slug = preg_replace('/[^a-z0-9\-]/', '-', $slug);
            $slug = preg_replace('/-+/', '-', $slug);
            $slug = trim($slug, '-');
            
            if (strlen($slug) > 200) {
                $slug = substr($slug, 0, 200);
                $slug = substr($slug, 0, strrpos($slug, '-'));
            }
            
            // Check if slug exists
            $existing = fetchSingle("SELECT id FROM products WHERE slug = ? AND id != ?", [$slug, $product['id']]);
            if ($existing) {
                $slug = $slug . '-' . $product['id'];
            }
            
            executeQuery("UPDATE products SET slug = ? WHERE id = ?", [$slug, $product['id']]);
            echo "<p>✅ {$product['title']} → {$slug}</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ All products have slugs!</p>";
    }
    echo "</div>";

    // Step 2: Create .htaccess redirects
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>⚙ Step 2: Create .htaccess Redirects</h2>";
    
    // Get all products with slugs
    $allProducts = fetchAll("SELECT product_code, slug FROM products WHERE status = 'active' AND slug IS NOT NULL");
    
    if (empty($allProducts)) {
        echo "<p style='color: red;'>❌ No products with slugs found!</p>";
        exit;
    }
    
    // Backup existing .htaccess
    if (file_exists('.htaccess')) {
        $backup_name = '.htaccess.backup.' . date('Y-m-d-H-i-s');
        copy('.htaccess', $backup_name);
        echo "<p>✅ Backed up existing .htaccess to $backup_name</p>";
    }
    
    // Create .htaccess content
    $htaccess_content = "# SEO-Friendly Product URL Redirects\n";
    $htaccess_content .= "# Generated on " . date('Y-m-d H:i:s') . "\n\n";
    $htaccess_content .= "RewriteEngine On\n\n";
    
    // Add redirects from old product codes to new slugs
    $htaccess_content .= "# Redirect old product code URLs to SEO-friendly URLs\n";
    foreach ($allProducts as $product) {
        $htaccess_content .= "RewriteCond %{QUERY_STRING} ^product=" . preg_quote($product['product_code'], '/') . "(&.*)?$\n";
        $htaccess_content .= "RewriteRule ^product-details\\.php$ /zia1/product-details.php?product=" . $product['slug'] . " [R=301,L]\n\n";
    }
    
    // Write .htaccess file
    if (file_put_contents('.htaccess', $htaccess_content)) {
        echo "<p style='color: green;'>✅ Created .htaccess with " . count($allProducts) . " redirect rules</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create .htaccess file</p>";
    }
    echo "</div>";

    // Step 3: Test the redirects
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🧪 Test Your Redirects</h2>";
    echo "<p>Your existing pages work normally, but when someone clicks a product link, it will redirect to SEO-friendly URLs:</p>";
    
    $sampleProducts = array_slice($allProducts, 0, 3);
    foreach ($sampleProducts as $product) {
        $oldUrl = "product-details.php?product=" . $product['product_code'];
        $newUrl = "product-details.php?product=" . $product['slug'];
        
        echo "<div style='background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<p><strong>Test this redirect:</strong></p>";
        echo "<p>Click: <a href='$oldUrl' target='_blank' style='color: #dc3545; font-family: monospace;'>$oldUrl</a></p>";
        echo "<p>Redirects to: <span style='color: #28a745; font-family: monospace; font-weight: bold;'>$newUrl</span></p>";
        echo "</div>";
    }
    echo "</div>";

    // Success message
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center;'>";
    echo "<h2>🎉 Setup Complete!</h2>";
    echo "<p style='font-size: 18px; color: #155724;'>Your pages work normally, but URLs now redirect to SEO-friendly format!</p>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<h3>✅ What Works:</h3>";
    echo "<ul style='text-align: left; display: inline-block;'>";
    echo "<li>✅ All your existing pages work normally</li>";
    echo "<li>✅ Product links redirect to SEO-friendly URLs</li>";
    echo "<li>✅ Cart functionality preserved</li>";
    echo "<li>✅ No code changes to your working pages</li>";
    echo "<li>✅ SEO benefits from descriptive URLs</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

    // Test links
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🔗 Test Your Pages</h2>";
    echo "<p>Your pages should work normally now:</p>";
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    $pages = [
        'index.php' => '🏠 Home Page',
        'hair-care.php' => '💇 Hair Care',
        'skin-care.php' => '🧴 Skin Care',
        'health-care.php' => '💊 Health Care',
        'personal-care.php' => '🧼 Personal Care'
    ];
    
    foreach ($pages as $page => $title) {
        echo "<a href='$page' target='_blank' style='background: #28a745; color: white; padding: 15px; text-decoration: none; border-radius: 5px; text-align: center; display: block; font-weight: bold;'>$title</a>";
    }
    echo "</div>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p style='color: #721c24;'>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
a { text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
