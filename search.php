<?php
require_once 'config/dbconfig.php';
include('header.php');

// Get search query
$search_query = isset($_GET['q']) ? trim($_GET['q']) : '';
$search_results = [];
$total_results = 0;

if (!empty($search_query)) {
    // Search in products table
    $search_sql = "
        SELECT DISTINCT p.*,
               GROUP_CONCAT(c.name SEPARATOR ', ') as categories,
               COALESCE(p.sale_price, p.price) as display_price,
               CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price
                    THEN p.price ELSE NULL END as original_price
        FROM products p
        LEFT JOIN product_categories pc ON p.id = pc.product_id
        LEFT JOIN categories c ON pc.category_id = c.id
        WHERE p.status = 'active'
        AND (p.title LIKE ? 
             OR p.description LIKE ? 
             OR p.key_benefits LIKE ?
             OR c.name LIKE ?)
        GROUP BY p.id
        ORDER BY p.title ASC
    ";
    
    $search_term = '%' . $search_query . '%';
    $search_results = fetchAll($search_sql, [$search_term, $search_term, $search_term, $search_term]);
    $total_results = count($search_results);
}
?>

<br><br><br><br>

<!-- Search Results Hero -->
<section class="search-hero-banner position-relative">
    <div class="container">
        <div class="row align-items-center py-4">
            <div class="col-12">
                <div class="search-hero-content text-center">
                    <h1 class="display-5 fw-bold text-dark mb-3">
                        <?php if (!empty($search_query)): ?>
                            Search Results for "<?php echo htmlspecialchars($search_query); ?>"
                        <?php else: ?>
                            Search Products
                        <?php endif; ?>
                    </h1>
                    <?php if (!empty($search_query)): ?>
                        <p class="lead text-muted">
                            Found <?php echo $total_results; ?> product<?php echo $total_results !== 1 ? 's' : ''; ?>
                        </p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Search Form Section -->
<section class="search-form-section py-4 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <form action="search.php" method="GET" class="search-form-main">
                    <div class="input-group input-group-lg">
                        <input type="search" 
                               name="q" 
                               class="form-control" 
                               placeholder="Search for products..." 
                               value="<?php echo htmlspecialchars($search_query); ?>"
                               required>
                        <button class="btn btn-primary" type="submit">
                            <i class="fa fa-search me-2"></i>Search
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Search Results Section -->
<section class="search-results-section py-5">
    <div class="container">
        <?php if (!empty($search_query)): ?>
            <?php if ($total_results > 0): ?>
                <div class="row">
                    <?php foreach ($search_results as $product): 
                        $review_count = rand(5, 25);
                        $rating = rand(35, 50) / 10; // Random rating between 3.5 and 5.0
                    ?>
                        <div class="col-6 col-lg-4 mb-4 mb-sm-9">
                            <!--== Start Product Item ==-->
                            <div class="product-item">
                                <div class="product-thumb">
                                    <a class="d-block" href="product-details.php?product=<?php echo htmlspecialchars($product['product_code']); ?>">
                                        <img src="<?php echo htmlspecialchars($product['image'] ?: 'assets/images/shop/default.png'); ?>"
                                             width="370" height="450" alt="<?php echo htmlspecialchars($product['title']); ?>">
                                    </a>
                                    <div class="product-action">
                                        <a class="product-action-btn action-btn-quick-view" href="#" data-bs-toggle="modal" data-bs-target="#action-QuickViewModal">
                                            <i class="fa fa-expand"></i>
                                        </a>
                                        <a class="product-action-btn action-btn-wishlist" href="#" data-bs-toggle="modal" data-bs-target="#action-WishlistModal">
                                            <i class="fa fa-heart-o"></i>
                                        </a>
                                        <a class="product-action-btn action-btn-compare" href="#" data-bs-toggle="modal" data-bs-target="#action-CompareModal">
                                            <i class="fa fa-random"></i>
                                        </a>
                                    </div>
                                    <div class="product-action-bottom">
                                        <button class="product-action-cart-btn" onclick="addToCart(<?php echo $product['id']; ?>)">
                                            <span>Add to cart</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="product-info">
                                    <div class="product-info-main">
                                        <h3 class="title">
                                            <a href="product-details.php?product=<?php echo htmlspecialchars($product['product_code']); ?>">
                                                <?php echo htmlspecialchars($product['title']); ?>
                                            </a>
                                        </h3>
                                        <div class="prices">
                                            <?php if ($product['original_price']): ?>
                                                <span class="price-old">Rs. <?php echo number_format($product['original_price'], 2); ?></span>
                                            <?php endif; ?>
                                            <span class="price">Rs. <?php echo number_format($product['display_price'], 2); ?></span>
                                        </div>
                                        <div class="product-rating">
                                            <div class="rating-stars">
                                                <?php 
                                                $full_stars = floor($rating);
                                                $half_star = ($rating - $full_stars) >= 0.5;
                                                
                                                for ($i = 1; $i <= 5; $i++): 
                                                    if ($i <= $full_stars): ?>
                                                        <i class="fa fa-star"></i>
                                                    <?php elseif ($i == $full_stars + 1 && $half_star): ?>
                                                        <i class="fa fa-star-half-o"></i>
                                                    <?php else: ?>
                                                        <i class="fa fa-star-o"></i>
                                                    <?php endif;
                                                endfor; ?>
                                            </div>
                                            <span class="rating-count">(<?php echo $review_count; ?> reviews)</span>
                                        </div>
                                        <?php if ($product['categories']): ?>
                                            <div class="product-categories">
                                                <small class="text-muted">
                                                    <i class="fa fa-tag me-1"></i>
                                                    <?php echo htmlspecialchars($product['categories']); ?>
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <!--== End Product Item ==-->
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <!-- No Results Found -->
                <div class="no-results-section text-center py-5">
                    <div class="row justify-content-center">
                        <div class="col-lg-6">
                            <div class="no-results-content">
                                <i class="fa fa-search fa-4x text-muted mb-4"></i>
                                <h3 class="mb-3">No Products Found</h3>
                                <p class="text-muted mb-4">
                                    Sorry, we couldn't find any products matching "<?php echo htmlspecialchars($search_query); ?>". 
                                    Try searching with different keywords or browse our categories.
                                </p>
                                <div class="search-suggestions">
                                    <h5 class="mb-3">Try searching for:</h5>
                                    <div class="suggestion-tags">
                                        <a href="search.php?q=hair" class="btn btn-outline-primary btn-sm me-2 mb-2">Hair Care</a>
                                        <a href="search.php?q=skin" class="btn btn-outline-primary btn-sm me-2 mb-2">Skin Care</a>
                                        <a href="search.php?q=serum" class="btn btn-outline-primary btn-sm me-2 mb-2">Serum</a>
                                        <a href="search.php?q=oil" class="btn btn-outline-primary btn-sm me-2 mb-2">Oil</a>
                                        <a href="search.php?q=natural" class="btn btn-outline-primary btn-sm me-2 mb-2">Natural</a>
                                    </div>
                                </div>
                                <div class="browse-categories mt-4">
                                    <h5 class="mb-3">Or browse our categories:</h5>
                                    <div class="category-links">
                                        <a href="hair-care.php" class="btn btn-primary me-2 mb-2">Hair Care</a>
                                        <a href="skin-care.php" class="btn btn-primary me-2 mb-2">Skin Care</a>
                                        <a href="health-care.php" class="btn btn-primary me-2 mb-2">Health Care</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <!-- Empty Search State -->
            <div class="empty-search-section text-center py-5">
                <div class="row justify-content-center">
                    <div class="col-lg-6">
                        <i class="fa fa-search fa-4x text-muted mb-4"></i>
                        <h3 class="mb-3">Search Our Products</h3>
                        <p class="text-muted mb-4">
                            Enter a search term above to find the perfect natural products for your needs.
                        </p>
                        <div class="popular-searches">
                            <h5 class="mb-3">Popular Searches:</h5>
                            <div class="search-tags">
                                <a href="search.php?q=hair serum" class="btn btn-outline-primary btn-sm me-2 mb-2">Hair Serum</a>
                                <a href="search.php?q=face wash" class="btn btn-outline-primary btn-sm me-2 mb-2">Face Wash</a>
                                <a href="search.php?q=natural oil" class="btn btn-outline-primary btn-sm me-2 mb-2">Natural Oil</a>
                                <a href="search.php?q=organic" class="btn btn-outline-primary btn-sm me-2 mb-2">Organic</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php include 'includes/search-modal.php'; ?>
<?php include 'includes/offcanvas-menu.php'; ?>
<?php include 'footer.php'; ?>

<style>
.search-hero-banner {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.search-form-main .input-group {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 50px;
    overflow: hidden;
}

.search-form-main .form-control {
    border: none;
    padding: 15px 25px;
    font-size: 16px;
}

.search-form-main .btn {
    padding: 15px 30px;
    border: none;
    background: #FF6565;
    font-weight: 600;
}

.search-form-main .btn:hover {
    background: #ff3232;
}

.product-item {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.product-rating {
    margin: 10px 0;
}

.rating-stars {
    color: #ffc107;
    margin-bottom: 5px;
}

.rating-count {
    font-size: 12px;
    color: #6c757d;
}

.product-categories {
    margin-top: 8px;
}

.suggestion-tags .btn,
.search-tags .btn {
    transition: all 0.3s ease;
}

.suggestion-tags .btn:hover,
.search-tags .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
}

.no-results-section,
.empty-search-section {
    min-height: 400px;
    display: flex;
    align-items: center;
}

@media (max-width: 768px) {
    .search-form-main .input-group {
        border-radius: 25px;
    }
    
    .search-form-main .form-control,
    .search-form-main .btn {
        padding: 12px 20px;
        font-size: 14px;
    }
}
</style>

<?php include 'includes/scripts.php'; ?>

