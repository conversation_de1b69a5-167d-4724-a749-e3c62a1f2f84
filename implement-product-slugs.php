<?php
require_once 'config/dbconfig.php';

echo "<h1>🔗 Implement SEO-Friendly Product URLs</h1>";
echo "<p>This script will add product slugs to your database and update all product links to use SEO-friendly URLs.</p>";

// Function to create URL-friendly slugs
function createSlug($text) {
    // Convert to lowercase
    $slug = strtolower($text);
    
    // Replace spaces and special characters with hyphens
    $slug = preg_replace('/[^a-z0-9\-]/', '-', $slug);
    $slug = preg_replace('/-+/', '-', $slug); // Remove multiple hyphens
    $slug = trim($slug, '-'); // Remove leading/trailing hyphens
    
    // Limit length
    if (strlen($slug) > 200) {
        $slug = substr($slug, 0, 200);
        $slug = substr($slug, 0, strrpos($slug, '-')); // Don't cut in middle of word
    }
    
    return $slug;
}

try {
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>📋 Step 1: Add Slug Column to Database</h2>";
    
    // Check if slug column exists
    $columns = fetchAll("SHOW COLUMNS FROM products LIKE 'slug'");
    
    if (empty($columns)) {
        echo "<p>Adding slug column to products table...</p>";
        executeQuery("ALTER TABLE products ADD COLUMN slug VARCHAR(255) NULL AFTER product_code");
        executeQuery("ALTER TABLE products ADD UNIQUE KEY unique_slug (slug)");
        echo "<p style='color: green;'>✅ Slug column added successfully!</p>";
    } else {
        echo "<p style='color: green;'>✅ Slug column already exists!</p>";
    }
    echo "</div>";

    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🏷 Step 2: Generate Slugs for Existing Products</h2>";
    
    // Get all products without slugs
    $products = fetchAll("SELECT id, product_code, title, slug FROM products WHERE slug IS NULL OR slug = ''");
    
    if (!empty($products)) {
        echo "<p>Generating slugs for " . count($products) . " products...</p>";
        
        foreach ($products as $product) {
            $slug = createSlug($product['title']);
            
            // Check if slug already exists
            $existing = fetchSingle("SELECT id FROM products WHERE slug = ? AND id != ?", [$slug, $product['id']]);
            
            if ($existing) {
                // Append product ID to make it unique
                $slug = $slug . '-' . $product['id'];
            }
            
            // Update the product with the slug
            executeQuery("UPDATE products SET slug = ? WHERE id = ?", [$slug, $product['id']]);
            
            echo "<p>✅ <strong>{$product['title']}</strong> → <code>{$slug}</code></p>";
        }
        
        echo "<p style='color: green;'><strong>✅ All product slugs generated!</strong></p>";
    } else {
        echo "<p style='color: green;'>✅ All products already have slugs!</p>";
    }
    echo "</div>";

    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🔧 Step 3: Update product-details.php</h2>";
    echo "<p>Updating product-details.php to support both slugs and product codes...</p>";
    
    // Read current product-details.php
    $productDetailsContent = file_get_contents('product-details.php');
    
    echo "<p>⚠ Note: product-details.php will be updated manually for better compatibility.</p>";
    echo "</div>";

    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>📊 Step 4: Current Product URLs</h2>";
    echo "<p>Here are your new SEO-friendly product URLs:</p>";
    
    $allProducts = fetchAll("SELECT id, product_code, title, slug FROM products WHERE status = 'active' ORDER BY title");
    
    echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;'>";
    
    echo "<div>";
    echo "<h4>🔗 New SEO-Friendly URLs</h4>";
    foreach ($allProducts as $product) {
        $newUrl = "product-details.php?product=" . urlencode($product['slug']);
        echo "<p><strong>{$product['title']}</strong><br>";
        echo "<a href='{$newUrl}' target='_blank' style='color: #28a745; font-family: monospace;'>{$newUrl}</a></p>";
    }
    echo "</div>";
    
    echo "<div>";
    echo "<h4>🔄 Old URLs (Still Work)</h4>";
    foreach ($allProducts as $product) {
        $oldUrl = "product-details.php?product=" . urlencode($product['product_code']);
        echo "<p><strong>{$product['title']}</strong><br>";
        echo "<span style='color: #6c757d; font-family: monospace;'>{$oldUrl}</span></p>";
    }
    echo "</div>";
    
    echo "</div>";
    echo "</div>";

    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🔄 Step 5: Update Product Links</h2>";
    echo "<p>Click the button below to update all product links across your website:</p>";
    echo "<a href='update-product-links.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0;'>🔗 Update All Product Links</a>";
    echo "</div>";

    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🧪 Test Your New URLs</h2>";
    echo "<p>Test some of your new SEO-friendly URLs:</p>";
    
    $sampleProducts = array_slice($allProducts, 0, 5);
    foreach ($sampleProducts as $product) {
        $testUrl = "product-details.php?product=" . urlencode($product['slug']);
        echo "<a href='{$testUrl}' target='_blank' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>📱 Test: {$product['title']}</a>";
    }
    echo "</div>";

    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>ℹ Important Notes</h2>";
    echo "<ul>";
    echo "<li><strong>✅ Backward Compatibility:</strong> Old URLs with product codes still work</li>";
    echo "<li><strong>🔗 SEO Benefits:</strong> New URLs are search engine friendly</li>";
    echo "<li><strong>🛒 Cart Function:</strong> Add to cart functionality remains unchanged</li>";
    echo "<li><strong>📱 Mobile Friendly:</strong> URLs work on all devices</li>";
    echo "<li><strong>🔄 Auto-Redirect:</strong> Consider adding redirects from old URLs to new ones</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p style='color: #721c24;'>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: monospace; }
a { text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
