<?php
require_once 'config/dbconfig.php';

echo "<h1>🛒 Test Cart Functionality with SEO-Friendly URLs</h1>";
echo "<p>This page tests that the add to cart functionality works properly with the new product slug URLs.</p>";

try {
    // Get products with slugs
    $products = fetchAll("
        SELECT id, product_code, title, slug, price, image 
        FROM products 
        WHERE status = 'active' AND slug IS NOT NULL 
        ORDER BY title 
        LIMIT 5
    ");
    
    if (empty($products)) {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px;'>";
        echo "<h3>❌ No Products Found</h3>";
        echo "<p>Please ensure products have been set up with slugs.</p>";
        echo "</div>";
        exit;
    }

    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>✅ Found " . count($products) . " Products to Test</h2>";
    echo "<p>Each product below uses the new SEO-friendly URL format. Test the 'Add to Cart' functionality.</p>";
    echo "</div>";

    // Display products for testing
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;'>";
    
    foreach ($products as $product) {
        $slug_url = "product-details.php?product=" . urlencode($product['slug']);
        $code_url = "product-details.php?product=" . urlencode($product['product_code']);
        
        echo "<div style='border: 1px solid #ddd; border-radius: 10px; padding: 20px; background: white;'>";
        echo "<div style='text-align: center; margin-bottom: 15px;'>";
        
        if ($product['image']) {
            echo "<img src='" . htmlspecialchars($product['image']) . "' alt='" . htmlspecialchars($product['title']) . "' style='max-width: 100%; height: 200px; object-fit: cover; border-radius: 5px;'>";
        } else {
            echo "<div style='width: 100%; height: 200px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; border-radius: 5px; color: #6c757d;'>No Image</div>";
        }
        
        echo "</div>";
        
        echo "<h3 style='margin: 15px 0; text-align: center;'>" . htmlspecialchars($product['title']) . "</h3>";
        echo "<p style='text-align: center; font-size: 18px; color: #28a745; font-weight: bold;'>Rs. " . number_format($product['price'], 0) . "</p>";
        
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>🔗 New SEO URL:</strong></p>";
        echo "<p style='font-family: monospace; font-size: 12px; word-break: break-all; color: #28a745;'>" . $slug_url . "</p>";
        echo "<p><strong>🔄 Old URL:</strong></p>";
        echo "<p style='font-family: monospace; font-size: 12px; word-break: break-all; color: #6c757d;'>" . $code_url . "</p>";
        echo "</div>";
        
        echo "<div style='text-align: center; margin: 15px 0;'>";
        echo "<a href='$slug_url' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>📱 Test New URL</a>";
        echo "<a href='$code_url' target='_blank' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>🔄 Test Old URL</a>";
        echo "</div>";
        
        // Add to cart test button
        echo "<div style='text-align: center; margin: 15px 0;'>";
        echo "<button onclick='testAddToCart(" . $product['id'] . ", \"" . htmlspecialchars($product['title']) . "\")' style='background: #28a745; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>🛒 Test Add to Cart</button>";
        echo "</div>";
        
        echo "<div id='cart-result-" . $product['id'] . "' style='margin: 10px 0; padding: 10px; border-radius: 5px; display: none;'></div>";
        
        echo "</div>";
    }
    
    echo "</div>";

    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🧪 Testing Instructions</h2>";
    echo "<ol>";
    echo "<li><strong>Click 'Test New URL'</strong> - This should open the product page with the SEO-friendly URL</li>";
    echo "<li><strong>Verify the URL</strong> - Check that the browser address bar shows the product name in the URL</li>";
    echo "<li><strong>Test Add to Cart</strong> - Click the 'Add to Cart' button on the product page</li>";
    echo "<li><strong>Check Cart Count</strong> - Verify the cart count updates in the header</li>";
    echo "<li><strong>View Cart</strong> - Go to the cart page to see the added products</li>";
    echo "<li><strong>Test Old URL</strong> - Verify backward compatibility by testing the old product code URLs</li>";
    echo "</ol>";
    echo "</div>";

    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🔍 What to Look For</h2>";
    echo "<ul>";
    echo "<li><strong>✅ URL Format:</strong> Should show product name instead of PROD001, PROD002, etc.</li>";
    echo "<li><strong>✅ Page Loading:</strong> Product details should load correctly</li>";
    echo "<li><strong>✅ Add to Cart:</strong> Should work without errors</li>";
    echo "<li><strong>✅ Cart Count:</strong> Should update in the header</li>";
    echo "<li><strong>✅ Backward Compatibility:</strong> Old URLs should still work</li>";
    echo "<li><strong>✅ Redirects:</strong> Old URLs should redirect to new ones (if .htaccess is updated)</li>";
    echo "</ul>";
    echo "</div>";

    // Cart testing section
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🛒 Quick Cart Test</h2>";
    echo "<p>Test the add to cart functionality directly from this page:</p>";
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<button onclick='clearCart()' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;'>🗑 Clear Cart</button>";
    echo "<a href='cart.php' target='_blank' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>👀 View Cart</a>";
    echo "<span id='cart-count-display' style='background: #28a745; color: white; padding: 10px 15px; border-radius: 5px; margin: 5px; display: inline-block;'>Cart: 0 items</span>";
    echo "</div>";
    echo "</div>";

    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>📊 Test Results</h2>";
    echo "<div id='test-results' style='margin: 15px 0;'>";
    echo "<p>Test results will appear here as you test the functionality...</p>";
    echo "</div>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p style='color: #721c24;'>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<script>
// Test add to cart functionality
function testAddToCart(productId, productTitle) {
    const resultDiv = document.getElementById('cart-result-' + productId);
    const testResults = document.getElementById('test-results');
    
    // Show loading
    resultDiv.style.display = 'block';
    resultDiv.style.background = '#fff3cd';
    resultDiv.innerHTML = '<p>🔄 Testing add to cart for ' + productTitle + '...</p>';
    
    // Prepare request data
    const requestData = {
        product_id: productId,
        volume_id: null,
        quantity: 1
    };
    
    // Send AJAX request
    fetch('cart/add-to-cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.style.background = '#d4edda';
            resultDiv.innerHTML = '<p style="color: #155724;">✅ Success! ' + productTitle + ' added to cart. Cart count: ' + data.cart_count + '</p>';
            
            // Update cart count display
            document.getElementById('cart-count-display').textContent = 'Cart: ' + data.cart_count + ' items';
            
            // Add to test results
            const timestamp = new Date().toLocaleTimeString();
            testResults.innerHTML += '<p style="color: #28a745;">[' + timestamp + '] ✅ ' + productTitle + ' - Add to cart successful</p>';
        } else {
            resultDiv.style.background = '#f8d7da';
            resultDiv.innerHTML = '<p style="color: #721c24;">❌ Error: ' + data.message + '</p>';
            
            // Add to test results
            const timestamp = new Date().toLocaleTimeString();
            testResults.innerHTML += '<p style="color: #dc3545;">[' + timestamp + '] ❌ ' + productTitle + ' - Error: ' + data.message + '</p>';
        }
    })
    .catch(error => {
        resultDiv.style.background = '#f8d7da';
        resultDiv.innerHTML = '<p style="color: #721c24;">❌ Network Error: ' + error.message + '</p>';
        
        // Add to test results
        const timestamp = new Date().toLocaleTimeString();
        testResults.innerHTML += '<p style="color: #dc3545;">[' + timestamp + '] ❌ ' + productTitle + ' - Network Error</p>';
    });
}

// Clear cart function
function clearCart() {
    fetch('cart/clear-cart.php', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('cart-count-display').textContent = 'Cart: 0 items';
            
            // Clear all result divs
            const resultDivs = document.querySelectorAll('[id^="cart-result-"]');
            resultDivs.forEach(div => {
                div.style.display = 'none';
            });
            
            // Add to test results
            const timestamp = new Date().toLocaleTimeString();
            document.getElementById('test-results').innerHTML += '<p style="color: #007bff;">[' + timestamp + '] 🗑 Cart cleared successfully</p>';
        }
    })
    .catch(error => {
        console.error('Error clearing cart:', error);
    });
}

// Load current cart count on page load
document.addEventListener('DOMContentLoaded', function() {
    fetch('cart/get-cart-count.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('cart-count-display').textContent = 'Cart: ' + data.cart_count + ' items';
            }
        })
        .catch(error => console.log('Error loading cart count:', error));
});
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
button:hover { opacity: 0.9; }
a:hover { text-decoration: underline; }
</style>
