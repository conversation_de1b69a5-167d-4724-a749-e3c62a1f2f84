<?php
require_once 'config/dbconfig.php';

echo "<h1>🔧 Update .htaccess for SEO-Friendly Product URLs</h1>";
echo "<p>This script will update your .htaccess file to support clean URLs and proper redirects.</p>";

try {
    // Get all products with slugs
    $products = fetchAll("SELECT product_code, slug FROM products WHERE status = 'active' AND slug IS NOT NULL");
    
    if (empty($products)) {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px;'>";
        echo "<h3>❌ No Product Slugs Found</h3>";
        echo "<p>Please run the <a href='implement-product-slugs.php'>implement-product-slugs.php</a> script first.</p>";
        echo "</div>";
        exit;
    }

    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>📋 Step 1: Backup Current .htaccess</h2>";
    
    if (file_exists('.htaccess')) {
        $backup_name = '.htaccess.backup.' . date('Y-m-d-H-i-s');
        if (copy('.htaccess', $backup_name)) {
            echo "<p style='color: green;'>✅ Current .htaccess backed up as: $backup_name</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to backup .htaccess</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ No existing .htaccess file found</p>";
    }
    echo "</div>";

    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🔧 Step 2: Generate New .htaccess Rules</h2>";
    
    $htaccess_content = "# Dr.Zia Naturals - SEO-Friendly URLs and Redirects\n";
    $htaccess_content .= "# Generated on " . date('Y-m-d H:i:s') . "\n\n";
    
    $htaccess_content .= "RewriteEngine On\n\n";
    
    // Add general rules for clean URLs
    $htaccess_content .= "# Force .php extension to show in URLs\n";
    $htaccess_content .= "# Redirect URLs without .php to URLs with .php\n\n";
    
    // Add specific redirects for main pages
    $main_pages = [
        'about-us' => 'about-us.php',
        'hair-care' => 'hair-care.php', 
        'skin-care' => 'skin-care.php',
        'health-care' => 'health-care.php',
        'personal-care' => 'personal-care.php',
        'contact' => 'contact.php',
        'cart' => 'cart.php',
        'checkout' => 'checkout.php',
        'my-account' => 'my-account.php',
        'login' => 'login.php',
        'signup' => 'signup.php'
    ];
    
    foreach ($main_pages as $clean => $actual) {
        $htaccess_content .= "RewriteCond %{QUERY_STRING} (.*)\n";
        $htaccess_content .= "RewriteRule ^{$clean}$ /{$actual}?%1 [R=301,L]\n\n";
    }
    
    // Special rule for product-details with query parameters
    $htaccess_content .= "# Product details page with query parameters\n";
    $htaccess_content .= "RewriteCond %{QUERY_STRING} (.*)\n";
    $htaccess_content .= "RewriteRule ^product-details$ /product-details.php?%1 [R=301,L]\n\n";
    
    // Add redirects from old product codes to new slugs
    $htaccess_content .= "# Redirect old product code URLs to new slug URLs\n";
    foreach ($products as $product) {
        $htaccess_content .= "RewriteCond %{QUERY_STRING} ^product=" . preg_quote($product['product_code'], '/') . "(&.*)?$\n";
        $htaccess_content .= "RewriteRule ^product-details\\.php$ /product-details.php?product=" . $product['slug'] . "&%2 [R=301,L]\n\n";
    }
    
    // Add security and performance rules
    $htaccess_content .= "# Security Rules\n";
    $htaccess_content .= "# Prevent access to sensitive files\n";
    $htaccess_content .= "<Files ~ \"\\.(env|log|ini)$\">\n";
    $htaccess_content .= "    Order allow,deny\n";
    $htaccess_content .= "    Deny from all\n";
    $htaccess_content .= "</Files>\n\n";
    
    $htaccess_content .= "# Performance Rules\n";
    $htaccess_content .= "# Enable compression\n";
    $htaccess_content .= "<IfModule mod_deflate.c>\n";
    $htaccess_content .= "    AddOutputFilterByType DEFLATE text/plain\n";
    $htaccess_content .= "    AddOutputFilterByType DEFLATE text/html\n";
    $htaccess_content .= "    AddOutputFilterByType DEFLATE text/xml\n";
    $htaccess_content .= "    AddOutputFilterByType DEFLATE text/css\n";
    $htaccess_content .= "    AddOutputFilterByType DEFLATE application/xml\n";
    $htaccess_content .= "    AddOutputFilterByType DEFLATE application/xhtml+xml\n";
    $htaccess_content .= "    AddOutputFilterByType DEFLATE application/rss+xml\n";
    $htaccess_content .= "    AddOutputFilterByType DEFLATE application/javascript\n";
    $htaccess_content .= "    AddOutputFilterByType DEFLATE application/x-javascript\n";
    $htaccess_content .= "</IfModule>\n\n";
    
    $htaccess_content .= "# Browser Caching\n";
    $htaccess_content .= "<IfModule mod_expires.c>\n";
    $htaccess_content .= "    ExpiresActive On\n";
    $htaccess_content .= "    ExpiresByType image/jpg \"access plus 1 month\"\n";
    $htaccess_content .= "    ExpiresByType image/jpeg \"access plus 1 month\"\n";
    $htaccess_content .= "    ExpiresByType image/gif \"access plus 1 month\"\n";
    $htaccess_content .= "    ExpiresByType image/png \"access plus 1 month\"\n";
    $htaccess_content .= "    ExpiresByType text/css \"access plus 1 month\"\n";
    $htaccess_content .= "    ExpiresByType application/pdf \"access plus 1 month\"\n";
    $htaccess_content .= "    ExpiresByType application/javascript \"access plus 1 month\"\n";
    $htaccess_content .= "    ExpiresByType application/x-javascript \"access plus 1 month\"\n";
    $htaccess_content .= "    ExpiresByType application/x-shockwave-flash \"access plus 1 month\"\n";
    $htaccess_content .= "    ExpiresByType image/x-icon \"access plus 1 year\"\n";
    $htaccess_content .= "    ExpiresDefault \"access plus 2 days\"\n";
    $htaccess_content .= "</IfModule>\n";
    
    echo "<p>Generated .htaccess rules for " . count($products) . " products</p>";
    echo "</div>";

    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>💾 Step 3: Save New .htaccess File</h2>";
    
    if (file_put_contents('.htaccess', $htaccess_content)) {
        echo "<p style='color: green;'>✅ New .htaccess file created successfully!</p>";
        echo "<p><strong>Features added:</strong></p>";
        echo "<ul>";
        echo "<li>✅ SEO-friendly URL redirects</li>";
        echo "<li>✅ Product code to slug redirects</li>";
        echo "<li>✅ Security rules</li>";
        echo "<li>✅ Performance optimization</li>";
        echo "<li>✅ Browser caching</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create .htaccess file</p>";
    }
    echo "</div>";

    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🧪 Test Your Redirects</h2>";
    echo "<p>Test these URL redirects to make sure they work:</p>";
    
    $sample_products = array_slice($products, 0, 3);
    foreach ($sample_products as $product) {
        $old_url = "product-details.php?product=" . $product['product_code'];
        $new_url = "product-details.php?product=" . $product['slug'];
        
        echo "<div style='background: white; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<p><strong>Test Redirect:</strong></p>";
        echo "<p>Old: <a href='$old_url' target='_blank' style='color: #dc3545;'>$old_url</a></p>";
        echo "<p>Should redirect to: <a href='$new_url' target='_blank' style='color: #28a745;'>$new_url</a></p>";
        echo "</div>";
    }
    echo "</div>";

    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>📊 Summary</h2>";
    echo "<p><strong>✅ What was accomplished:</strong></p>";
    echo "<ul>";
    echo "<li>🔄 " . count($products) . " product redirects created</li>";
    echo "<li>🔧 Main page redirects added</li>";
    echo "<li>🛡 Security rules implemented</li>";
    echo "<li>⚡ Performance optimizations added</li>";
    echo "<li>💾 Old .htaccess backed up</li>";
    echo "</ul>";
    echo "</div>";

    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🚀 Next Steps</h2>";
    echo "<ol>";
    echo "<li><strong>Test all redirects</strong> to ensure they work properly</li>";
    echo "<li><strong>Update your sitemap</strong> with new URLs</li>";
    echo "<li><strong>Submit to Google Search Console</strong></li>";
    echo "<li><strong>Monitor for 404 errors</strong> in your analytics</li>";
    echo "<li><strong>Test cart functionality</strong> with new URLs</li>";
    echo "</ol>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p style='color: #721c24;'>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: monospace; }
a { text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
