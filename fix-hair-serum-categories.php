<?php
require_once 'dbconfig.php';

echo "<h2>🔧 Fixing Hair Serum Category Assignment</h2>";

// 1. First, let's see all products with "serum" in the title
echo "<h3>1. Finding Hair Serum Products</h3>";

$serum_products = fetchAll("
    SELECT p.*, 
           GROUP_CONCAT(c.name SEPARATOR ', ') as current_categories,
           GROUP_CONCAT(c.id SEPARATOR ', ') as category_ids
    FROM products p
    LEFT JOIN product_categories pc ON p.id = pc.product_id
    LEFT JOIN categories c ON pc.category_id = c.id
    WHERE p.title LIKE '%serum%' OR p.title LIKE '%Serum%'
    GROUP BY p.id
");

if (empty($serum_products)) {
    echo "<p style='color: orange;'>⚠ No serum products found</p>";
} else {
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📦 Found Serum Products:</h4>";
    foreach ($serum_products as $product) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 3px;'>";
        echo "<strong>Product:</strong> " . htmlspecialchars($product['title']) . "<br>";
        echo "<strong>ID:</strong> " . $product['id'] . "<br>";
        echo "<strong>Current Categories:</strong> " . ($product['current_categories'] ?: 'None') . "<br>";
        echo "<strong>Category IDs:</strong> " . ($product['category_ids'] ?: 'None') . "<br>";
        echo "</div>";
    }
    echo "</div>";
}

// 2. Get category IDs
echo "<h3>2. Getting Category Information</h3>";

$categories = fetchAll("SELECT id, name FROM categories WHERE status = 'active'");
$category_map = [];
foreach ($categories as $cat) {
    $category_map[strtolower($cat['name'])] = $cat['id'];
}

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>📋 Available Categories:</h4>";
foreach ($categories as $cat) {
    echo "<p><strong>{$cat['name']}</strong> (ID: {$cat['id']})</p>";
}
echo "</div>";

// 3. Fix hair serum categories
echo "<h3>3. Fixing Hair Serum Categories</h3>";

if (!empty($serum_products)) {
    foreach ($serum_products as $product) {
        $title_lower = strtolower($product['title']);
        
        // Check if this is a hair serum
        if (strpos($title_lower, 'hair') !== false || 
            strpos($title_lower, 'scalp') !== false ||
            (strpos($title_lower, 'serum') !== false && 
             strpos($title_lower, 'face') === false && 
             strpos($title_lower, 'skin') === false)) {
            
            echo "<div style='border: 1px solid #28a745; padding: 15px; margin: 10px 0; border-radius: 5px; background: #d4edda;'>";
            echo "<h4>🔧 Processing: " . htmlspecialchars($product['title']) . "</h4>";
            
            // Remove from all categories first
            $removed = executeQuery("DELETE FROM product_categories WHERE product_id = ?", [$product['id']]);
            echo "<p>✅ Removed from all existing categories</p>";
            
            // Add to Hair Care category only
            if (isset($category_map['hair care'])) {
                executeQuery("INSERT INTO product_categories (product_id, category_id) VALUES (?, ?)", 
                           [$product['id'], $category_map['hair care']]);
                echo "<p>✅ Added to <strong>Hair Care</strong> category</p>";
            } else {
                echo "<p style='color: red;'>❌ Hair Care category not found</p>";
            }
            
            echo "</div>";
        }
    }
} else {
    echo "<p style='color: blue;'>ℹ No serum products to process</p>";
}

// 4. Verify the fix
echo "<h3>4. Verification - Updated Product Categories</h3>";

$updated_serum_products = fetchAll("
    SELECT p.*, 
           GROUP_CONCAT(c.name SEPARATOR ', ') as current_categories
    FROM products p
    LEFT JOIN product_categories pc ON p.id = pc.product_id
    LEFT JOIN categories c ON pc.category_id = c.id
    WHERE p.title LIKE '%serum%' OR p.title LIKE '%Serum%'
    GROUP BY p.id
");

if (!empty($updated_serum_products)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ Updated Serum Products:</h4>";
    foreach ($updated_serum_products as $product) {
        echo "<div style='border: 1px solid #c3e6cb; padding: 10px; margin: 5px 0; border-radius: 3px; background: white;'>";
        echo "<strong>Product:</strong> " . htmlspecialchars($product['title']) . "<br>";
        echo "<strong>Categories:</strong> " . ($product['current_categories'] ?: 'None') . "<br>";
        echo "</div>";
    }
    echo "</div>";
}

// 5. Test the queries
echo "<h3>5. Testing Category Page Queries</h3>";

// Test Hair Care query
echo "<h4>🧪 Hair Care Page Query Test:</h4>";
$hair_care_test = fetchAll("
    SELECT DISTINCT p.title,
           GROUP_CONCAT(c.name SEPARATOR ', ') as categories
    FROM products p
    LEFT JOIN product_categories pc ON p.id = pc.product_id
    LEFT JOIN categories c ON pc.category_id = c.id
    WHERE p.status = 'active'
    AND (c.name = 'Hair Care'
         OR p.title LIKE '%hair%'
         OR p.title LIKE '%Hair%'
         OR p.title LIKE '%shampoo%'
         OR p.title LIKE '%Shampoo%'
         OR p.title LIKE '%conditioner%'
         OR p.title LIKE '%Conditioner%'
         OR p.title LIKE '%oil%'
         OR p.title LIKE '%Oil%'
         OR p.title LIKE '%serum%'
         OR p.title LIKE '%Serum%')
    GROUP BY p.id
    ORDER BY p.title
");

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h5>Hair Care Products Found:</h5>";
foreach ($hair_care_test as $product) {
    echo "<p><strong>" . htmlspecialchars($product['title']) . "</strong> - Categories: " . ($product['categories'] ?: 'None') . "</p>";
}
echo "</div>";

// Test Health Care query
echo "<h4>🧪 Health Care Page Query Test:</h4>";
$health_care_test = fetchAll("
    SELECT DISTINCT p.title,
           GROUP_CONCAT(c.name SEPARATOR ', ') as categories
    FROM products p
    LEFT JOIN product_categories pc ON p.id = pc.product_id
    LEFT JOIN categories c ON pc.category_id = c.id
    WHERE p.status = 'active'
    AND (c.name = 'Health Care'
         OR p.title LIKE '%syrup%'
         OR p.title LIKE '%Syrup%'
         OR p.title LIKE '%health%'
         OR p.title LIKE '%Health%'
         OR p.title LIKE '%supplement%'
         OR p.title LIKE '%Supplement%'
         OR p.title LIKE '%vitamin%'
         OR p.title LIKE '%Vitamin%'
         OR p.title LIKE '%medicine%'
         OR p.title LIKE '%Medicine%'
         OR p.title LIKE '%kids%'
         OR p.title LIKE '%Kids%'
         OR p.title LIKE '%children%'
         OR p.title LIKE '%Children%'
         OR p.title LIKE '%child%'
         OR p.title LIKE '%Child%')
    AND p.title NOT LIKE '%wash%'
    AND p.title NOT LIKE '%Wash%'
    AND p.title NOT LIKE '%face%'
    AND p.title NOT LIKE '%Face%'
    GROUP BY p.id
    ORDER BY p.title
");

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h5>Health Care Products Found:</h5>";
foreach ($health_care_test as $product) {
    echo "<p><strong>" . htmlspecialchars($product['title']) . "</strong> - Categories: " . ($product['categories'] ?: 'None') . "</p>";
}
echo "</div>";

echo "<hr>";
echo "<h3>📊 Summary</h3>";
echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<p><strong>✅ Hair serum products have been moved to Hair Care category only</strong></p>";
echo "<p><strong>✅ Health Care category now excludes hair serums</strong></p>";
echo "<p><strong>✅ Category assignments are now clean and specific</strong></p>";
echo "</div>";

echo "<h3>🔗 Test Your Pages</h3>";
echo "<p><a href='hair-care.php' target='_blank' style='color: #007bff; text-decoration: none;'>🧴 Test Hair Care Page</a></p>";
echo "<p><a href='health-care.php' target='_blank' style='color: #007bff; text-decoration: none;'>💊 Test Health Care Page</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
hr { margin: 30px 0; border: none; border-top: 2px solid #eee; }
</style>
