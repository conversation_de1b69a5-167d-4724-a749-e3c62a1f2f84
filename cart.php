<?php
session_start();
require_once 'config/dbconfig.php';

$session_id = session_id();
$cart_items = [];
$cart_total = 0;
$shipping_fee = 200; // Default shipping fee

if ($session_id) {
    // Get cart items with product details
    $cart_items = fetchAll("
        SELECT ci.*, p.title, p.image, p.product_code, p.price, p.sale_price,
               pv.size, pv.price as volume_price, pv.offer,
               COALESCE(pv.price, COALESCE(p.sale_price, p.price)) as item_price,
               CASE WHEN pv.id IS NULL AND p.sale_price IS NOT NULL AND p.sale_price < p.price
                    THEN p.price ELSE NULL END as original_price
        FROM cart_items ci
        JOIN products p ON ci.product_id = p.id
        LEFT JOIN product_volumes pv ON ci.volume_id = pv.id
        WHERE ci.session_id = ?
        ORDER BY ci.added_at DESC
    ", [$session_id]);
    
    // Calculate total
    foreach ($cart_items as $item) {
        $cart_total += $item['item_price'] * $item['quantity'];
    }
}

$final_total = $cart_total + $shipping_fee;

// Handle cart updates
if ($_POST) {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        $item_id = intval($_POST['item_id'] ?? 0);
        
        try {
            switch ($action) {
                case 'update_quantity':
                    $quantity = intval($_POST['quantity'] ?? 1);
                    if ($quantity > 0 && $quantity <= 10) {
                        executeQuery("UPDATE cart_items SET quantity = ?, updated_at = NOW() WHERE id = ? AND session_id = ?", 
                            [$quantity, $item_id, $session_id]);
                        $success_message = 'Cart updated successfully';
                    } else {
                        $error_message = 'Invalid quantity';
                    }
                    break;
                    
                case 'remove_item':
                    executeQuery("DELETE FROM cart_items WHERE id = ? AND session_id = ?", [$item_id, $session_id]);
                    $success_message = 'Item removed from cart';
                    break;
                    
                case 'clear_cart':
                    executeQuery("DELETE FROM cart_items WHERE session_id = ?", [$session_id]);
                    $success_message = 'Cart cleared successfully';
                    break;
            }
            
            // Refresh cart items after update
            header('Location: cart.php');
            exit();
            
        } catch (Exception $e) {
            $error_message = 'Error updating cart: ' . $e->getMessage();
        }
    }
}

include 'header.php';
?>

<!-- Custom Cart Styling -->
<link rel="stylesheet" href="assets/css/cart-checkout-custom.css">

<br><br><br>
<main class="main-content">
    <!--== Start Page Header Area Wrapper ==-->
    <div class="page-header-area" data-bg-img="assets/images/photos/bg3.jpg">
        <div class="container pt--0 pb--0">
            <div class="row">
                <div class="col-12">
                    <div class="page-header-content">
                        <h2 class="title" data-aos="fade-down" data-aos-duration="1000">Shopping Cart</h2>
                        <nav class="breadcrumb-area" data-aos="fade-down" data-aos-duration="1200">
                            <ul class="breadcrumb">
                                <li><a href="index.php">Home</a></li>
                                <li class="breadcrumb-sep">//</li>
                                <li>Shopping Cart</li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--== End Page Header Area Wrapper ==-->

    <!--== Start Shopping Cart Area Wrapper ==-->
    <section class="section-space">
        <div class="container">
            <?php if (isset($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fa fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if (empty($cart_items)): ?>
                <div class="text-center py-5">
                    <i class="fa fa-shopping-cart fa-5x text-muted mb-4"></i>
                    <h3>Your cart is empty</h3>
                    <p class="text-muted">Add some products to your cart to continue shopping.</p>
                    <a href="index.php" class="btn btn-primary">Continue Shopping</a>
                </div>
            <?php else: ?>
                <div class="row">
                    <div class="col-lg-8">
                        <div class="cart-table-wrap">
                            <div class="cart-table table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th class="width-thumbnail">Image</th>
                                            <th class="width-name">Product</th>
                                            <th class="width-price">Price</th>
                                            <th class="width-quantity">Quantity</th>
                                            <th class="width-subtotal">Subtotal</th>
                                            <th class="width-remove">Remove</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($cart_items as $item): ?>
                                            <tr>
                                                <td class="product-thumbnail" data-label="Image">
                                                    <a href="product-details.php?product=<?php echo htmlspecialchars($item['product_code']); ?>">
                                                        <img src="<?php echo htmlspecialchars($item['image'] ?: 'assets/images/shop/default.png'); ?>"
                                                             alt="<?php echo htmlspecialchars($item['title']); ?>" width="80" height="80">
                                                    </a>
                                                </td>
                                                <td class="product-name" data-label="Product">
                                                    <h5><a href="product-details.php?product=<?php echo htmlspecialchars($item['product_code']); ?>">
                                                        <?php echo htmlspecialchars($item['title']); ?>
                                                    </a></h5>
                                                    <?php if ($item['size']): ?>
                                                        <p class="variation"><strong>Size:</strong> <?php echo htmlspecialchars($item['size']); ?></p>
                                                    <?php endif; ?>
                                                    <?php if ($item['offer']): ?>
                                                        <p class="offer text-success"><strong>Offer:</strong> <?php echo htmlspecialchars($item['offer']); ?></p>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="product-price" data-label="Price">
                                                    <?php if ($item['original_price']): ?>
                                                        <span class="original-price text-muted text-decoration-line-through">Rs. <?php echo number_format($item['original_price'], 2); ?></span><br>
                                                        <span class="sale-price text-danger fw-bold">Rs. <?php echo number_format($item['item_price'], 2); ?></span>
                                                    <?php else: ?>
                                                        <span class="amount">Rs. <?php echo number_format($item['item_price'], 2); ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="cart-quality" data-label="Quantity">
                                                    <div class="product-quality">
                                                        <form method="POST" class="d-inline quantity-form">
                                                            <input type="hidden" name="action" value="update_quantity">
                                                            <input type="hidden" name="item_id" value="<?php echo $item['id']; ?>">
                                                            <div class="quantity-wrapper">
                                                                <button type="button" class="qty-btn qty-minus" onclick="changeQuantity(this, -1)">-</button>
                                                                <input type="number" name="quantity" value="<?php echo $item['quantity']; ?>"
                                                                       min="1" max="10" class="qty-input"
                                                                       onchange="this.form.submit()" readonly>
                                                                <button type="button" class="qty-btn qty-plus" onclick="changeQuantity(this, 1)">+</button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </td>
                                                <td class="product-total" data-label="Subtotal">
                                                    <span class="amount">Rs. <?php echo number_format($item['item_price'] * $item['quantity'], 2); ?></span>
                                                </td>
                                                <td class="product-remove" data-label="Remove">
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="action" value="remove_item">
                                                        <input type="hidden" name="item_id" value="<?php echo $item['id']; ?>">
                                                        <button type="submit" class="btn btn-link text-danger"
                                                                onclick="return confirm('Remove this item from cart?')">
                                                            <i class="fa fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="cart-shiping-update-wrapper">
                            <div class="cart-shiping-update">
                                <a href="index.php" class="btn btn-outline-primary">Continue Shopping</a>
                            </div>
                            <div class="cart-clear">
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="clear_cart">
                                    <button type="submit" class="btn btn-outline-danger" 
                                            onclick="return confirm('Clear entire cart?')">
                                        <i class="fa fa-trash"></i> Clear Cart
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="cart-calculate-area">
                            <h4>Cart Totals</h4>
                            <div class="cart-calculate-item">
                                <div class="cart-calculate-item-inner">
                                    <div class="cart-calculate-details">
                                        <div class="cart-calculate-details-item">
                                            <span>Subtotal</span>
                                            <span>Rs. <?php echo number_format($cart_total, 2); ?></span>
                                        </div>
                                        <div class="cart-calculate-details-item">
                                            <span>Shipping</span>
                                            <span>Rs. <?php echo number_format($shipping_fee, 2); ?></span>
                                        </div>
                                        <div class="cart-calculate-details-item cart-calculate-total">
                                            <span>Total</span>
                                            <span>Rs. <?php echo number_format($final_total, 2); ?></span>
                                        </div>
                                    </div>
                                    <a href="checkout.php" class="btn btn-primary btn-block">Proceed to Checkout</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>
    <!--== End Shopping Cart Area Wrapper ==-->
</main>

<?php include 'footer.php'; ?>

<script>
// Update cart count in session storage
document.addEventListener('DOMContentLoaded', function() {
    const cartCount = <?php echo array_sum(array_column($cart_items, 'quantity')); ?>;

    // Update cart count display if element exists
    const cartCountElement = document.querySelector('.cart-count');
    if (cartCount > 0) {
        if (cartCountElement) {
            cartCountElement.textContent = cartCount;
            cartCountElement.style.display = 'inline-block';
        }
    } else {
        if (cartCountElement) {
            cartCountElement.style.display = 'none';
        }
    }
});

// Quantity change function
function changeQuantity(button, change) {
    const form = button.closest('.quantity-form');
    const input = form.querySelector('.qty-input');
    const currentValue = parseInt(input.value);
    const newValue = currentValue + change;

    if (newValue >= 1 && newValue <= 10) {
        input.value = newValue;
        form.submit();
    }
}

// Add loading state to forms
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Processing...';
            }
        });
    });
});
</script>

<?php include 'includes/search-modal.php'; ?>
<?php include 'includes/scripts.php'; ?>

    <?php include 'includes/offcanvas-menu.php'; ?>
    <?php include 'includes/search-modal.php'; ?>

</body>
</html>
