<?php
session_start();
require_once 'config/dbconfig.php';

// Test add to cart functionality
if ($_POST && isset($_POST['test_add'])) {
    $product_id = intval($_POST['product_id']);
    $quantity = intval($_POST['quantity']) ?: 1;
    
    // Simulate the add to cart process
    $session_id = session_id();
    
    try {
        // Check if product exists
        $product = fetchSingle("SELECT * FROM products WHERE id = ? AND status = 'active'", [$product_id]);
        
        if (!$product) {
            $error = "Product not found or inactive";
        } else {
            // Create or update cart session
            $cart_session = fetchSingle("SELECT * FROM cart_sessions WHERE session_id = ?", [$session_id]);
            
            if (!$cart_session) {
                $expires_at = date('Y-m-d H:i:s', strtotime('+7 days'));
                insertData("INSERT INTO cart_sessions (session_id, expires_at) VALUES (?, ?)", [$session_id, $expires_at]);
            }
            
            // Check if item already exists in cart
            $existing_item = fetchSingle("
                SELECT * FROM cart_items 
                WHERE session_id = ? AND product_id = ? AND volume_id IS NULL
            ", [$session_id, $product_id]);
            
            if ($existing_item) {
                // Update quantity
                $new_quantity = $existing_item['quantity'] + $quantity;
                updateData("UPDATE cart_items SET quantity = ?, updated_at = NOW() WHERE id = ?", 
                          [$new_quantity, $existing_item['id']]);
            } else {
                // Insert new item
                insertData("
                    INSERT INTO cart_items (session_id, product_id, quantity) 
                    VALUES (?, ?, ?)
                ", [$session_id, $product_id, $quantity]);
            }
            
            $success = "Product added to cart successfully!";
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get current cart status
$session_id = session_id();
$cart_items = fetchAll("
    SELECT ci.*, p.title, p.image, p.price 
    FROM cart_items ci
    JOIN products p ON ci.product_id = p.id
    WHERE ci.session_id = ?
", [$session_id]);

$cart_count = array_sum(array_column($cart_items, 'quantity'));

// Get available products
$products = fetchAll("SELECT * FROM products WHERE status = 'active' LIMIT 5");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cart Debug - Dr.Zia Naturals</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2><i class="fas fa-bug"></i> Cart Debug Tool</h2>
        
        <?php if (isset($success)): ?>
            <div class="alert alert-success"><i class="fas fa-check"></i> <?php echo $success; ?></div>
        <?php endif; ?>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?></div>
        <?php endif; ?>
        
        <!-- Cart Status -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-shopping-cart"></i> Cart Status</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Session ID:</strong> <code><?php echo htmlspecialchars($session_id); ?></code></p>
                        <p><strong>Total Items:</strong> <span class="badge bg-primary"><?php echo $cart_count; ?></span></p>
                        <p><strong>Unique Products:</strong> <span class="badge bg-info"><?php echo count($cart_items); ?></span></p>
                        
                        <div class="mt-3">
                            <a href="cart.php" class="btn btn-success btn-sm">
                                <i class="fas fa-eye"></i> View Cart
                            </a>
                            <a href="index.php" class="btn btn-primary btn-sm">
                                <i class="fas fa-home"></i> Homepage
                            </a>
                            <a href="test-cart.php" class="btn btn-info btn-sm">
                                <i class="fas fa-vial"></i> Test Cart
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tools"></i> Quick Test</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="mb-3">
                                <label class="form-label">Product ID:</label>
                                <select name="product_id" class="form-select" required>
                                    <option value="">Select Product</option>
                                    <?php foreach ($products as $product): ?>
                                        <option value="<?php echo $product['id']; ?>">
                                            <?php echo htmlspecialchars($product['title']); ?> (ID: <?php echo $product['id']; ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Quantity:</label>
                                <input type="number" name="quantity" class="form-control" value="1" min="1" max="10">
                            </div>
                            <button type="submit" name="test_add" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add to Cart
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Current Cart Items -->
        <?php if (!empty($cart_items)): ?>
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list"></i> Current Cart Items</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Image</th>
                                <th>Product</th>
                                <th>Price</th>
                                <th>Quantity</th>
                                <th>Total</th>
                                <th>Added</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($cart_items as $item): ?>
                            <tr>
                                <td>
                                    <img src="<?php echo htmlspecialchars($item['image'] ?: 'assets/images/shop/default.png'); ?>" 
                                         width="50" height="50" class="rounded">
                                </td>
                                <td><?php echo htmlspecialchars($item['title']); ?></td>
                                <td>Rs. <?php echo number_format($item['price'], 2); ?></td>
                                <td><span class="badge bg-secondary"><?php echo $item['quantity']; ?></span></td>
                                <td>Rs. <?php echo number_format($item['price'] * $item['quantity'], 2); ?></td>
                                <td><?php echo date('M d, H:i', strtotime($item['added_at'])); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr class="table-info">
                                <th colspan="4">Total:</th>
                                <th>Rs. <?php echo number_format(array_sum(array_map(function($item) { 
                                    return $item['price'] * $item['quantity']; 
                                }, $cart_items)), 2); ?></th>
                                <th></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle"></i> Cart is Empty</h5>
            <p>Use the form above to add products to the cart for testing.</p>
        </div>
        <?php endif; ?>
        
        <!-- AJAX Test -->
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-code"></i> AJAX Test</h5>
            </div>
            <div class="card-body">
                <p>Test the AJAX add to cart functionality:</p>
                <div class="row">
                    <?php foreach (array_slice($products, 0, 3) as $product): ?>
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <img src="<?php echo htmlspecialchars($product['image'] ?: 'assets/images/shop/default.png'); ?>" 
                                 class="card-img-top" style="height: 150px; object-fit: cover;">
                            <div class="card-body text-center">
                                <h6 class="card-title"><?php echo htmlspecialchars($product['title']); ?></h6>
                                <p class="card-text">Rs. <?php echo number_format($product['price'], 2); ?></p>
                                <button class="btn btn-primary btn-sm" onclick="testAjaxAddToCart(<?php echo $product['id']; ?>)">
                                    <i class="fas fa-plus"></i> Add via AJAX
                                </button>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testAjaxAddToCart(productId) {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
            
            fetch('cart/add-to-cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: 1
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('Product added successfully!', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert('Error: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('AJAX Error: ' + error.message, 'danger');
            })
            .finally(() => {
                btn.disabled = false;
                btn.innerHTML = originalText;
            });
        }
        
        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);
            
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
