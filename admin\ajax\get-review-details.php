<?php
session_start();
require_once '../../config/dbconfig.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

header('Content-Type: application/json');

try {
    $review_id = intval($_GET['id'] ?? 0);
    
    // Validation
    if ($review_id <= 0) {
        throw new Exception('Invalid review ID');
    }
    
    // Fetch review with product details
    $review = fetchSingle("
        SELECT pr.*, p.title as product_title, p.product_code, p.image as product_image, p.price
        FROM product_reviews pr
        LEFT JOIN products p ON pr.product_id = p.id
        WHERE pr.id = ?
    ", [$review_id]);
    
    if (!$review) {
        throw new Exception('Review not found');
    }
    
    // Generate HTML for review details
    $html = '
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Product Information</h6>
                </div>
                <div class="card-body">
                    ' . ($review['product_image'] ? '<img src="../../' . htmlspecialchars($review['product_image']) . '" alt="Product" class="img-fluid mb-3" style="max-height: 200px; object-fit: cover;">' : '') . '
                    <h6>' . htmlspecialchars($review['product_title']) . '</h6>
                    <p class="text-muted mb-1">Code: ' . htmlspecialchars($review['product_code']) . '</p>
                    <p class="text-muted">Price: Rs. ' . number_format($review['price'], 0) . '</p>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Review Details</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Customer Name:</strong></div>
                        <div class="col-sm-8">' . htmlspecialchars($review['customer_name']) . '</div>
                    </div>
                    
                    ' . ($review['customer_designation'] ? '
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Designation:</strong></div>
                        <div class="col-sm-8">' . htmlspecialchars($review['customer_designation']) . '</div>
                    </div>
                    ' : '') . '
                    
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Rating:</strong></div>
                        <div class="col-sm-8">
                            <div class="d-flex align-items-center">
                                ';
                                
    for ($i = 1; $i <= 5; $i++) {
        $html .= '<i class="fas fa-star ' . ($i <= $review['rating'] ? 'text-warning' : 'text-muted') . '"></i>';
    }
    
    $html .= '
                                <span class="ms-2">(' . $review['rating'] . ' out of 5)</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Status:</strong></div>
                        <div class="col-sm-8">
                            <span class="badge bg-' . ($review['status'] == 'approved' ? 'success' : ($review['status'] == 'pending' ? 'warning' : 'danger')) . '">
                                ' . ucfirst($review['status']) . '
                            </span>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Date:</strong></div>
                        <div class="col-sm-8">' . date('F d, Y \a\t g:i A', strtotime($review['created_at'])) . '</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-sm-4"><strong>Comment:</strong></div>
                        <div class="col-sm-8">
                            <div class="border rounded p-3 bg-light">
                                ' . nl2br(htmlspecialchars($review['comment'])) . '
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="mt-3">
        <div class="btn-group">
            ' . ($review['status'] !== 'approved' ? '<button class="btn btn-success" onclick="updateReviewStatus(' . $review['id'] . ', \'approved\')">Approve</button>' : '') . '
            ' . ($review['status'] !== 'rejected' ? '<button class="btn btn-warning" onclick="updateReviewStatus(' . $review['id'] . ', \'rejected\')">Reject</button>' : '') . '
            ' . ($review['status'] !== 'pending' ? '<button class="btn btn-info" onclick="updateReviewStatus(' . $review['id'] . ', \'pending\')">Mark as Pending</button>' : '') . '
            <button class="btn btn-danger" onclick="deleteReview(' . $review['id'] . ')">Delete</button>
        </div>
    </div>
    ';
    
    echo json_encode([
        'success' => true,
        'html' => $html
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
