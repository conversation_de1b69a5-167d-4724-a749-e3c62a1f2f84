<?php
require_once 'config/dbconfig.php';

echo "<h1>🔧 Fix Product URLs Now</h1>";
echo "<p>This will immediately implement SEO-friendly URLs for your products.</p>";

// Function to create URL-friendly slugs
function createSlug($text) {
    $slug = strtolower($text);
    $slug = preg_replace('/[^a-z0-9\-]/', '-', $slug);
    $slug = preg_replace('/-+/', '-', $slug);
    $slug = trim($slug, '-');
    
    if (strlen($slug) > 200) {
        $slug = substr($slug, 0, 200);
        $slug = substr($slug, 0, strrpos($slug, '-'));
    }
    
    return $slug;
}

try {
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🔧 Step 1: Database Setup</h2>";
    
    // Check if slug column exists
    $columns = fetchAll("SHOW COLUMNS FROM products LIKE 'slug'");
    
    if (empty($columns)) {
        echo "<p>Adding slug column...</p>";
        executeQuery("ALTER TABLE products ADD COLUMN slug VARCHAR(255) NULL AFTER product_code");
        executeQuery("ALTER TABLE products ADD UNIQUE KEY unique_slug (slug)");
        echo "<p style='color: green;'>✅ Slug column added!</p>";
    } else {
        echo "<p style='color: green;'>✅ Slug column exists!</p>";
    }
    
    // Generate slugs
    $products = fetchAll("SELECT id, product_code, title, slug FROM products WHERE slug IS NULL OR slug = ''");
    
    if (!empty($products)) {
        echo "<p>Generating slugs for " . count($products) . " products...</p>";
        
        foreach ($products as $product) {
            $slug = createSlug($product['title']);
            
            // Check if slug exists
            $existing = fetchSingle("SELECT id FROM products WHERE slug = ? AND id != ?", [$slug, $product['id']]);
            if ($existing) {
                $slug = $slug . '-' . $product['id'];
            }
            
            executeQuery("UPDATE products SET slug = ? WHERE id = ?", [$slug, $product['id']]);
            echo "<p>✅ {$product['title']} → {$slug}</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ All products have slugs!</p>";
    }
    echo "</div>";

    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🔗 Step 2: Update Product Links</h2>";
    
    // Get all products with slugs
    $allProducts = fetchAll("SELECT product_code, slug FROM products WHERE status = 'active' AND slug IS NOT NULL");
    $codeToSlug = [];
    foreach ($allProducts as $product) {
        $codeToSlug[$product['product_code']] = $product['slug'];
    }
    
    // Update files
    $files = ['index.php', 'hair-care.php', 'skin-care.php', 'health-care.php', 'personal-care.php'];
    $totalUpdates = 0;
    
    foreach ($files as $filename) {
        if (!file_exists($filename)) continue;
        
        $content = file_get_contents($filename);
        $originalContent = $content;
        $fileUpdates = 0;
        
        foreach ($codeToSlug as $code => $slug) {
            $pattern = '/product-details\.php\?product=' . preg_quote($code, '/') . '(?=["\'\s&])/';
            $replacement = 'product-details.php?product=' . $slug;
            
            $matches = preg_match_all($pattern, $content);
            if ($matches > 0) {
                $content = preg_replace($pattern, $replacement, $content);
                $fileUpdates += $matches;
            }
        }
        
        if ($content !== $originalContent) {
            file_put_contents($filename, $content);
            echo "<p style='color: green;'>✅ Updated $filename ($fileUpdates links)</p>";
            $totalUpdates += $fileUpdates;
        } else {
            echo "<p style='color: #6c757d;'>ℹ No updates needed for $filename</p>";
        }
    }
    
    echo "<p><strong>Total links updated: $totalUpdates</strong></p>";
    echo "</div>";

    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🎉 Success!</h2>";
    echo "<p><strong>Your product URLs are now SEO-friendly!</strong></p>";
    
    // Show examples
    $examples = fetchAll("SELECT product_code, title, slug FROM products WHERE status = 'active' AND slug IS NOT NULL LIMIT 3");
    
    echo "<h3>📋 Examples of your new URLs:</h3>";
    foreach ($examples as $product) {
        $oldUrl = "product-details.php?product=" . $product['product_code'];
        $newUrl = "product-details.php?product=" . $product['slug'];
        
        echo "<div style='background: white; padding: 10px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<p><strong>" . htmlspecialchars($product['title']) . "</strong></p>";
        echo "<p>Old: <span style='color: #6c757d; font-family: monospace;'>$oldUrl</span></p>";
        echo "<p>New: <a href='$newUrl' target='_blank' style='color: #28a745; font-family: monospace; font-weight: bold;'>$newUrl</a></p>";
        echo "</div>";
    }
    echo "</div>";

    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center;'>";
    echo "<h2>🧪 Test Your New URLs</h2>";
    echo "<p>Click these links to see your new SEO-friendly URLs in action:</p>";
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 20px 0;'>";
    echo "<a href='hair-care.php' target='_blank' style='background: #007bff; color: white; padding: 15px; text-decoration: none; border-radius: 5px;'>💇 Hair Care</a>";
    echo "<a href='skin-care.php' target='_blank' style='background: #28a745; color: white; padding: 15px; text-decoration: none; border-radius: 5px;'>🧴 Skin Care</a>";
    echo "<a href='health-care.php' target='_blank' style='background: #17a2b8; color: white; padding: 15px; text-decoration: none; border-radius: 5px;'>💊 Health Care</a>";
    echo "<a href='personal-care.php' target='_blank' style='background: #ffc107; color: black; padding: 15px; text-decoration: none; border-radius: 5px;'>🧼 Personal Care</a>";
    echo "</div>";
    echo "</div>";

    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>✅ What Changed</h2>";
    echo "<ul>";
    echo "<li>✅ Added 'slug' column to products table</li>";
    echo "<li>✅ Generated SEO-friendly slugs from product titles</li>";
    echo "<li>✅ Updated all product links to use slugs instead of codes</li>";
    echo "<li>✅ Maintained backward compatibility (old URLs still work)</li>";
    echo "<li>✅ Cart functionality preserved completely</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p style='color: #721c24;'>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
a { text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
