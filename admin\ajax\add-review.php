<?php
session_start();
require_once '../../config/dbconfig.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

header('Content-Type: application/json');

try {
    $product_id = intval($_POST['product_id'] ?? 0);
    $customer_name = trim($_POST['customer_name'] ?? '');
    $customer_designation = trim($_POST['customer_designation'] ?? '');
    $rating = floatval($_POST['rating'] ?? 0);
    $comment = trim($_POST['comment'] ?? '');
    $status = $_POST['status'] ?? 'pending';
    
    // Validation
    if ($product_id <= 0) {
        throw new Exception('Invalid product ID');
    }
    
    if (empty($customer_name)) {
        throw new Exception('Customer name is required');
    }
    
    if ($rating < 1 || $rating > 5) {
        throw new Exception('Rating must be between 1 and 5');
    }
    
    if (empty($comment)) {
        throw new Exception('Review comment is required');
    }
    
    // Check if product exists
    $product = fetchSingle("SELECT id FROM products WHERE id = ?", [$product_id]);
    if (!$product) {
        throw new Exception('Product not found');
    }
    
    // Insert review
    $sql = "INSERT INTO product_reviews (product_id, customer_name, customer_designation, rating, comment, status) 
            VALUES (?, ?, ?, ?, ?, ?)";
    
    $review_id = insertData($sql, [$product_id, $customer_name, $customer_designation, $rating, $comment, $status]);
    
    if ($review_id) {
        echo json_encode([
            'success' => true,
            'message' => 'Review added successfully',
            'review_id' => $review_id
        ]);
    } else {
        throw new Exception('Failed to add review');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
