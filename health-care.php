<?php
require_once 'config/dbconfig.php';
include 'header.php';
?>

<!-- FontAwesome CDN for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<?php

// Get health care products (syrups and health supplements, excluding face wash and hair products)
$health_care_query = "
    SELECT DISTINCT p.*,
           GROUP_CONCAT(c.name SEPARATOR ', ') as categories,
           COALESCE(p.sale_price, p.price) as display_price,
           CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price
                THEN p.price ELSE NULL END as original_price
    FROM products p
    LEFT JOIN product_categories pc ON p.id = pc.product_id
    LEFT JOIN categories c ON pc.category_id = c.id
    WHERE p.status = 'active'
    AND (c.name = 'Health Care'
         OR p.title LIKE '%syrup%'
         OR p.title LIKE '%Syrup%'
         OR p.title LIKE '%health%'
         OR p.title LIKE '%Health%'
         OR p.title LIKE '%supplement%'
         OR p.title LIKE '%Supplement%'
         OR p.title LIKE '%vitamin%'
         OR p.title LIKE '%Vitamin%'
         OR p.title LIKE '%medicine%'
         OR p.title LIKE '%Medicine%'
         OR p.title LIKE '%kids%'
         OR p.title LIKE '%Kids%'
         OR p.title LIKE '%children%'
         OR p.title LIKE '%Children%'
         OR p.title LIKE '%child%'
         OR p.title LIKE '%Child%')
    AND p.title NOT LIKE '%wash%'
    AND p.title NOT LIKE '%Wash%'
    AND p.title NOT LIKE '%face%'
    AND p.title NOT LIKE '%Face%'
    AND p.title NOT LIKE '%hair%'
    AND p.title NOT LIKE '%Hair%'
    AND p.title NOT LIKE '%serum%'
    AND p.title NOT LIKE '%Serum%'
    AND p.title NOT LIKE '%shampoo%'
    AND p.title NOT LIKE '%Shampoo%'
    AND p.title NOT LIKE '%conditioner%'
    AND p.title NOT LIKE '%Conditioner%'
    GROUP BY p.id
    ORDER BY p.created_at ASC
";

$health_products = fetchAll($health_care_query);
?>
<br><br><br><br>
<!-- Health Care Hero Banner -->
<section class="health-hero-banner position-relative">
    <div class="container">
        <div class="row align-items-center py-5">
            <div class="col-lg-6">
                <div class="health-hero-content">
                    <div class="hero-badge mb-3">
                        <span class="badge bg-success fs-6 px-3 py-2 rounded-pill">
                            <i class="fas fa-leaf me-2"></i>Natural Health Solutions
                        </span>
                    </div>
                    <h1 class="display-4 fw-bold text-dark mb-4">
                        Your Wellness, <br>
                        <span class="text-success">Naturally Enhanced</span>
                    </h1>
                    <p class="lead text-muted mb-4">
                        Discover our premium collection of natural health care products, including organic syrups,
                        herbal supplements, and wellness solutions crafted with the finest natural ingredients.
                    </p>
                    <div class="health-features">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="feature-item d-flex align-items-center">
                                    <i class="fas fa-leaf text-success me-3 fs-5"></i>
                                    <span class="text-dark">100% Natural Ingredients</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item d-flex align-items-center">
                                    <i class="fas fa-award text-warning me-3 fs-5"></i>
                                    <span class="text-dark">Quality Assured</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item d-flex align-items-center">
                                    <i class="fas fa-heart text-danger me-3 fs-5"></i>
                                    <span class="text-dark">Wellness Focused</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item d-flex align-items-center">
                                    <i class="fas fa-users text-primary me-3 fs-5"></i>
                                    <span class="text-dark">Trusted by Families</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="health-hero-image text-center">
                    <img src="assets/images/health_img.png" alt="Health Care Products"
                         class="img-fluid rounded-3 shadow" style="max-width: 600px; height: auto;">
                </div>
            </div>
        </div>
    </div>
</section>
<br><br>
<!-- Health Products Section -->
<section id="health-products" class="shop-section section section-padding bg-light">
    <div class="container">
        <!-- Section Header -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <div class="section-header">
                    <span class="badge bg-success fs-6 px-3 py-2 rounded-pill mb-3">
                        <i class="fas fa-pills me-2"></i>Health Care Collection
                    </span>
                    <h2 class="display-5 fw-bold text-dark mb-3">
                        <i class="fas fa-heartbeat text-success me-3"></i>Natural Health Solutions
                    </h2>
                    <p class="lead text-muted mx-auto" style="max-width: 600px;">
                        Explore our carefully curated selection of natural syrups, herbal supplements,
                        and wellness products designed to support your health journey.
                    </p>
                </div>
            </div>
        </div>

        <!-- Breadcrumb -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-white rounded-3 p-3 shadow-sm">
                        <li class="breadcrumb-item">
                            <a href="index.php" class="text-decoration-none text-success">
                                <i class="fas fa-home me-1"></i>Home
                            </a>
                        </li>
                        <li class="breadcrumb-item active text-dark" aria-current="page">Health Care</li>
                    </ol>
                </nav>
            </div>
        </div>


        <div class="row">
            <?php if (empty($health_products)): ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <h4>No Health Care Products Available</h4>
                        <p>We're working on adding more products. Please check back soon!</p>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($health_products as $product):
                    $review_count = rand(5, 25);
                ?>
                    <div class="col-6 col-lg-4 mb-4 mb-sm-9">
                        <!--== Start Product Item ==-->
                        <div class="product-item">
                            <div class="product-thumb">
                                <a class="d-block" href="product-details.php?product=<?php echo htmlspecialchars($product['slug'] ?: $product['product_code']); ?>">
                                    <img src="<?php echo htmlspecialchars($product['image'] ?: 'assets/images/shop/default.png'); ?>"
                                         width="370" height="450" alt="<?php echo htmlspecialchars($product['title']); ?>">
                                </a>
                                <span class="flag-new">new</span>
                                <div class="product-action">
                                    <button type="button" class="product-action-btn action-btn-cart"
                                            onclick="addToCart(<?php echo $product['id']; ?>)">
                                        <span>Add to cart</span>
                                    </button>
                                </div>
                            </div>
                            <div class="product-info">
                                <div class="product-rating">
                                    <div class="rating">
                                        <?php
                                        // Get actual average rating for this product
                                        $product_reviews = fetchAll("SELECT rating FROM product_reviews WHERE product_id = ? AND status = 'approved'", [$product['id']]);
                                        $avg_rating = !empty($product_reviews) ? array_sum(array_column($product_reviews, 'rating')) / count($product_reviews) : 5;
                                        $review_count = count($product_reviews);

                                        // Show all 5 stars, but highlight only the rating amount
                                        for ($i = 1; $i <= 5; $i++):
                                            if ($i <= $avg_rating): ?>
                                                <i class="fa fa-star" style="color: #ffc107;"></i>
                                            <?php else: ?>
                                                <i class="fa fa-star" style="color: #ddd;"></i>
                                            <?php endif;
                                        endfor; ?>
                                    </div>
                                    <div class="reviews text-muted small"><?php echo $review_count; ?> review<?php echo $review_count != 1 ? 's' : ''; ?></div>
                                </div>
                                <h4 class="title">
                                    <a href="product-details.php?product=<?php echo htmlspecialchars($product['slug'] ?: $product['product_code']); ?>">
                                        <?php echo htmlspecialchars($product['title']); ?>
                                    </a>
                                </h4>
                                <div class="prices">
                                    <?php if ($product['original_price']): ?>
                                        <span class="price-old">Rs. <?php echo number_format($product['original_price'], 0); ?></span>
                                        <span class="price">Rs. <?php echo number_format($product['display_price'], 0); ?></span>
                                    <?php else: ?>
                                        <span class="price">Rs. <?php echo number_format($product['display_price'], 0); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="product-action-bottom">
                                <button type="button" class="product-action-btn action-btn-quick-view"
                                        data-bs-toggle="modal" data-bs-target="#action-QuickViewModal"
                                        data-product-id="<?php echo $product['id']; ?>">
                                    <i class="fa fa-expand"></i>
                                </button>
                                <button type="button" class="product-action-btn action-btn-wishlist"
                                        data-bs-toggle="modal" data-bs-target="#action-WishlistModal">
                                    <i class="fa fa-heart-o"></i>
                                </button>
                                <button type="button" class="product-action-btn action-btn-cart"
                                        onclick="addToCart(<?php echo $product['id']; ?>)">
                                    <span>Add to cart</span>
                                </button>
                            </div>
                        </div>
                        <!--== End Product Item ==-->
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'includes/search-modal.php'; ?>
<?php include 'includes/offcanvas-menu.php'; ?>

<?php include 'footer.php'; ?>

<!-- Health Care Page Custom Styles -->
<style>
.health-hero-banner {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 2rem 0;
}

.health-hero-content {
    padding: 1rem 0;
}

.health-hero-image img {
    /* Hover effect removed */
}

.hero-badge .badge {
    font-weight: 500;
    letter-spacing: 0.5px;
}

.feature-item {
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateX(5px);
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    transition: all 0.3s ease;
}

.btn-success:hover {
    background: linear-gradient(45deg, #20c997, #28a745);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.btn-outline-success {
    border: 2px solid #28a745;
    color: #28a745;
    transition: all 0.3s ease;
}

.btn-outline-success:hover {
    background: #28a745;
    border-color: #28a745;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.section-header .badge {
    font-weight: 500;
    letter-spacing: 0.5px;
}

.shop-header {
    border-left: 4px solid #28a745;
}

.stat-number {
    font-size: 1.5rem;
}

.product-item {
    transition: all 0.3s ease;
    border-radius: 15px;
    overflow: hidden;
    border: 1px solid transparent;
}

.product-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.product-item .product-info {
    padding: 15px;
}

.product-item .product-action-bottom {
    padding: 0 15px 15px 15px;
}

.breadcrumb {
    border-left: 4px solid #28a745;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #28a745;
    font-weight: bold;
}

/* Smooth scrolling */
.smooth-scroll {
    scroll-behavior: smooth;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .health-hero-content {
        padding: 1rem 0;
        text-align: center;
    }

    .health-hero-image {
        margin-top: 2rem;
    }

    .health-hero-image img {
        max-width: 400px !important;
    }

    .display-4 {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .health-stats {
        flex-direction: column;
        gap: 1rem !important;
    }

    .stat-item {
        text-align: left !important;
    }
}
</style>

<!-- Health Care Page JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add animation on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe product items
    document.querySelectorAll('.product-item').forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        item.style.transition = 'all 0.6s ease';
        observer.observe(item);
    });
});

// Add to cart function for health products
function addToCart(productId) {
    // Show loading state
    const cartButtons = document.querySelectorAll(`[onclick="addToCart(${productId})"]`);
    cartButtons.forEach(btn => {
        btn.disabled = true;
        btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Adding...';
    });

    // Send AJAX request
    fetch('cart/add-to-cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: 1
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success notification
            showHealthNotification('Health product added to cart successfully!', 'success');

            // Update cart count if function exists
            if (typeof updateCartCount === 'function') {
                updateCartCount(data.cart_count);
            }
        } else {
            showHealthNotification('Error adding to cart: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showHealthNotification('Error adding to cart. Please try again.', 'error');
    })
    .finally(() => {
        // Reset button state
        cartButtons.forEach(btn => {
            btn.disabled = false;
            btn.innerHTML = '<span>Add to cart</span>';
        });
    });
}

// Health-themed notification function
function showHealthNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>

<?php include 'includes/scripts.php'; ?>
