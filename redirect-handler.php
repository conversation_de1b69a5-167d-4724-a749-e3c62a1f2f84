<?php
// Simple redirect handler for pages without .php extension
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);

// Remove the base path if it exists
$base_path = '/zia1';
if (strpos($path, $base_path) === 0) {
    $path = substr($path, strlen($base_path));
}

// Remove leading slash
$path = ltrim($path, '/');

// Define redirects
$redirects = [
    'index' => 'index.php',
    'hair-care' => 'hair-care.php',
    'skin-care' => 'skin-care.php',
    'health-care' => 'health-care.php',
    'personal-care' => 'personal-care.php'
];

// Check if we need to redirect
if (isset($redirects[$path])) {
    $redirect_url = $base_path . '/' . $redirects[$path];
    header("Location: $redirect_url", true, 301);
    exit();
}

// If no redirect needed, show 404
http_response_code(404);
echo "Page not found";
?>
