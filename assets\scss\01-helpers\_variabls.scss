// Font Family
$inter: "Inter", sans-serif;

// Responsive Variables
$extraBig-device: "only screen and (min-width: 1550px)";
$laptop-device: "only screen and (min-width: 1200px) and (max-width: 1549px)";
$desktop-device: "only screen and (min-width: 992px) and (max-width: 1199px)";
$tablet-device: "only screen and (min-width: 768px) and (max-width: 991px)";
$large-mobile: "only screen and (max-width: 767px)";
$small-mobile: "only screen and (max-width: 575px)";
$extra-small-mobile: "only screen and (max-width: 479px)";

//===============================
// Bootstrap Variables Overright
//===============================

// Color
$primary: #FF6565;
$secondary: #6C757D;
$success: #4CAF50;
$danger: #F44336;
$warning: #FFC107;
$info: #17A2B8;
$light: #F8F9FA;
$dark: #231942;
//
$body-color: #364958;
$headings-color: #231942;
//
$white: #FFFFFF;
$black: #000000;
$grey: #FAFAFA;

// theme colors map
$theme-colors: (
  "primary": $primary,
  "secondary": $secondary,
  "success": $success,
  "danger": $danger,
  "warning": $warning,
  "info": $info,
  "light": $light,
  "dark": $dark,
);

// Options
$enable-negative-margins: true;
// Spacing
$spacer: 1rem;
$spacers: (
  0: 0,
  1: $spacer * 0.3125,
  2: $spacer * 0.625,
  3: $spacer * 0.9375,
  4: $spacer * 1.25,
  5: $spacer * 1.625,
  6: $spacer * 1.875,
  7: $spacer * 2.1875,
  8: $spacer * 2.5,
  9: $spacer * 2.8125,
  10: $spacer * 3.125,
);
// Grid breakpoints
$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1600px,
);

// Grid containers
$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1200px,
  xxl: 1200px,
);
//
$grid-gutter-width: 30px;
//
$gutters: $spacers;
// Transition
$transition-base: all 0.5s ease 0s;

// Font Weight
$font-weight-light:           300;
$font-weight-normal:          400;
$font-weight-medium:          500;
$font-weight-semi-bold:       600;
$font-weight-bold:            700;
$font-weight-extra-bold:      800;
$font-weight-black:           900;

// Typography
$font-family-base: $inter;
$font-size-base: 1rem;
$font-weight-base: $font-weight-normal;
$line-height-base: 1.75;
//
$headings-font-family: $inter;
$headings-font-weight: $font-weight-medium;
$headings-line-height: 1.2;
