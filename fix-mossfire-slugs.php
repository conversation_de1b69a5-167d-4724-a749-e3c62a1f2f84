<?php
require_once 'config/dbconfig.php';

echo "<h1>🔧 Fix MOSSFIRE Product Slugs</h1>";

// Function to create URL-friendly slug
function createSlug($string) {
    // Convert to lowercase
    $slug = strtolower($string);
    
    // Replace spaces and special characters with hyphens
    $slug = preg_replace('/[^a-z0-9]+/', '-', $slug);
    
    // Remove leading/trailing hyphens
    $slug = trim($slug, '-');
    
    // Remove multiple consecutive hyphens
    $slug = preg_replace('/-+/', '-', $slug);
    
    return $slug;
}

// Get all MOSSFIRE products
$mossfire_products = fetchAll("SELECT * FROM products WHERE title LIKE '%MOSSFIRE%' OR title LIKE '%mossfire%' OR title LIKE '%Mossfire%'");

echo "<h2>Found " . count($mossfire_products) . " MOSSFIRE products:</h2>";

if (!empty($mossfire_products)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>ID</th><th>Product Code</th><th>Title</th><th>Current Slug</th><th>New Slug</th><th>Action</th>";
    echo "</tr>";
    
    foreach ($mossfire_products as $product) {
        $new_slug = createSlug($product['title']);
        
        // Make sure slug is unique
        $counter = 1;
        $original_slug = $new_slug;
        while (true) {
            $existing = fetchSingle("SELECT id FROM products WHERE slug = ? AND id != ?", [$new_slug, $product['id']]);
            if (!$existing) {
                break;
            }
            $new_slug = $original_slug . '-' . $counter;
            $counter++;
        }
        
        echo "<tr>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . $product['product_code'] . "</td>";
        echo "<td>" . htmlspecialchars($product['title']) . "</td>";
        echo "<td>" . ($product['slug'] ?: 'NO SLUG') . "</td>";
        echo "<td><strong>" . $new_slug . "</strong></td>";
        
        // Update the slug
        $result = executeQuery("UPDATE products SET slug = ? WHERE id = ?", [$new_slug, $product['id']]);
        
        if ($result) {
            echo "<td style='color: green;'>✅ Updated</td>";
        } else {
            echo "<td style='color: red;'>❌ Failed</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>✅ Verification - Updated Products:</h2>";
    
    // Get updated products
    $updated_products = fetchAll("SELECT * FROM products WHERE title LIKE '%MOSSFIRE%' OR title LIKE '%mossfire%' OR title LIKE '%Mossfire%'");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #e8f5e8;'>";
    echo "<th>ID</th><th>Product Code</th><th>Title</th><th>Slug</th><th>Test Link</th>";
    echo "</tr>";
    
    foreach ($updated_products as $product) {
        echo "<tr>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . $product['product_code'] . "</td>";
        echo "<td>" . htmlspecialchars($product['title']) . "</td>";
        echo "<td><strong>" . $product['slug'] . "</strong></td>";
        
        $test_url = "product-details.php?product=" . urlencode($product['slug']);
        echo "<td><a href='{$test_url}' target='_blank' style='color: #007bff; font-weight: bold;'>🔗 Test Link</a></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>🧪 Test the Fixed Links:</h2>";
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p><strong>Click these links to test if they show the correct products:</strong></p>";
    
    foreach ($updated_products as $product) {
        $test_url = "product-details.php?product=" . urlencode($product['slug']);
        echo "<p>📦 <a href='{$test_url}' target='_blank' style='color: #007bff; font-weight: bold;'>{$product['title']}</a></p>";
    }
    
    echo "<p style='margin-top: 20px;'><strong>Also test the personal care page:</strong></p>";
    echo "<p>🏪 <a href='personal-care.php' target='_blank' style='color: #28a745; font-weight: bold;'>Personal Care Page</a></p>";
    echo "</div>";
    
} else {
    echo "<p style='color: red;'>No MOSSFIRE products found!</p>";
}

echo "<h2>📋 Next Steps:</h2>";
echo "<ol>";
echo "<li>Click on the test links above to verify each product shows correctly</li>";
echo "<li>Go to the Personal Care page and click on each product</li>";
echo "<li>Test the 'Add to Cart' functionality on both products</li>";
echo "<li>If issues persist, check the database directly</li>";
echo "</ol>";
?>
