<?php
session_start();
require_once '../config/dbconfig.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    $session_id = session_id();
    
    if ($session_id) {
        // Clear cart items
        executeQuery("DELETE FROM cart_items WHERE session_id = ?", [$session_id]);
        executeQuery("DELETE FROM cart_sessions WHERE session_id = ?", [$session_id]);
        
        // Clear session cart count
        unset($_SESSION['cart_count']);
        
        echo json_encode([
            'success' => true,
            'message' => 'Cart cleared successfully',
            'cart_count' => 0
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'No session found'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error clearing cart: ' . $e->getMessage()
    ]);
}
?>
