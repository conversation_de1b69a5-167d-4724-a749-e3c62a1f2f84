RewriteEngine On

# Force .php extension to show in URLs
# Redirect URLs without .php extension to URLs with .php extension

# Redirect /product-details to /product-details.php (with query parameters)
RewriteCond %{QUERY_STRING} (.*)
RewriteRule ^product-details$ /product-details.php?%1 [R=301,L]

# Redirect /skin-care to /skin-care.php
RewriteRule ^skin-care$ /skin-care.php [R=301,L]

# Redirect /hair-care to /hair-care.php
RewriteRule ^hair-care$ /hair-care.php [R=301,L]

# Redirect /health-care to /health-care.php
RewriteRule ^health-care$ /health-care.php [R=301,L]

# Redirect /personal-care to /personal-care.php
RewriteRule ^personal-care$ /personal-care.php [R=301,L]

# Redirect /about-us to /about-us.php
RewriteRule ^about-us$ /about-us.php [R=301,L]

# Redirect /contact to /contact.php
RewriteRule ^contact$ /contact.php [R=301,L]

# Redirect /search to /search.php (with query parameters)
RewriteCond %{QUERY_STRING} (.*)
RewriteRule ^search$ /search.php?%1 [R=301,L]

# Redirect /cart to /cart.php
RewriteRule ^cart$ /cart.php [R=301,L]

# Redirect /checkout to /checkout.php
RewriteRule ^checkout$ /checkout.php [R=301,L]

# Redirect /login to /login.php
RewriteRule ^login$ /login.php [R=301,L]

# Redirect /my-account to /my-account.php
RewriteRule ^my-account$ /my-account.php [R=301,L]

# General rule for other pages (optional)
# This will redirect any page without .php to the .php version
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteRule ^([^.]+)$ /$1.php [R=301,L]
