<?php
session_start();
require_once '../../config/dbconfig.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

if ($_POST && isset($_POST['product_id']) && isset($_POST['status'])) {
    $product_id = intval($_POST['product_id']);
    $status = $_POST['status'];
    
    // Validate status
    if (!in_array($status, ['active', 'inactive'])) {
        echo json_encode(['success' => false, 'message' => 'Invalid status']);
        exit();
    }
    
    try {
        $updated = updateData("UPDATE products SET status = ? WHERE id = ?", [$status, $product_id]);
        
        if ($updated) {
            echo json_encode(['success' => true, 'message' => 'Product status updated successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update product status']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
}
?>
