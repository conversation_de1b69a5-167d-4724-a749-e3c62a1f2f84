/* Custom Cart and Checkout Styling */

/* Cart Page Improvements */
.cart-table-wrap {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.cart-table table {
    margin-bottom: 0;
}

.cart-table table thead {
    background: #f8f9fa;
}

.cart-table table thead th {
    border: none;
    padding: 20px 15px;
    font-weight: 600;
    color: #333;
    text-transform: uppercase;
    font-size: 14px;
    letter-spacing: 0.5px;
}

.cart-table table tbody td {
    padding: 20px 15px;
    vertical-align: middle;
    border-bottom: 1px solid #eee;
}

.cart-table table tbody tr:last-child td {
    border-bottom: none;
}

.product-thumbnail img {
    border-radius: 6px;
    transition: transform 0.3s ease;
}

.product-thumbnail img:hover {
    transform: scale(1.05);
}

.product-name h5 {
    margin-bottom: 8px;
    font-weight: 600;
}

.product-name h5 a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.product-name h5 a:hover {
    color: #FF6565;
}

.product-name .variation {
    margin-bottom: 5px;
    font-size: 13px;
    color: #666;
}

.product-name .offer {
    margin-bottom: 0;
    font-size: 12px;
    font-weight: 500;
}

.product-price .amount {
    font-weight: 600;
    color: #333;
    font-size: 16px;
}

/* Quantity Input Styling */
.quantity-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    overflow: hidden;
    width: fit-content;
    margin: 0 auto;
}

.qty-btn {
    background: #f8f9fa;
    border: none;
    width: 35px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
}

.qty-btn:hover {
    background: #FF6565;
    color: white;
}

.qty-btn:active {
    transform: scale(0.95);
}

.qty-input {
    width: 60px;
    height: 40px;
    border: none;
    text-align: center;
    font-weight: 600;
    background: white;
    color: #333;
    font-size: 14px;
}

.qty-input:focus {
    outline: none;
}

.product-total .amount {
    font-weight: 700;
    color: #FF6565;
    font-size: 18px;
}

.product-remove .btn {
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.product-remove .btn:hover {
    background-color: #dc3545;
    color: white;
    transform: scale(1.1);
}

/* Cart Actions */
.cart-shiping-update-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-top: 2px solid #f0f0f0;
    margin-top: 20px;
}

.cart-shiping-update .btn,
.cart-clear .btn {
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.cart-shiping-update .btn:hover,
.cart-clear .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Cart Totals */
.cart-calculate-area {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.cart-calculate-area h4 {
    background: #f8f9fa;
    margin: 0;
    padding: 20px 25px;
    font-weight: 600;
    color: #333;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid #eee;
}

.cart-calculate-item-inner {
    padding: 25px;
}

.cart-calculate-details-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 15px;
}

.cart-calculate-details-item:last-child {
    border-bottom: none;
}

.cart-calculate-total {
    border-top: 2px solid #FF6565 !important;
    padding-top: 15px !important;
    margin-top: 10px;
    font-weight: 700 !important;
    font-size: 18px !important;
    color: #333 !important;
}

.cart-calculate-area .btn {
    width: 100%;
    padding: 15px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-radius: 6px;
    margin-top: 20px;
    transition: all 0.3s ease;
}

.cart-calculate-area .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 101, 101, 0.3);
}

/* Empty Cart Styling */
.text-center.py-5 {
    padding: 60px 20px !important;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.text-center.py-5 .fa-shopping-cart {
    color: #ddd;
    margin-bottom: 20px;
}

.text-center.py-5 h3 {
    color: #666;
    margin-bottom: 15px;
}

.text-center.py-5 .text-muted {
    margin-bottom: 25px;
}

/* Checkout Progress Indicator */
.checkout-progress {
    background: #fff;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 25px;
    left: 50px;
    right: 50px;
    height: 2px;
    background: #e0e0e0;
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #e0e0e0;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background: #FF6565;
    color: white;
}

.step-title {
    font-size: 14px;
    font-weight: 500;
    color: #666;
    text-align: center;
}

.step.active .step-title {
    color: #FF6565;
    font-weight: 600;
}

/* Checkout Page Improvements */
.checkout-form {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.checkout-form h3 {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #FF6565;
    color: #333;
    font-weight: 600;
}

.checkout-form-list {
    margin-bottom: 20px;
}

.checkout-form-list label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.checkout-form-list .required {
    color: #dc3545;
}

.checkout-form-list input,
.checkout-form-list textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    background: #fff;
}

.checkout-form-list input:focus,
.checkout-form-list textarea:focus {
    border-color: #FF6565;
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 101, 101, 0.1);
}

.checkout-form-list textarea {
    height: 100px;
    resize: vertical;
}

/* Form Validation Styling */
.checkout-form-list input.error,
.checkout-form-list textarea.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.error-message {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    font-weight: 500;
}

.checkout-form-list input:valid,
.checkout-form-list textarea:valid {
    border-color: #28a745;
}

/* Loading States */
button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Order Summary */
.your-order {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.your-order h3 {
    background: #f8f9fa;
    margin: 0;
    padding: 20px 25px;
    font-weight: 600;
    color: #333;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid #eee;
}

.your-order-table {
    padding: 25px;
}

.your-order-table table {
    margin-bottom: 0;
}

.your-order-table table th {
    border: none;
    border-bottom: 2px solid #f0f0f0;
    padding: 15px 0;
    font-weight: 600;
    color: #333;
    text-transform: uppercase;
    font-size: 13px;
    letter-spacing: 0.5px;
}

.your-order-table table td {
    border: none;
    border-bottom: 1px solid #f5f5f5;
    padding: 15px 0;
    vertical-align: middle;
}

.your-order-table table tbody tr:last-child td {
    border-bottom: none;
}

.cart-product-name {
    font-weight: 500;
    color: #333;
}

.cart-product-name small {
    display: block;
    margin-top: 5px;
}

.product-quantity {
    color: #FF6565;
    font-weight: 600;
}

.cart-product-total .amount {
    font-weight: 600;
    color: #333;
}

.your-order-table tfoot th,
.your-order-table tfoot td {
    border-top: 2px solid #f0f0f0;
    padding: 15px 0;
    font-weight: 600;
}

.order-total th,
.order-total td {
    border-top: 2px solid #FF6565 !important;
    color: #333 !important;
    font-size: 16px !important;
    font-weight: 700 !important;
}

/* Payment Methods */
.payment-method {
    padding: 25px;
    border-top: 1px solid #eee;
}

.payment-accordion-toggle {
    margin-bottom: 15px;
    padding: 15px;
    border: 2px solid #f0f0f0;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.payment-accordion-toggle:hover {
    border-color: #FF6565;
}

.payment-accordion-toggle input[type="radio"] {
    margin-right: 10px;
}

.payment-accordion-toggle label {
    margin: 0;
    font-weight: 600;
    color: #333;
    cursor: pointer;
}

.payment-content {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #f0f0f0;
    color: #666;
    font-size: 14px;
}

.order-button-payment .btn {
    width: 100%;
    padding: 15px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-radius: 6px;
    margin-top: 20px;
    transition: all 0.3s ease;
}

.order-button-payment .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 101, 101, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .cart-shiping-update-wrapper {
        flex-direction: column;
        gap: 15px;
    }

    .cart-table table {
        font-size: 14px;
    }

    .cart-table table thead {
        display: none;
    }

    .cart-table table tbody tr {
        display: block;
        border: 1px solid #eee;
        border-radius: 8px;
        margin-bottom: 15px;
        padding: 15px;
        background: #fff;
    }

    .cart-table table tbody td {
        display: block;
        text-align: left !important;
        padding: 8px 0;
        border: none;
        position: relative;
        padding-left: 50%;
    }

    .cart-table table tbody td:before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        width: 45%;
        font-weight: 600;
        color: #333;
        text-transform: uppercase;
        font-size: 12px;
    }

    .product-thumbnail {
        text-align: center !important;
        padding-left: 0 !important;
    }

    .product-thumbnail:before {
        display: none;
    }

    .quantity-wrapper {
        margin: 0;
    }

    .checkout-form,
    .your-order {
        margin-bottom: 20px;
    }

    .checkout-form,
    .your-order-table,
    .payment-method {
        padding: 20px;
    }

    .progress-steps {
        flex-direction: column;
        gap: 20px;
    }

    .progress-steps::before {
        display: none;
    }

    .step {
        flex-direction: row;
        gap: 15px;
    }

    .step-number {
        margin-bottom: 0;
    }
}

/* Alert Improvements */
.alert {
    border-radius: 6px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 25px;
    font-weight: 500;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert i {
    margin-right: 8px;
}
