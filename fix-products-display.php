<?php
// Complete fix for product display issues
require_once 'config/dbconfig.php';

echo "<h2>🔧 Fixing Product Display Issues</h2>";

try {
    // 1. Run database migration first
    echo "<h3>1. Database Migration Check</h3>";
    
    $new_fields = ['key_benefits', 'ingredients', 'how_to_use', 'sale_price'];
    $missing_fields = [];
    
    foreach ($new_fields as $field) {
        $exists = fetchAll("SHOW COLUMNS FROM products LIKE '$field'");
        if (empty($exists)) {
            $missing_fields[] = $field;
        }
    }
    
    if (!empty($missing_fields)) {
        echo "<p style='color: orange;'>⚠ Missing fields: " . implode(', ', $missing_fields) . "</p>";
        echo "<p>Adding missing fields...</p>";
        
        if (in_array('key_benefits', $missing_fields)) {
            executeQuery("ALTER TABLE products ADD COLUMN key_benefits TEXT NULL AFTER description");
            echo "<p>✅ Added key_benefits field</p>";
        }
        
        if (in_array('ingredients', $missing_fields)) {
            executeQuery("ALTER TABLE products ADD COLUMN ingredients TEXT NULL AFTER key_benefits");
            echo "<p>✅ Added ingredients field</p>";
        }
        
        if (in_array('how_to_use', $missing_fields)) {
            executeQuery("ALTER TABLE products ADD COLUMN how_to_use TEXT NULL AFTER ingredients");
            echo "<p>✅ Added how_to_use field</p>";
        }
        
        if (in_array('sale_price', $missing_fields)) {
            executeQuery("ALTER TABLE products ADD COLUMN sale_price DECIMAL(10,2) NULL AFTER price");
            echo "<p>✅ Added sale_price field</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ All database fields exist</p>";
    }
    
    // 2. Check and create categories
    echo "<h3>2. Categories Setup</h3>";
    
    $categories = fetchAll("SELECT * FROM categories WHERE status = 'active'");
    
    if (empty($categories)) {
        echo "<p>Creating default categories...</p>";
        
        $default_categories = [
            ['Hair Care', 'Products for hair care and styling'],
            ['Skin Care', 'Products for skin care and beauty'],
            ['Health Care', 'Health supplements and syrups']
        ];
        
        foreach ($default_categories as $cat) {
            executeQuery("INSERT INTO categories (name, description, status) VALUES (?, ?, 'active')", $cat);
            echo "<p>✅ Created category: " . $cat[0] . "</p>";
        }
        
        $categories = fetchAll("SELECT * FROM categories WHERE status = 'active'");
    }
    
    echo "<p style='color: green;'>✅ Found " . count($categories) . " categories</p>";
    
    // 3. Assign products to categories
    echo "<h3>3. Product Category Assignment</h3>";
    
    $products_without_categories = fetchAll("
        SELECT p.* 
        FROM products p 
        LEFT JOIN product_categories pc ON p.id = pc.product_id 
        WHERE pc.product_id IS NULL AND p.status = 'active'
    ");
    
    if (!empty($products_without_categories)) {
        echo "<p>Assigning " . count($products_without_categories) . " products to categories...</p>";
        
        // Get category IDs
        $category_map = [];
        foreach ($categories as $cat) {
            $category_map[strtolower($cat['name'])] = $cat['id'];
        }
        
        foreach ($products_without_categories as $product) {
            $title = strtolower($product['title']);
            $assigned = false;
            
            // Hair Care products
            if (strpos($title, 'hair') !== false || 
                strpos($title, 'shampoo') !== false || 
                strpos($title, 'serum') !== false ||
                strpos($title, 'oil') !== false) {
                
                if (isset($category_map['hair care'])) {
                    executeQuery("INSERT IGNORE INTO product_categories (product_id, category_id) VALUES (?, ?)", 
                               [$product['id'], $category_map['hair care']]);
                    echo "<p>✅ " . htmlspecialchars($product['title']) . " → Hair Care</p>";
                    $assigned = true;
                }
            }
            
            // Skin Care products
            if (strpos($title, 'face') !== false || 
                strpos($title, 'skin') !== false || 
                strpos($title, 'wash') !== false || 
                strpos($title, 'cream') !== false) {
                
                if (isset($category_map['skin care'])) {
                    executeQuery("INSERT IGNORE INTO product_categories (product_id, category_id) VALUES (?, ?)", 
                               [$product['id'], $category_map['skin care']]);
                    echo "<p>✅ " . htmlspecialchars($product['title']) . " → Skin Care</p>";
                    $assigned = true;
                }
            }
            
            // Health Care products
            if (strpos($title, 'syrup') !== false || 
                strpos($title, 'health') !== false || 
                strpos($title, 'vitamin') !== false ||
                strpos($title, 'kids') !== false ||
                strpos($title, 'children') !== false) {
                
                if (isset($category_map['health care'])) {
                    executeQuery("INSERT IGNORE INTO product_categories (product_id, category_id) VALUES (?, ?)", 
                               [$product['id'], $category_map['health care']]);
                    echo "<p>✅ " . htmlspecialchars($product['title']) . " → Health Care</p>";
                    $assigned = true;
                }
            }
            
            // If no category assigned, assign to Hair Care as default
            if (!$assigned && isset($category_map['hair care'])) {
                executeQuery("INSERT IGNORE INTO product_categories (product_id, category_id) VALUES (?, ?)", 
                           [$product['id'], $category_map['hair care']]);
                echo "<p>✅ " . htmlspecialchars($product['title']) . " → Hair Care (default)</p>";
            }
        }
    } else {
        echo "<p style='color: green;'>✅ All products already have categories</p>";
    }
    
    // 4. Add sample content to products
    echo "<h3>4. Adding Sample Content</h3>";
    
    $products_without_content = fetchAll("
        SELECT * FROM products 
        WHERE (key_benefits IS NULL OR key_benefits = '') 
        AND status = 'active'
    ");
    
    if (!empty($products_without_content)) {
        echo "<p>Adding sample content to " . count($products_without_content) . " products...</p>";
        
        foreach ($products_without_content as $product) {
            $title = strtolower($product['title']);
            
            if (strpos($title, 'hair') !== false || strpos($title, 'shampoo') !== false || strpos($title, 'serum') !== false) {
                $benefits = "• Promotes healthy hair growth\n• Reduces hair fall and breakage\n• Strengthens hair follicles\n• Adds natural shine and volume\n• Suitable for all hair types";
                $ingredients = "Natural oils, Vitamin E, Biotin, Keratin proteins, Essential amino acids, Herbal extracts";
                $usage = "1. Apply a small amount to clean, damp hair\n2. Massage gently into scalp and hair\n3. Leave for 2-3 minutes\n4. Rinse thoroughly with water\n5. Use 2-3 times per week for best results";
            } elseif (strpos($title, 'face') !== false || strpos($title, 'skin') !== false || strpos($title, 'wash') !== false) {
                $benefits = "• Deep cleanses and purifies skin\n• Removes dirt and impurities\n• Maintains skin's natural moisture\n• Suitable for daily use\n• Gentle on sensitive skin";
                $ingredients = "Natural cleansing agents, Aloe vera extract, Vitamin C, Glycerin, Essential oils, Herbal extracts";
                $usage = "1. Wet your face with lukewarm water\n2. Apply a small amount to your palm\n3. Gently massage in circular motions\n4. Rinse thoroughly with water\n5. Pat dry with a clean towel\n6. Use twice daily for best results";
            } elseif (strpos($title, 'syrup') !== false || strpos($title, 'health') !== false) {
                $benefits = "• Boosts immune system naturally\n• Provides essential nutrients\n• Safe for regular consumption\n• Made with natural ingredients\n• No artificial preservatives";
                $ingredients = "Natural fruit extracts, Vitamins, Minerals, Herbal concentrates, Natural sweeteners";
                $usage = "1. Shake well before use\n2. Take 1-2 teaspoons daily\n3. Can be taken with water or directly\n4. Best taken after meals\n5. Store in a cool, dry place\n6. Consult physician for children under 2 years";
            } else {
                $benefits = "• Made with premium natural ingredients\n• Safe and effective formula\n• Dermatologically tested\n• Suitable for regular use\n• No harmful chemicals";
                $ingredients = "Premium natural ingredients, Essential vitamins, Herbal extracts, Natural preservatives";
                $usage = "1. Read instructions carefully before use\n2. Apply as directed on packaging\n3. Use regularly for best results\n4. Store in a cool, dry place\n5. Discontinue if irritation occurs";
            }
            
            executeQuery("UPDATE products SET key_benefits = ?, ingredients = ?, how_to_use = ? WHERE id = ?", 
                       [$benefits, $ingredients, $usage, $product['id']]);
            
            echo "<p>✅ Added content to: " . htmlspecialchars($product['title']) . "</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ All products already have content</p>";
    }
    
    // 5. Test queries
    echo "<h3>5. Testing Product Queries</h3>";
    
    $total_products = fetchSingle("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
    echo "<p><strong>Total Active Products:</strong> " . $total_products['count'] . "</p>";
    
    foreach ($categories as $category) {
        $count = fetchSingle("
            SELECT COUNT(DISTINCT p.id) as count 
            FROM products p 
            JOIN product_categories pc ON p.id = pc.product_id 
            WHERE pc.category_id = ? AND p.status = 'active'
        ", [$category['id']]);
        
        echo "<p><strong>" . $category['name'] . ":</strong> " . $count['count'] . " products</p>";
    }
    
    echo "<h3>6. 🎉 Success!</h3>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p><strong>✅ All issues have been fixed!</strong></p>";
    echo "<p><strong>Test your website now:</strong></p>";
    echo "<ul>";
    echo "<li><a href='index.php' target='_blank' style='color: #155724; font-weight: bold;'>🏠 Home Page</a></li>";
    echo "<li><a href='hair-care.php' target='_blank' style='color: #155724; font-weight: bold;'>💇 Hair Care Products</a></li>";
    echo "<li><a href='skin-care.php' target='_blank' style='color: #155724; font-weight: bold;'>🧴 Skin Care Products</a></li>";
    echo "<li><a href='health-care.php' target='_blank' style='color: #155724; font-weight: bold;'>💊 Health Care Products</a></li>";
    echo "<li><a href='product-details.php?product=PROD001' target='_blank' style='color: #155724; font-weight: bold;'>📄 Sample Product Details</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and try again.</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
h3 { border-bottom: 2px solid #ddd; padding-bottom: 5px; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
a { text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
