<?php
/**
 * Add Product Ratings Script
 * This script adds sample ratings and reviews to products
 */

require_once 'config/dbconfig.php';

echo "<h1>⭐ Add Product Ratings</h1>";
echo "<p>This script will add sample ratings and reviews to your products.</p>";
echo "<hr>";

if ($_POST['action'] ?? '' === 'add_ratings') {
    try {
        $pdo->beginTransaction();
        
        echo "<h2>Adding Sample Ratings and Reviews...</h2>";
        
        // Get all products
        $products = fetchAll("SELECT id, product_code, title FROM products WHERE status = 'active'");
        
        if (empty($products)) {
            echo "<p style='color: red;'>❌ No products found! Please add products first.</p>";
            exit;
        }
        
        // Sample customer data for reviews
        $sample_customers = [
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => 'Fatima Khan', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON><PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => 'Ayesha Siddique', 'email' => '<EMAIL>'],
            ['name' => 'Omar Farooq', 'email' => '<EMAIL>']
        ];
        
        // Sample review comments
        $sample_reviews = [
            5 => [
                "Excellent product! Highly recommended. Works exactly as described.",
                "Amazing quality! I've been using this for months and love the results.",
                "Perfect! This product exceeded my expectations. Will definitely buy again.",
                "Outstanding quality and fast delivery. Very satisfied with my purchase.",
                "Love this product! Natural ingredients and great results. 5 stars!"
            ],
            4 => [
                "Very good product. Works well but took some time to see results.",
                "Good quality product. Satisfied with the purchase overall.",
                "Nice product with natural ingredients. Would recommend to others.",
                "Good value for money. Product quality is quite good.",
                "Pretty good product. Does what it promises. Happy with it."
            ],
            3 => [
                "Average product. It's okay but nothing special.",
                "Decent quality but expected better results.",
                "It's an okay product. Does the job but could be better."
            ]
        ];
        
        // Rating distribution for different products
        $rating_patterns = [
            'high' => [5 => 60, 4 => 30, 3 => 10], // Mostly 5 and 4 stars
            'good' => [5 => 40, 4 => 45, 3 => 15], // Balanced good ratings
            'mixed' => [5 => 30, 4 => 40, 3 => 30]  // More mixed ratings
        ];
        
        foreach ($products as $index => $product) {
            echo "<h3>Adding ratings for: {$product['title']}</h3>";
            
            // Determine rating pattern based on product index
            if ($index % 3 == 0) {
                $pattern = 'high';
                $total_reviews = rand(8, 15);
            } elseif ($index % 3 == 1) {
                $pattern = 'good';
                $total_reviews = rand(5, 12);
            } else {
                $pattern = 'mixed';
                $total_reviews = rand(3, 8);
            }
            
            $ratings_to_add = $rating_patterns[$pattern];
            
            // Clear existing reviews for this product
            executeQuery("DELETE FROM product_reviews WHERE product_id = ?", [$product['id']]);
            
            $review_count = 0;
            
            // Add reviews based on rating distribution
            foreach ($ratings_to_add as $rating => $percentage) {
                $num_reviews = round(($percentage / 100) * $total_reviews);
                
                for ($i = 0; $i < $num_reviews; $i++) {
                    $customer = $sample_customers[array_rand($sample_customers)];
                    $review_text = $sample_reviews[$rating][array_rand($sample_reviews[$rating])];
                    
                    // Add some variation to review dates
                    $days_ago = rand(1, 90);
                    $review_date = date('Y-m-d H:i:s', strtotime("-$days_ago days"));
                    
                    insertData("
                        INSERT INTO product_reviews 
                        (product_id, customer_name, customer_email, rating, review_text, status, created_at) 
                        VALUES (?, ?, ?, ?, ?, 'approved', ?)
                    ", [
                        $product['id'],
                        $customer['name'],
                        $customer['email'],
                        $rating,
                        $review_text,
                        $review_date
                    ]);
                    
                    $review_count++;
                }
            }
            
            // Calculate average rating
            $avg_rating = fetchSingle("
                SELECT AVG(rating) as avg_rating, COUNT(*) as total_reviews 
                FROM product_reviews 
                WHERE product_id = ? AND status = 'approved'
            ", [$product['id']]);
            
            echo "<p>✅ Added $review_count reviews (Average: " . round($avg_rating['avg_rating'], 1) . " stars)</p>";
        }
        
        $pdo->commit();
        
        echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h2>🎉 Ratings Added Successfully!</h2>";
        echo "<p>Sample ratings and reviews have been added to all products.</p>";
        echo "<ul>";
        echo "<li>✅ " . count($products) . " products updated with ratings</li>";
        echo "<li>✅ Various star ratings (3-5 stars) distributed across products</li>";
        echo "<li>✅ Realistic review comments added</li>";
        echo "<li>✅ Different review dates for authenticity</li>";
        echo "</ul>";
        echo "<p><strong>Next Steps:</strong></p>";
        echo "<ol>";
        echo "<li><a href='hair-care.php'>View Hair Care Products</a> to see the ratings</li>";
        echo "<li><a href='skin-care.php'>View Skin Care Products</a> to see the ratings</li>";
        echo "<li><a href='health-care.php'>View Health Care Products</a> to see the ratings</li>";
        echo "<li><a href='index.php'>Check Homepage</a> products with ratings</li>";
        echo "<li><a href='admin/reviews.php'>Manage Reviews</a> in admin panel</li>";
        echo "</ol>";
        echo "</div>";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo "<p style='color: red;'>❌ Error adding ratings: " . $e->getMessage() . "</p>";
    }
}

// Show current ratings status
echo "<h2>Current Product Ratings Status</h2>";

try {
    $products_with_ratings = fetchAll("
        SELECT p.id, p.title, 
               AVG(pr.rating) as avg_rating,
               COUNT(pr.id) as review_count
        FROM products p
        LEFT JOIN product_reviews pr ON p.id = pr.product_id AND pr.status = 'approved'
        WHERE p.status = 'active'
        GROUP BY p.id
        ORDER BY p.title ASC
    ");
    
    if ($products_with_ratings) {
        echo "<table border='1' cellpadding='8' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'><th>Product</th><th>Average Rating</th><th>Total Reviews</th><th>Stars Display</th></tr>";
        
        foreach ($products_with_ratings as $product) {
            $avg_rating = $product['avg_rating'] ? round($product['avg_rating'], 1) : 0;
            $review_count = $product['review_count'] ?: 0;
            
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($product['title']) . "</strong></td>";
            echo "<td>" . ($avg_rating > 0 ? $avg_rating . " / 5" : "No ratings") . "</td>";
            echo "<td>" . $review_count . " reviews</td>";
            echo "<td>";
            
            // Display stars
            for ($i = 1; $i <= 5; $i++) {
                if ($i <= $avg_rating) {
                    echo "⭐";
                } elseif ($i - 0.5 <= $avg_rating) {
                    echo "⭐"; // Half star (simplified)
                } else {
                    echo "☆";
                }
            }
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No products found in database.</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking ratings: " . $e->getMessage() . "</p>";
}

if (!isset($_POST['action'])) {
?>

<h2>Add Sample Ratings</h2>
<div style="background: #fff3cd; color: #856404; padding: 15px; border-radius: 8px; margin: 20px 0;">
    <h3>⚠️ What This Will Do:</h3>
    <ul>
        <li><strong>Add realistic ratings</strong> (3-5 stars) to all products</li>
        <li><strong>Create sample reviews</strong> with customer names and comments</li>
        <li><strong>Distribute ratings naturally</strong> - some products get mostly 5 stars, others get mixed ratings</li>
        <li><strong>Add review dates</strong> from the past 90 days for authenticity</li>
        <li><strong>Clear existing reviews</strong> and replace with new sample data</li>
    </ul>
    <p><strong>Rating Distribution:</strong></p>
    <ul>
        <li>🌟 <strong>High-rated products:</strong> Mostly 5 and 4 stars (60% five-star, 30% four-star)</li>
        <li>⭐ <strong>Good products:</strong> Balanced ratings (40% five-star, 45% four-star)</li>
        <li>✨ <strong>Mixed products:</strong> More varied ratings (30% five-star, 40% four-star, 30% three-star)</li>
    </ul>
</div>

<form method="POST">
    <input type="hidden" name="action" value="add_ratings">
    <button type="submit" style="background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: bold;">
        ⭐ Add Sample Ratings to All Products
    </button>
</form>

<?php } ?>

<hr>
<h2>🔗 Quick Links</h2>
<p><a href="hair-care.php">Hair Care Products</a></p>
<p><a href="skin-care.php">Skin Care Products</a></p>
<p><a href="health-care.php">Health Care Products</a></p>
<p><a href="index.php">Homepage</a></p>
<p><a href="admin/reviews.php">Manage Reviews (Admin)</a></p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
table { border-collapse: collapse; width: 100%; margin: 15px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; font-weight: bold; }
hr { margin: 30px 0; border: none; border-top: 2px solid #eee; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
