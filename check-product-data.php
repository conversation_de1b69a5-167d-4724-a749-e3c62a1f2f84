<?php
require_once 'config/dbconfig.php';

echo "<h2>Product Data Check</h2>";

try {
    // Get a sample product
    $products = fetchAll("SELECT id, title, description FROM products WHERE status = 'active' LIMIT 3");
    
    if ($products) {
        foreach ($products as $product) {
            echo "<div style='border: 1px solid #ccc; padding: 20px; margin: 20px 0;'>";
            echo "<h3>Product: " . htmlspecialchars($product['title']) . "</h3>";
            echo "<h4>Raw Description (from database):</h4>";
            echo "<pre style='background: #f5f5f5; padding: 10px;'>" . htmlspecialchars($product['description']) . "</pre>";
            
            echo "<h4>Description Length:</h4>";
            echo "<p>" . strlen($product['description']) . " characters</p>";
            
            echo "<h4>Line Breaks Found:</h4>";
            $line_breaks = substr_count($product['description'], "\n") + substr_count($product['description'], "\r\n");
            echo "<p>" . $line_breaks . " line breaks</p>";
            
            echo "<h4>Split by Lines:</h4>";
            $lines = preg_split('/\r\n|\r|\n/', trim($product['description']));
            echo "<ul>";
            foreach ($lines as $i => $line) {
                echo "<li>Line " . ($i + 1) . ": '" . htmlspecialchars($line) . "'</li>";
            }
            echo "</ul>";
            echo "</div>";
        }
    } else {
        echo "<p>No products found</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
</style>
