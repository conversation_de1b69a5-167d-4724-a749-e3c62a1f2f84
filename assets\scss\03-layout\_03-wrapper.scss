/*----------------------------------------*/
/*  Wrapper CSS
/*----------------------------------------*/

// Section Title
.section-title {
  margin-bottom: 60px;
  @media #{$desktop-device} {
    margin-bottom: 55px;
  }
  @media #{$tablet-device} {
    margin-bottom: 50px;
  }
  @media #{$large-mobile} {
    margin-bottom: 45px;
  }
  .title {
    font-size: 50px;
    font-weight: $font-weight-normal;
    margin-bottom: 13px;
    line-height: 39px;
    padding-bottom: 10px;
    text-transform: capitalize;
    @media #{$desktop-device} {
      font-size: 42px;
      margin-bottom: 12px;
      line-height: 32px;
      padding-bottom: 8px;
    }
    @media #{$tablet-device} {
      font-size: 38px;
      margin-bottom: 10px;
      line-height: 30px;
      padding-bottom: 7px;
    }
    @media #{$large-mobile} {
      font-size: 30px;
      margin-bottom: 10px;
      line-height: 30px;
      padding-bottom: 7px;
    }
  }
  p {
    max-width: 430px;
    margin: 0 auto;
  }
}

.line-left-style {
  background-color: #CECECE;
  display: inline-block;
  height: 1px;
  width: 40px;
}

// Breadcrumb
.breadcrumb-style1 {
  background: #FFFAEF;
  padding: 36px 0 20px;
}

.breadcrumb-item {
  color: #494949;
  font-size: 14px;
  line-height: 28px;
  @media #{$desktop-device, $tablet-device, $large-mobile} {
    font-size: 15px;
  }

  a {
    color: #494949;

    &:hover {
      color: $primary;
    }
  }

  &.active {
    color: #494949;
  }

  + {
    .breadcrumb-item {
      &:before {
        color: #494949;
        font-size: 16px;
      }
    }
  }
}

// Page Header
.page-header-style2-area {
  border-radius: 30px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: right center;
  margin: 0 auto;
  max-width: 1703px;
  padding: 38px 0 90px;
  @media only screen and (max-width: 1649px) {
    background-position: left center;
  }
  @media #{$desktop-device, $tablet-device} {
    padding: 90px 0 90px;
  }
  @media #{$tablet-device, $large-mobile} {
    padding: 115px 0 85px;
  }
  @media #{$large-mobile} {
    padding: 48px 0 82px;
  }
}

.page-header-content {
  margin-top: 40px;
  @media #{$desktop-device} {
    margin-top: 0;
  }
  @media #{$tablet-device} {
    margin-top: -30px;
  }

  .title-img {
    margin-bottom: -39px;
    @media #{$desktop-device} {
      margin-bottom: -60px;
      width: 330px;
    }
    @media #{$tablet-device, $large-mobile} {
      margin-bottom: -52px;
      width: 250px;
    }
    @media #{$extra-small-mobile} {
      width: 180px;
      margin-bottom: -32px;
    }
  }

  .page-header-title {
    font-weight: $font-weight-light;
    font-size: 67px;
    margin-bottom: 14px;
    @media #{$desktop-device, $tablet-device} {
      font-size: 48px;
      margin-bottom: 10px
    }
    @media #{$tablet-device, $large-mobile} {
      font-size: 42px;
      margin-bottom: 8px
    }
    @media #{$extra-small-mobile} {
      font-size: 35px;
    }
  }

  .page-header-sub-title {
    font-weight: $font-weight-normal;
    font-size: 21px;
    line-height: 28px;
    letter-spacing: 0.43em;
    margin-bottom: 28px;
    text-decoration-line: underline;
    @media #{$desktop-device, $tablet-device, $large-mobile} {
      font-size: 18px;
      margin-bottom: 18px;
    }
    @media #{$extra-small-mobile} {
      font-size: 14px;
      margin-bottom: 15px;
    }
  }

  .page-header-desc {
    font-size: 21px;
    line-height: 1.5;
    max-width: 470px;
    @media #{$desktop-device, $tablet-device, $large-mobile} {
      font-size: 16px;
    }
    @media #{$extra-small-mobile} {
      font-size: 15px
    }
  }
}

.page-header-st2-content {
  .title-img {
    margin-bottom: -53px;
    @media #{$desktop-device, $tablet-device, $large-mobile} {
      margin-bottom: -40px;
      width: 220px;
    }
  }

  .page-header-title {
    line-height: 87px;
    margin-bottom: 16px;
    @media #{$desktop-device, $tablet-device, $large-mobile} {
      line-height: 1.2;
      margin-bottom: 7px;
      font-size: 43px;
    }
    @media #{$small-mobile} {
      font-size: 38px;
    }
  }
}

.page-header-st3-content {
  .breadcrumb {
    margin-bottom: 10px;
  }

  .page-header-title {
    font-size: 28px;
    font-weight: $font-weight-normal;
    margin-bottom: 0;
  }
}

.showing-pagination-results {
  font-size: 15px;
  font-weight: $font-weight-normal;
  line-height: 1.75;
  margin-bottom: 0;
  @media #{$desktop-device, $tablet-device, $large-mobile} {
    font-size: 15px;
  }
}

.page-header-thumb {
  text-align: right;
  @media #{$large-mobile} {
    text-align: left;
    margin-top: 50px;
  }
  @media #{$extra-small-mobile} {
    margin-top: 30px;
  }
  img {
    @media #{$large-mobile} {
      width: 330px;
    }
  }
}

// Newsletter
.newsletter-content-wrap {
  height: 230px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 171px 0 100px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  @media #{$desktop-device} {
    padding: 0 30px 0 30px;
  }
  @media #{$tablet-device} {
    padding: 0 15px 0 15px;
  }
  @media #{$large-mobile} {
    display: block;
    padding: 40px 20px 44px;
    text-align: center;
    height: auto;
  }
}

.newsletter-content {
  padding-top: 10px;
  @media #{$tablet-device} {
    padding-right: 15px;
  }
  @media #{$large-mobile} {
    padding-right: 0;
    padding-top: 0;
  }
  .title {
    margin-bottom: 11px;
  }
  p {
    max-width: 310px;
  }
}

.newsletter-form {
  position: relative;
  margin-top: 12px;
  .form-control {
    box-shadow: none;
    border: 1px solid #DC7354;
    border-radius: 10px;
    font-size: 13px;
    font-style: italic;
    color: #979797;
    height: 42px;
    width: 400px;
    padding: 5px 58px 7px 14px;
    @media #{$tablet-device} {
      width: 318px;
      padding: 5px 30px 7px 28px;
    }
    @media #{$large-mobile} {
      width: 100%;
      padding: 5px 30px 7px 28px;
    }
  }
  .btn-submit {
    background-color: #CE4820;
    border-radius: 10px;
    position: absolute;
    top: 0;
    right: 0;
    border: none;
    color: #fff;
    height: 100%;
    width: 50px;
    padding: 0;
    margin: 0;
    display: inline-block;
    font-size: 15px;
    line-height: 38px;
    &:hover {
      background-color: darken(#CE4820, 10%);
    }
  }
}

// My Account
.my-account-tab-menu {
  flex-direction: column;
  @media #{$large-mobile} {
    margin-bottom: 40px;
  }
  &.nav-tabs {
    .nav-link {
      background-color: transparent;
      border: 1px solid #e5e5e5;
      border-bottom: none;
      border-radius: 0;
      color: $black;
      font-family: $font-current-theme1;
      font-size: 15px;
      font-weight: 500;
      display: block;
      padding: 10px 15px;
      text-align: left;
      text-transform: capitalize;
      &:last-child {
        border-bottom: 1px solid #e5e5e5;
      }
      &.active {
        background-color: $theme-color;
      }
      &:hover,
      &.active {
        background-color: $theme-color;
        border-color: $theme-color;
        color: $white;
      } 
    }
  }
}

// Page Not Found
.page-not-found-area {
  align-items: center;
  display: flex;
  height: calc(100vh - 144px);
  min-height: 650px;
}

.page-not-found {
  margin-top: -50px;
  text-align: center;
  @media #{$desktop-device, $tablet-device, $large-mobile} {
    margin-top: 0;
  }

  img {
    margin-bottom: 18px;
  }

  .title {
    color: $black;
    font-weight: $font-weight-medium;
    font-size: 50px;
    line-height: 66px;
    margin-bottom: 14px;
    @media #{$desktop-device, $tablet-device, $large-mobile} {
      font-size: 40px;
      line-height: 1.2;
      margin-bottom: 4px;
    }
    @media #{$extra-small-mobile} {
      font-size: 30px;
    }
  }

  .back-btn {
    color: $black;
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 0;
    @media #{$desktop-device, $tablet-device, $large-mobile} {
      font-size: 15px;
    }

    a {
      font-weight: $font-weight-medium;
      font-size: 18px;
      line-height: 1.6;
      text-decoration-line: underline;
      color: #007DF0;
      @media #{$desktop-device, $tablet-device, $large-mobile} {
        font-size: 16px;
      }

      &:hover {
        color: $primary;
      }
    }
  }
}

// Botton
.btn {
  background-color: $primary;
  border: 2px solid $primary;
  box-shadow: none;
  border-radius: 50px;
  color: $white;
  display: inline-block;
  font-size: 14px;
  height: 50px;
  letter-spacing: 7px;
  line-height: 37px;
  padding: 5px 25px 5px 37px;
  text-align: center;
  text-transform: uppercase;
  @media #{$desktop-device, $tablet-device, $large-mobile} {
    padding: 5px 18px 5px 25px;
    height: 48px;
    line-height: 35px;
    letter-spacing: 5px;
  }
  &:hover {
    background-color: $dark;
    border-color: $dark;
    color: $white;
  }

  &.btn-border-primary {
    background-color: transparent;
    border-color: #E63946;
    color: $dark;
    &:hover {
      background-color: $primary;
      border-color: $primary;
      color: $white;
    }
  }

  &.btn-border-dark {
    background-color: $white;
    border-color: #7A7A7A;
    color: $dark;
    &:hover {
      background-color: $primary;
      border-color: $primary;
      color: $white;
    }
  }

  &.btn-sm {
    font-size: 13px;
    font-weight: $font-weight-medium;
    letter-spacing: 0.5em;
    padding: 5px 41px 5px 47px;
  }
}

.btn-link {
  color: #572DFF;
  display: inline-block;
  font-size: 13px;
  font-weight: $font-weight-medium;
  position: relative;
  text-transform: uppercase;
  text-decoration: none;

  &:before {
    background-color: #572DFF;
    content: "";
    height: 2px;
    position: absolute;
    width: 100%;
    bottom: 1px;
    opacity: .6;
    transition: $transition-base;
  } 

  &:hover {
    color: $primary;
    &:before {
      background-color: $primary;
    }
  }
}

// Popup Images, Video
.ht-popup-video {
  align-items: center;
  display: flex;

  .icon {
    background-color: #E63946;
    height: 60px;
    width: 60px;
    border-radius: 50%;
    color: #fff;
    text-align: center;
    line-height: 59px;
    font-size: 14px;
    padding-left: 2px;
    @media #{$desktop-device, $tablet-device, $large-mobile} {
      height: 50px;
      width: 50px;
      line-height: 50px;
      font-size: 14px;
    }
  }
  span {
    color: #1D3557;
    font-size: 13px;
    font-weight: $font-weight-semi-bold;
    letter-spacing: 0.2em;
    margin-left: 14px;
    text-transform: uppercase;
    transition: $transition-base;
  }

  &:hover {
    span {
      color: #e63946;
    }
  }
}

// Map
.map-area {
  iframe {
    width: 100%;
    height: 600px;
    border-radius: 0;
    @media #{$tablet-device, $large-mobile} {
      height: 400px;
    }
  }
}

// Pagination
.pagination {
  align-items: center;
  border: 2px solid #E63946;
  border-radius: 50px;
  width: fit-content;

  .page-link {
    font-weight: 500;
    font-size: 13px;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    color: #231942;
    border: none;
    border-radius: 0;
    background: none;
    padding: 0;
    margin: 0;
    height: 46px;
    width: 46px;
    text-align: center;
    line-height: 46px;
    @media #{$large-mobile} {
      width: 38px;
    }

    &:hover {
      color: $primary;
    }
  }

  span {
    color: #575757;
  }

  .previous {
    border-right: 1px solid #CDCDCD;
    margin-right: 18px;
    @media #{$large-mobile} {
      margin-right: 0;
    }
  }

  .next {
    border-left: 1px solid #CDCDCD;
    margin-left: 16px !important;
    @media #{$large-mobile} {
      margin-left: 0 !important;
    }
  }

  .previous, .next {
    height: 25px;
    font-size: 12px;
    line-height: 26px
  }
}

// Fancybox
.fancybox__content {
  background-color: transparent;
  outline: none;
  border: none;
  padding: 0;
  margin: 0;
}

.fancybox__carousel.is-draggable .fancybox__slide,
.fancybox__carousel.is-draggable .fancybox__slide .fancybox__content {
  cursor: url('../images/icons/cancel-white.webp'), auto;
}

.offcanvas-backdrop,
.modal-backdrop {
  background-color: rgba(43, 43, 43, 0.92);
  cursor: url('../images/icons/cancel-white.webp'), auto;
  &.show {
    opacity: 1;
  }
}

// Nice Select
.nice-select {
  height: auto;
  width: auto;
  line-height: 1;
  background: none;
  border-radius: 0;
  padding: 0 18px 0 0;

  .current {
    margin-right: 0;
  }

  .option {
    font-size: 14px;
    line-height: 35px;
    min-height: 35px;
    border-bottom: 1px solid #eee;
    padding: 2px 20px;

    &:last-child {
      border-bottom: none;
    }
  }

  &:after {
    content: url('../images/icons/menu-down2.webp');
    border: none;
    height: auto;
    width: auto;
    transform: translate(0%, -50%) rotate(0deg);
    @include transform-origin(inherit);
    margin: 0;
    right: 0;
    line-height: 1
  }

  &.open {
    &:after {
      transform: translate(0%, -50%) rotate(-180deg);
      margin: 4px 0 0 0;
    }
  }
}

.offcanvas {
  z-index: 10;
}

.offcanvas-backdrop {
  z-index: 9;
}

.scroll-to-top {
  box-shadow: 0 0 8px 1px rgb(0 0 0 / 20%);
  bottom: -60px;
  background-color: $primary;
  color: $white;
  position: fixed;
  right: 30px;
  display: block;
  padding: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  text-align: center;
  font-size: 20px;
  line-height: 38px;
  font-weight: 700;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  overflow: hidden;
  @include transition (all 0.5s cubic-bezier(0.645, 0.045, 0.355, 1));
  z-index: 999;

  &:hover {
    background-color: $primary;
  }

  &.show {
    visibility: visible;
    opacity: .8;
    bottom: 20px;

    &:hover {
      opacity: 1;
    }
  }
}