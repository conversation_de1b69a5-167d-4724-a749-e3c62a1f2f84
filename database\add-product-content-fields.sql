-- Add new content fields to products table
-- Run this script to add Key Benefits, Ingredients, and How to Use fields

USE naturals;

-- Add new columns to products table
ALTER TABLE products 
ADD COLUMN key_benefits TEXT NULL AFTER description,
ADD COLUMN ingredients TEXT NULL AFTER key_benefits,
ADD COLUMN how_to_use TEXT NULL AFTER ingredients;

-- Update existing products with sample content (optional)
UPDATE products SET 
    key_benefits = CASE 
        WHEN title LIKE '%hair%' OR title LIKE '%Hair%' THEN 
            '• Promotes healthy hair growth\n• Reduces hair fall and breakage\n• Strengthens hair follicles\n• Adds natural shine and volume\n• Suitable for all hair types'
        WHEN title LIKE '%face%' OR title LIKE '%Face%' OR title LIKE '%skin%' OR title LIKE '%Skin%' THEN 
            '• Deep cleanses and purifies skin\n• Removes dirt and impurities\n• Maintains skin\'s natural moisture\n• Suitable for daily use\n• Gentle on sensitive skin'
        WHEN title LIKE '%syrup%' OR title LIKE '%Syrup%' OR title LIKE '%health%' OR title LIKE '%Health%' THEN 
            '• Boosts immune system naturally\n• Provides essential nutrients\n• Safe for regular consumption\n• Made with natural ingredients\n• No artificial preservatives'
        ELSE 
            '• Made with premium natural ingredients\n• Safe and effective formula\n• Dermatologically tested\n• Suitable for regular use\n• No harmful chemicals'
    END,
    ingredients = CASE 
        WHEN title LIKE '%hair%' OR title LIKE '%Hair%' THEN 
            'Natural oils, Vitamin E, Biotin, Keratin proteins, Essential amino acids, Herbal extracts'
        WHEN title LIKE '%face%' OR title LIKE '%Face%' OR title LIKE '%skin%' OR title LIKE '%Skin%' THEN 
            'Natural cleansing agents, Aloe vera extract, Vitamin C, Glycerin, Essential oils, Herbal extracts'
        WHEN title LIKE '%syrup%' OR title LIKE '%Syrup%' OR title LIKE '%health%' OR title LIKE '%Health%' THEN 
            'Natural fruit extracts, Vitamins, Minerals, Herbal concentrates, Natural sweeteners'
        ELSE 
            'Premium natural ingredients, Essential vitamins, Herbal extracts, Natural preservatives'
    END,
    how_to_use = CASE 
        WHEN title LIKE '%hair%' OR title LIKE '%Hair%' THEN 
            '1. Apply a small amount to clean, damp hair\n2. Massage gently into scalp and hair\n3. Leave for 2-3 minutes\n4. Rinse thoroughly with water\n5. Use 2-3 times per week for best results'
        WHEN title LIKE '%face%' OR title LIKE '%Face%' OR title LIKE '%skin%' OR title LIKE '%Skin%' THEN 
            '1. Wet your face with lukewarm water\n2. Apply a small amount to your palm\n3. Gently massage in circular motions\n4. Rinse thoroughly with water\n5. Pat dry with a clean towel\n6. Use twice daily for best results'
        WHEN title LIKE '%syrup%' OR title LIKE '%Syrup%' OR title LIKE '%health%' OR title LIKE '%Health%' THEN 
            '1. Shake well before use\n2. Take 1-2 teaspoons daily\n3. Can be taken with water or directly\n4. Best taken after meals\n5. Store in a cool, dry place\n6. Consult physician for children under 2 years'
        ELSE 
            '1. Read instructions carefully before use\n2. Apply as directed on packaging\n3. Use regularly for best results\n4. Store in a cool, dry place\n5. Discontinue if irritation occurs'
    END
WHERE status = 'active';

-- Show the updated structure
DESCRIBE products;

-- Show sample data
SELECT id, title, 
       LEFT(key_benefits, 50) as benefits_preview,
       LEFT(ingredients, 50) as ingredients_preview,
       LEFT(how_to_use, 50) as usage_preview
FROM products 
WHERE status = 'active' 
LIMIT 5;
