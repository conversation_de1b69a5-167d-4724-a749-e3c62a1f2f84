<?php
require_once 'config/dbconfig.php';

echo "<h1>Fix Missing Product Slugs</h1>";

// Function to create URL-friendly slug
function createSlug($string) {
    // Convert to lowercase
    $slug = strtolower($string);
    
    // Replace spaces and special characters with hyphens
    $slug = preg_replace('/[^a-z0-9]+/', '-', $slug);
    
    // Remove leading/trailing hyphens
    $slug = trim($slug, '-');
    
    // Remove multiple consecutive hyphens
    $slug = preg_replace('/-+/', '-', $slug);
    
    return $slug;
}

// Get all products without slugs
$products = fetchAll("SELECT * FROM products WHERE slug IS NULL OR slug = ''");

echo "<p>Found " . count($products) . " products without slugs.</p>";

if (count($products) > 0) {
    echo "<h2>Updating slugs:</h2>";
    
    foreach ($products as $product) {
        $slug = createSlug($product['title']);
        
        // Make sure slug is unique
        $counter = 1;
        $original_slug = $slug;
        while (true) {
            $existing = fetchSingle("SELECT id FROM products WHERE slug = ? AND id != ?", [$slug, $product['id']]);
            if (!$existing) {
                break;
            }
            $slug = $original_slug . '-' . $counter;
            $counter++;
        }
        
        // Update the product with the slug
        $result = executeQuery("UPDATE products SET slug = ? WHERE id = ?", [$slug, $product['id']]);
        
        echo "<p>✓ Updated product ID {$product['id']} ({$product['title']}) with slug: <strong>{$slug}</strong></p>";
    }
    
    echo "<h2>Verification:</h2>";
    
    // Verify the updates
    $updated_products = fetchAll("SELECT id, title, slug, product_code FROM products ORDER BY id");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Product Code</th><th>Title</th><th>Slug</th></tr>";
    
    foreach ($updated_products as $product) {
        echo "<tr>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . $product['product_code'] . "</td>";
        echo "<td>" . $product['title'] . "</td>";
        echo "<td><strong>" . $product['slug'] . "</strong></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>Test Links:</h2>";
    echo "<p>Now you can test these product links:</p>";
    foreach ($updated_products as $product) {
        $url = "product-details.php?product=" . $product['slug'];
        echo "<p><a href='{$url}' target='_blank'>{$product['title']}</a> - {$url}</p>";
    }
    
} else {
    echo "<p>All products already have slugs!</p>";
}

echo "<h2>Next Steps:</h2>";
echo "<p>1. Test the MOSSFIRE MOSQUITO REPELLENT LOTION product link</p>";
echo "<p>2. Try adding it to cart</p>";
echo "<p>3. Check if other products still work</p>";
?>
