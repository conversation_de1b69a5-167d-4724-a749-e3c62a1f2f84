<?php
require_once 'config/dbconfig.php';

echo "<h2>🔍 Checking for Duplicate Reviews</h2>";

// Check for duplicate reviews by same customer for same product
$duplicates = fetchAll("
    SELECT product_id, customer_name, COUNT(*) as count
    FROM product_reviews 
    GROUP BY product_id, customer_name 
    HAVING COUNT(*) > 1
    ORDER BY count DESC
");

if (!empty($duplicates)) {
    echo "<h3>❌ Found Duplicate Reviews:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>Product ID</th><th>Customer Name</th><th>Duplicate Count</th><th>Action</th></tr>";
    
    foreach ($duplicates as $duplicate) {
        echo "<tr>";
        echo "<td>" . $duplicate['product_id'] . "</td>";
        echo "<td>" . htmlspecialchars($duplicate['customer_name']) . "</td>";
        echo "<td>" . $duplicate['count'] . "</td>";
        echo "<td><a href='?remove_duplicates=1&product_id=" . $duplicate['product_id'] . "&customer_name=" . urlencode($duplicate['customer_name']) . "' style='color: red;'>Remove Duplicates</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: green;'>✅ No duplicate reviews found!</p>";
}

// Handle duplicate removal
if (isset($_GET['remove_duplicates']) && $_GET['remove_duplicates'] == '1') {
    $product_id = intval($_GET['product_id']);
    $customer_name = $_GET['customer_name'];
    
    if ($product_id > 0 && !empty($customer_name)) {
        // Get all reviews for this product/customer combination
        $reviews = fetchAll("
            SELECT id, created_at FROM product_reviews 
            WHERE product_id = ? AND customer_name = ?
            ORDER BY created_at DESC
        ", [$product_id, $customer_name]);
        
        if (count($reviews) > 1) {
            // Keep the most recent one, delete the rest
            $keep_id = $reviews[0]['id'];
            $delete_ids = array_slice(array_column($reviews, 'id'), 1);
            
            foreach ($delete_ids as $delete_id) {
                executeQuery("DELETE FROM product_reviews WHERE id = ?", [$delete_id]);
            }
            
            echo "<p style='color: green;'>✅ Removed " . count($delete_ids) . " duplicate reviews for " . htmlspecialchars($customer_name) . "</p>";
            echo "<p><a href='check-duplicate-reviews.php'>Refresh to check again</a></p>";
        }
    }
}

// Show all reviews for debugging
echo "<h3>📋 All Reviews in Database:</h3>";
$all_reviews = fetchAll("
    SELECT pr.*, p.title as product_title 
    FROM product_reviews pr
    LEFT JOIN products p ON pr.product_id = p.id
    ORDER BY pr.product_id, pr.customer_name, pr.created_at DESC
");

if (!empty($all_reviews)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Product</th><th>Customer</th><th>Rating</th><th>Status</th><th>Date</th><th>Action</th></tr>";
    
    foreach ($all_reviews as $review) {
        echo "<tr>";
        echo "<td>" . $review['id'] . "</td>";
        echo "<td>" . htmlspecialchars($review['product_title']) . "</td>";
        echo "<td>" . htmlspecialchars($review['customer_name']) . "</td>";
        echo "<td>";
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $review['rating']) {
                echo "⭐";
            } else {
                echo "☆";
            }
        }
        echo " (" . $review['rating'] . ")</td>";
        echo "<td>" . $review['status'] . "</td>";
        echo "<td>" . date('M d, Y', strtotime($review['created_at'])) . "</td>";
        echo "<td><a href='?delete_review=" . $review['id'] . "' style='color: red;' onclick='return confirm(\"Delete this review?\")'>Delete</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No reviews found in database.</p>";
}

// Handle single review deletion
if (isset($_GET['delete_review'])) {
    $review_id = intval($_GET['delete_review']);
    if ($review_id > 0) {
        executeQuery("DELETE FROM product_reviews WHERE id = ?", [$review_id]);
        echo "<p style='color: green;'>✅ Review deleted successfully!</p>";
        echo "<p><a href='check-duplicate-reviews.php'>Refresh page</a></p>";
    }
}

echo "<hr>";
echo "<h3>🔧 Quick Actions:</h3>";
echo "<p><a href='admin/reviews.php' style='color: blue;'>📋 Manage Reviews in Admin Panel</a></p>";
echo "<p><a href='add-sample-reviews.php' style='color: green;'>➕ Add Sample Reviews</a></p>";
echo "<p><a href='index.php' style='color: purple;'>🏠 View Website</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
table { border-collapse: collapse; width: 100%; margin: 15px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; font-weight: bold; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
hr { margin: 30px 0; border: none; border-top: 2px solid #eee; }
</style>
