<?php
session_start();
require_once '../../config/dbconfig.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

header('Content-Type: application/json');

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $review_id = intval($input['review_id'] ?? 0);
    $status = $input['status'] ?? '';
    
    // Validation
    if ($review_id <= 0) {
        throw new Exception('Invalid review ID');
    }
    
    if (!in_array($status, ['approved', 'pending', 'rejected'])) {
        throw new Exception('Invalid status');
    }
    
    // Check if review exists
    $review = fetchSingle("SELECT id FROM product_reviews WHERE id = ?", [$review_id]);
    if (!$review) {
        throw new Exception('Review not found');
    }
    
    // Update review status
    $updated = executeQuery("UPDATE product_reviews SET status = ? WHERE id = ?", [$status, $review_id]);
    
    if ($updated) {
        echo json_encode([
            'success' => true,
            'message' => 'Review status updated successfully'
        ]);
    } else {
        throw new Exception('Failed to update review status');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
