<?php
session_start();
require_once '../config/dbconfig.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit();
}

$error_message = $_SESSION['error_message'] ?? '';
$success_message = $_SESSION['success_message'] ?? '';
$product_id = intval($_GET['id'] ?? 0);

// Clear session messages
unset($_SESSION['error_message'], $_SESSION['success_message']);

if ($product_id <= 0) {
    $_SESSION['error_message'] = 'Invalid product ID.';
    header('Location: products.php');
    exit();
}

// Get product details
$product = fetchSingle("SELECT * FROM products WHERE id = ?", [$product_id]);
if (!$product) {
    $_SESSION['error_message'] = 'Product not found.';
    header('Location: products.php');
    exit();
}

// Get categories for dropdown
$categories = fetchAll("SELECT * FROM categories WHERE status = 'active' ORDER BY name ASC");

// Get current product categories
$current_categories = fetchAll("
    SELECT c.id, c.name
    FROM categories c
    JOIN product_categories pc ON c.id = pc.category_id
    WHERE pc.product_id = ?
", [$product_id]);

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $title = trim($_POST['title'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $key_benefits = trim($_POST['key_benefits'] ?? '');
        $ingredients = trim($_POST['ingredients'] ?? '');
        $how_to_use = trim($_POST['how_to_use'] ?? '');
        $price = floatval($_POST['price'] ?? 0);
        $sale_price = !empty($_POST['sale_price']) ? floatval($_POST['sale_price']) : null;
        $category_ids = $_POST['category_ids'] ?? [];
        $status = $_POST['status'] ?? 'active';
        $collection = trim($_POST['collection'] ?? '');
        $shipping_info = trim($_POST['shipping_info'] ?? '');
        $weight = trim($_POST['weight'] ?? '');
        $dimensions = trim($_POST['dimensions'] ?? '');
        $materials = trim($_POST['materials'] ?? '');
        $other_info = trim($_POST['other_info'] ?? '');

        // Validation
        if (empty($title)) {
            throw new Exception('Product title is required');
        }
        if (empty($description)) {
            throw new Exception('Product description is required');
        }
        if ($price <= 0) {
            throw new Exception('Product price must be greater than 0');
        }
        
        // Handle image upload
        $image_path = $product['image']; // Keep existing image by default
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../assets/images/products/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'webp'];
            
            if (!in_array($file_extension, $allowed_extensions)) {
                throw new Exception('Invalid image format. Only JPG, PNG, and WebP are allowed.');
            }
            
            $new_filename = 'product_' . $product_id . '_' . time() . '.' . $file_extension;
            $upload_path = $upload_dir . $new_filename;
            
            if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                $image_path = 'assets/images/products/' . $new_filename;
                
                // Delete old image if it exists and is different
                if ($product['image'] && $product['image'] !== $image_path && file_exists('../' . $product['image'])) {
                    unlink('../' . $product['image']);
                }
            } else {
                throw new Exception('Failed to upload image');
            }
        }
        
        // Start transaction
        $pdo->beginTransaction();

        // Update product
        executeQuery("
            UPDATE products
            SET title = ?, description = ?, key_benefits = ?, ingredients = ?, how_to_use = ?,
                price = ?, sale_price = ?, status = ?, image = ?, collection = ?, shipping_info = ?,
                weight = ?, dimensions = ?, materials = ?, other_info = ?, updated_at = NOW()
            WHERE id = ?
        ", [$title, $description, $key_benefits, $ingredients, $how_to_use, $price, $sale_price, $status, $image_path, $collection, $shipping_info, $weight, $dimensions, $materials, $other_info, $product_id]);

        // Update product categories with automatic mapping
        // First, delete existing categories
        executeQuery("DELETE FROM product_categories WHERE product_id = ?", [$product_id]);

        // Then, insert new categories with auto-mapping
        if (!empty($category_ids)) {
            // Get category names for mapping
            $category_mapping = [];
            foreach ($category_ids as $category_id) {
                if (intval($category_id) > 0) {
                    $category = fetchSingle("SELECT name FROM categories WHERE id = ?", [intval($category_id)]);
                    if ($category) {
                        $category_mapping[intval($category_id)] = $category['name'];
                    }
                }
            }

            // Auto-map categories to main categories
            $auto_mapped_categories = [];
            foreach ($category_mapping as $cat_id => $cat_name) {
                $auto_mapped_categories[] = $cat_id; // Add original category

                // Auto-mapping logic
                switch (strtolower($cat_name)) {
                    case 'face care':
                    case 'anti-acne':
                        // Face Care and Anti-Acne products should also be in Skin Care
                        $skin_care = fetchSingle("SELECT id FROM categories WHERE name = 'Skin Care'");
                        if ($skin_care && !in_array($skin_care['id'], $auto_mapped_categories)) {
                            $auto_mapped_categories[] = $skin_care['id'];
                        }
                        break;

                    case 'health care':
                        // Health Care products stay in Health Care only
                        break;

                    case 'hair care':
                        // Hair Care products stay in Hair Care only
                        break;

                    case 'skin care':
                        // Skin Care products stay in Skin Care only
                        break;
                }
            }

            // Remove duplicates and insert all categories
            $auto_mapped_categories = array_unique($auto_mapped_categories);
            foreach ($auto_mapped_categories as $category_id) {
                executeQuery("INSERT INTO product_categories (product_id, category_id) VALUES (?, ?)", [$product_id, $category_id]);
            }
        }

        $pdo->commit();
        
        $_SESSION['success_message'] = 'Product updated successfully!';

        // Redirect to prevent form resubmission
        header("Location: edit-product.php?id=$product_id");
        exit();

    } catch (Exception $e) {
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $error_message = $e->getMessage();
    }
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Edit Product</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="products.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Products
                        </a>
                        <a href="../product-details.php?product=<?php echo urlencode($product['product_code']); ?>"
                           class="btn btn-outline-info" target="_blank">
                            <i class="fas fa-eye"></i> View Product
                        </a>
                    </div>
                </div>
            </div>

            <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Product Information</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" enctype="multipart/form-data">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Product Title *</label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="<?php echo htmlspecialchars($product['title']); ?>" required>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description *</label>
                                    <textarea class="form-control" id="description" name="description" rows="4" required><?php echo htmlspecialchars($product['description']); ?></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="key_benefits" class="form-label">Key Benefits</label>
                                    <textarea class="form-control" id="key_benefits" name="key_benefits" rows="4"
                                              placeholder="Enter key benefits (one per line)..."><?php echo htmlspecialchars($product['key_benefits'] ?? ''); ?></textarea>
                                    <div class="form-text">List the main benefits of this product. Use bullet points or separate lines.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="ingredients" class="form-label">Ingredients</label>
                                    <textarea class="form-control" id="ingredients" name="ingredients" rows="3"
                                              placeholder="Enter main ingredients..."><?php echo htmlspecialchars($product['ingredients'] ?? ''); ?></textarea>
                                    <div class="form-text">List the key ingredients used in this product.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="how_to_use" class="form-label">How to Use</label>
                                    <textarea class="form-control" id="how_to_use" name="how_to_use" rows="4"
                                              placeholder="Enter usage instructions..."><?php echo htmlspecialchars($product['how_to_use'] ?? ''); ?></textarea>
                                    <div class="form-text">Provide step-by-step instructions on how to use this product.</div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="price" class="form-label">Original Price (Rs.) *</label>
                                            <input type="number" class="form-control" id="price" name="price"
                                                   value="<?php echo $product['price']; ?>" step="0.01" min="0" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="sale_price" class="form-label">Sale Price (Rs.)</label>
                                            <input type="number" class="form-control" id="sale_price" name="sale_price"
                                                   value="<?php echo $product['sale_price'] ?? ''; ?>" step="0.01" min="0"
                                                   placeholder="Leave empty for no sale">
                                            <div class="form-text">Enter a lower price to show as sale/discount</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="collection" class="form-label">Collection</label>
                                            <input type="text" class="form-control" id="collection" name="collection"
                                                   value="<?php echo htmlspecialchars($product['collection'] ?? ''); ?>"
                                                   placeholder="e.g., Premium Collection">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="categories" class="form-label">Categories</label>
                                    <div class="row">
                                        <?php
                                        $current_category_ids = array_column($current_categories, 'id');
                                        foreach ($categories as $category):
                                        ?>
                                            <div class="col-md-4 mb-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox"
                                                           name="category_ids[]" value="<?php echo $category['id']; ?>"
                                                           id="category_<?php echo $category['id']; ?>"
                                                           <?php echo in_array($category['id'], $current_category_ids) ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="category_<?php echo $category['id']; ?>">
                                                        <?php echo htmlspecialchars($category['name']); ?>
                                                    </label>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="active" <?php echo ($product['status'] == 'active') ? 'selected' : ''; ?>>Active</option>
                                        <option value="inactive" <?php echo ($product['status'] == 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="image" class="form-label">Product Image</label>
                                    <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                    <small class="form-text text-muted">Leave empty to keep current image. Supported formats: JPG, PNG, WebP</small>
                                </div>

                                <div class="mb-3">
                                    <label for="shipping_info" class="form-label">Shipping Information</label>
                                    <textarea class="form-control" id="shipping_info" name="shipping_info" rows="2"><?php echo htmlspecialchars($product['shipping_info'] ?? ''); ?></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="weight" class="form-label">Weight</label>
                                            <input type="text" class="form-control" id="weight" name="weight"
                                                   value="<?php echo htmlspecialchars($product['weight'] ?? ''); ?>"
                                                   placeholder="e.g., 150 g">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="dimensions" class="form-label">Dimensions</label>
                                            <input type="text" class="form-control" id="dimensions" name="dimensions"
                                                   value="<?php echo htmlspecialchars($product['dimensions'] ?? ''); ?>"
                                                   placeholder="e.g., 4 x 4 x 15 cm">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="materials" class="form-label">Materials/Ingredients</label>
                                    <textarea class="form-control" id="materials" name="materials" rows="3"><?php echo htmlspecialchars($product['materials'] ?? ''); ?></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="other_info" class="form-label">Other Information</label>
                                    <textarea class="form-control" id="other_info" name="other_info" rows="3"><?php echo htmlspecialchars($product['other_info'] ?? ''); ?></textarea>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="products.php" class="btn btn-secondary me-md-2">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Update Product
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Current Product Image</h5>
                        </div>
                        <div class="card-body text-center">
                            <?php if ($product['image']): ?>
                                <img src="../<?php echo htmlspecialchars($product['image']); ?>" 
                                     alt="<?php echo htmlspecialchars($product['title']); ?>" 
                                     class="img-fluid rounded" style="max-height: 300px;">
                            <?php else: ?>
                                <div class="text-muted">
                                    <i class="fas fa-image fa-3x mb-2"></i>
                                    <p>No image uploaded</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Product Details</h5>
                        </div>
                        <div class="card-body">
                            <p><strong>Product Code:</strong> <?php echo htmlspecialchars($product['product_code']); ?></p>
                            <p><strong>Collection:</strong> <?php echo htmlspecialchars($product['collection'] ?? 'None'); ?></p>
                            <p><strong>Current Categories:</strong><br>
                                <?php if (!empty($current_categories)): ?>
                                    <?php foreach ($current_categories as $cat): ?>
                                        <span class="badge bg-primary me-1"><?php echo htmlspecialchars($cat['name']); ?></span>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <span class="text-muted">No categories assigned</span>
                                <?php endif; ?>
                            </p>
                            <p><strong>Created:</strong> <?php echo date('M d, Y', strtotime($product['created_at'])); ?></p>
                            <p><strong>Last Updated:</strong> <?php echo date('M d, Y', strtotime($product['updated_at'])); ?></p>
                            <p><strong>Status:</strong>
                                <span class="badge bg-<?php echo ($product['status'] == 'active') ? 'success' : 'secondary'; ?>">
                                    <?php echo ucfirst($product['status']); ?>
                                </span>
                            </p>
                        </div>
                    </div>

                    <!-- Reviews Management Section -->
                    <div class="card mt-3">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Product Reviews</h5>
                            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addReviewModal">
                                <i class="fas fa-plus"></i> Add Review
                            </button>
                        </div>
                        <div class="card-body">
                            <?php
                            // Fetch product reviews
                            $reviews = fetchAll("SELECT * FROM product_reviews WHERE product_id = ? ORDER BY created_at DESC", [$product_id]);
                            ?>

                            <?php if (!empty($reviews)): ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Customer</th>
                                                <th>Rating</th>
                                                <th>Comment</th>
                                                <th>Status</th>
                                                <th>Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($reviews as $review): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($review['customer_name']); ?></td>
                                                <td>
                                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                                        <i class="fas fa-star <?php echo $i <= $review['rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                                    <?php endfor; ?>
                                                    (<?php echo $review['rating']; ?>)
                                                </td>
                                                <td><?php echo htmlspecialchars(substr($review['comment'], 0, 50)) . (strlen($review['comment']) > 50 ? '...' : ''); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $review['status'] == 'approved' ? 'success' : ($review['status'] == 'pending' ? 'warning' : 'danger'); ?>">
                                                        <?php echo ucfirst($review['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('M d, Y', strtotime($review['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-success btn-sm" onclick="updateReviewStatus(<?php echo $review['id']; ?>, 'approved')">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger btn-sm" onclick="deleteReview(<?php echo $review['id']; ?>)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <p class="text-muted">No reviews yet for this product.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Add Review Modal -->
<div class="modal fade" id="addReviewModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Product Review</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addReviewForm">
                <div class="modal-body">
                    <input type="hidden" name="product_id" value="<?php echo $product_id; ?>">

                    <div class="mb-3">
                        <label for="customer_name" class="form-label">Customer Name *</label>
                        <input type="text" class="form-control" name="customer_name" required>
                    </div>

                    <div class="mb-3">
                        <label for="customer_designation" class="form-label">Customer Designation</label>
                        <input type="text" class="form-control" name="customer_designation" placeholder="e.g., Verified Buyer">
                    </div>

                    <div class="mb-3">
                        <label for="rating" class="form-label">Rating *</label>
                        <select class="form-control" name="rating" required>
                            <option value="">Select Rating</option>
                            <option value="5">5 Stars - Excellent</option>
                            <option value="4">4 Stars - Good</option>
                            <option value="3">3 Stars - Average</option>
                            <option value="2">2 Stars - Poor</option>
                            <option value="1">1 Star - Very Poor</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="comment" class="form-label">Review Comment *</label>
                        <textarea class="form-control" name="comment" rows="3" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-control" name="status">
                            <option value="approved">Approved</option>
                            <option value="pending">Pending</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Review</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Add Review Form Handler
document.getElementById('addReviewForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch('ajax/add-review.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Review added successfully!');
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error adding review');
    });
});

// Update Review Status
function updateReviewStatus(reviewId, status) {
    if (confirm('Update review status to ' + status + '?')) {
        fetch('ajax/update-review-status.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                review_id: reviewId,
                status: status
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
}

// Delete Review
function deleteReview(reviewId) {
    if (confirm('Are you sure you want to delete this review?')) {
        fetch('ajax/delete-review.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                review_id: reviewId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
}
</script>

<?php include 'includes/footer.php'; ?>
