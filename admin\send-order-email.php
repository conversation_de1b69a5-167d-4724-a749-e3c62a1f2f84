<?php
session_start();
require_once '../config/dbconfig.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit();
}

$message = '';
$message_type = '';

// Handle email sending
if ($_POST && isset($_POST['order_id'])) {
    $order_id = intval($_POST['order_id']);
    
    try {
        require_once '../order-email.php';
        $result = sendOrderNotificationEmail($order_id);
        
        if ($result) {
            $message = 'Order notification email sent successfully!';
            $message_type = 'success';
        } else {
            $message = 'Failed to send order notification email.';
            $message_type = 'error';
        }
    } catch (Exception $e) {
        $message = 'Error sending email: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Get order details if order_id is provided
$order = null;
$order_id = intval($_GET['id'] ?? $_POST['order_id'] ?? 0);

if ($order_id > 0) {
    $order = fetchSingle("
        SELECT o.*, c.first_name, c.last_name, c.email, c.phone 
        FROM orders o 
        LEFT JOIN customers c ON o.customer_id = c.id 
        WHERE o.id = ?
    ", [$order_id]);
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">📧 Send Order Email</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="orders.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Orders
                    </a>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($order): ?>
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Order Details</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Order Number:</strong> <?php echo htmlspecialchars($order['order_number']); ?></p>
                                        <p><strong>Customer:</strong> <?php echo htmlspecialchars(trim($order['first_name'] . ' ' . $order['last_name'])); ?></p>
                                        <p><strong>Email:</strong> <?php echo htmlspecialchars($order['email']); ?></p>
                                        <p><strong>Phone:</strong> <?php echo htmlspecialchars($order['phone']); ?></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Order Date:</strong> <?php echo date('M d, Y g:i A', strtotime($order['created_at'])); ?></p>
                                        <p><strong>Status:</strong> 
                                            <span class="badge bg-<?php echo $order['status'] === 'delivered' ? 'success' : ($order['status'] === 'cancelled' ? 'danger' : 'warning'); ?>">
                                                <?php echo ucfirst($order['status']); ?>
                                            </span>
                                        </p>
                                        <p><strong>Total Amount:</strong> Rs. <?php echo number_format($order['final_amount'], 0); ?></p>
                                        <p><strong>Payment Method:</strong> <?php echo ucfirst($order['payment_method']); ?></p>
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <p><strong>Shipping Address:</strong></p>
                                    <p class="text-muted"><?php echo nl2br(htmlspecialchars($order['shipping_address'])); ?></p>
                                </div>
                                
                                <?php if ($order['notes']): ?>
                                    <div class="mt-3">
                                        <p><strong>Order Notes:</strong></p>
                                        <p class="text-muted"><?php echo nl2br(htmlspecialchars($order['notes'])); ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">📧 Email Actions</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Email Type</label>
                                        <select class="form-select" name="email_type" disabled>
                                            <option value="order_notification" selected>Order Notification</option>
                                        </select>
                                        <small class="text-muted">Sends detailed order information to admin email</small>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Recipient</label>
                                        <input type="text" class="form-control" value="<EMAIL>" readonly>
                                        <small class="text-muted">Admin notification email</small>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane"></i> Send Email Notification
                                        </button>
                                    </div>
                                </form>
                                
                                <hr>
                                
                                <div class="mt-3">
                                    <h6>📋 Email Preview</h6>
                                    <p class="small text-muted">
                                        The email will include:
                                    </p>
                                    <ul class="small text-muted">
                                        <li>Complete order details</li>
                                        <li>Customer information</li>
                                        <li>Ordered items with prices</li>
                                        <li>Shipping address</li>
                                        <li>Payment information</li>
                                        <li>Next steps for processing</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="card-title mb-0">🔧 Quick Actions</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="order-details.php?id=<?php echo $order['id']; ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> View Full Order
                                    </a>
                                    <a href="orders.php" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-list"></i> All Orders
                                    </a>
                                    <a href="../order-email.php" target="_blank" class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-test-tube"></i> Test Email System
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h5>Order Not Found</h5>
                        <p class="text-muted">The requested order could not be found.</p>
                        <a href="orders.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> Back to Orders
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </main>
    </div>
</div>

<script>
// Auto-refresh page after successful email send
<?php if ($message_type === 'success'): ?>
setTimeout(function() {
    window.location.href = 'orders.php';
}, 3000);
<?php endif; ?>
</script>

<?php include 'includes/footer.php'; ?>
