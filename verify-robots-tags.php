<?php
echo "<h2>🔍 Verify Robots Meta Tags</h2>";

// List of all important pages to check
$pages_to_check = [
    'index.php' => 'Homepage',
    'about-us.php' => 'About Us',
    'contact.php' => 'Contact',
    'hair-care.php' => 'Hair Care Products',
    'skin-care.php' => 'Skin Care Products', 
    'health-care.php' => 'Health Care Products',
    'product-details.php' => 'Product Details',
    'cart.php' => 'Shopping Cart',
    'checkout.php' => 'Checkout',
    'my-account.php' => 'My Account',
    'login.php' => 'Login',
    'signup.php' => 'Sign Up',
    'search.php' => 'Search Results',
    'product.php' => 'All Products'
];

echo "<h3>📊 Current Robots Meta Tag Status</h3>";

$correct_count = 0;
$incorrect_count = 0;
$no_tag_count = 0;

foreach ($pages_to_check as $file => $name) {
    if (file_exists($file)) {
        echo "<div style='background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff;'>";
        echo "<h4>📄 $name ($file)</h4>";
        
        $content = file_get_contents($file);
        
        // Check for robots meta tag patterns
        $has_index_follow = preg_match('/<meta\s+name=["\']robots["\']\s+content=["\']index,?\s*follow["\'][^>]*>/i', $content);
        $has_noindex = preg_match('/<meta\s+name=["\']robots["\']\s+content=["\'][^"\']*noindex[^"\']*["\'][^>]*>/i', $content);
        $has_header_include = strpos($content, "include 'header.php'") !== false || strpos($content, 'include("header.php")') !== false;
        
        if ($has_index_follow) {
            echo "<p style='color: green; font-weight: bold;'>✅ CORRECT: Has 'index, follow' tag</p>";
            $correct_count++;
        } elseif ($has_noindex) {
            echo "<p style='color: red; font-weight: bold;'>❌ INCORRECT: Still has 'noindex' tag</p>";
            $incorrect_count++;
            
            // Show the actual tag
            if (preg_match('/<meta\s+name=["\']robots["\']\s+content=["\'][^"\']*["\'][^>]*>/i', $content, $matches)) {
                echo "<p style='background: #fff3cd; padding: 8px; border-radius: 3px;'><strong>Found:</strong> <code>" . htmlspecialchars($matches[0]) . "</code></p>";
            }
        } elseif ($has_header_include) {
            echo "<p style='color: blue; font-weight: bold;'>ℹ️ INHERITS: Uses header.php (should inherit correct tag)</p>";
            $correct_count++;
        } else {
            echo "<p style='color: orange; font-weight: bold;'>⚠️ NO TAG: No robots meta tag found</p>";
            $no_tag_count++;
        }
        
        // Check if it's a direct HTML file or includes header
        if (strpos($content, '<!DOCTYPE') !== false) {
            echo "<p><strong>Type:</strong> Direct HTML file</p>";
        } else {
            echo "<p><strong>Type:</strong> PHP file (likely includes header.php)</p>";
        }
        
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>❌ $name ($file)</h4>";
        echo "<p style='color: red;'>File not found</p>";
        echo "</div>";
    }
}

echo "<h3>📈 Summary Report</h3>";

echo "<div style='display: flex; gap: 15px; margin: 20px 0; flex-wrap: wrap;'>";

echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; flex: 1; min-width: 200px;'>";
echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ Correct Tags</h4>";
echo "<p style='font-size: 2em; font-weight: bold; color: #155724; margin: 0;'>$correct_count</p>";
echo "<p style='margin: 5px 0 0 0;'>Pages with proper indexing</p>";
echo "</div>";

echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; flex: 1; min-width: 200px;'>";
echo "<h4 style='color: #721c24; margin: 0 0 10px 0;'>❌ Incorrect Tags</h4>";
echo "<p style='font-size: 2em; font-weight: bold; color: #721c24; margin: 0;'>$incorrect_count</p>";
echo "<p style='margin: 5px 0 0 0;'>Pages still blocking indexing</p>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; flex: 1; min-width: 200px;'>";
echo "<h4 style='color: #856404; margin: 0 0 10px 0;'>⚠️ No Tags</h4>";
echo "<p style='font-size: 2em; font-weight: bold; color: #856404; margin: 0;'>$no_tag_count</p>";
echo "<p style='margin: 5px 0 0 0;'>Pages without robots tags</p>";
echo "</div>";

echo "</div>";

// Check header.php specifically
echo "<h3>🔧 Header.php Status</h3>";

if (file_exists('header.php')) {
    $header_content = file_get_contents('header.php');
    
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>📄 header.php Analysis</h4>";
    
    if (preg_match('/<meta\s+name=["\']robots["\']\s+content=["\']([^"\']*)["\'][^>]*>/i', $header_content, $matches)) {
        $robots_content = $matches[1];
        echo "<p><strong>Current robots tag:</strong> <code>" . htmlspecialchars($matches[0]) . "</code></p>";
        
        if (stripos($robots_content, 'index') !== false && stripos($robots_content, 'follow') !== false && stripos($robots_content, 'noindex') === false) {
            echo "<p style='color: green; font-weight: bold;'>✅ Header has correct 'index, follow' tag</p>";
        } else {
            echo "<p style='color: red; font-weight: bold;'>❌ Header has incorrect robots tag</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ No robots meta tag found in header.php</p>";
    }
    echo "</div>";
}

// Recommendations
echo "<h3>💡 Recommendations</h3>";

if ($incorrect_count > 0) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>🚨 Action Required</h4>";
    echo "<p>You still have <strong>$incorrect_count pages</strong> with noindex tags that are blocking search engine indexing.</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ol>";
    echo "<li>Run the <a href='fix-all-noindex-tags.php'>fix-all-noindex-tags.php</a> script</li>";
    echo "<li>Manually update any remaining files with noindex tags</li>";
    echo "<li>Re-run this verification script</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>🎉 All Good!</h4>";
    echo "<p>All your pages have correct robots meta tags for search engine indexing.</p>";
    echo "<p><strong>What happens next:</strong></p>";
    echo "<ul>";
    echo "<li>Google will re-crawl your pages (24-48 hours)</li>";
    echo "<li>Pages will start appearing in search results</li>";
    echo "<li>Check Google Search Console for indexing updates</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>📋 SEO Best Practices</h4>";
echo "<ul>";
echo "<li><strong>index, follow</strong> - Allows search engines to index and follow links</li>";
echo "<li><strong>noindex, follow</strong> - Blocks indexing but allows following links</li>";
echo "<li><strong>index, nofollow</strong> - Allows indexing but blocks following links</li>";
echo "<li><strong>noindex, nofollow</strong> - Blocks both indexing and following links</li>";
echo "</ul>";
echo "<p><strong>For Dr. Zia Naturals:</strong> Use 'index, follow' on all public pages to maximize search visibility.</p>";
echo "</div>";
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Verify Robots Tags - Dr. Zia Naturals</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h2, h3, h4 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
        code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
    </style>
</head>
<body>

<div style="text-align: center; margin: 30px 0;">
    <a href="fix-all-noindex-tags.php" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🔧 Fix NoIndex Tags</a>
    <a href="index.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🏠 Home Page</a>
    <a href="admin/products.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">⚙️ Admin Panel</a>
</div>

</body>
</html>
