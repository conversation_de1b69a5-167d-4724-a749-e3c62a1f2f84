<?php
// Test database connection and products
require_once 'config/dbconfig.php';

echo "<h2>🔍 Database Connection Test</h2>";

// 1. Test basic connection
echo "<h3>1. Testing Database Connection</h3>";
try {
    $test = $pdo->query("SELECT 1 as test");
    $result = $test->fetch();
    if ($result['test'] == 1) {
        echo "<p style='color: green;'>✅ Database connection successful</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    exit();
}

// 2. Check if products table exists
echo "<h3>2. Checking Products Table</h3>";
try {
    $tables = $pdo->query("SHOW TABLES LIKE 'products'")->fetchAll();
    if (empty($tables)) {
        echo "<p style='color: red;'>❌ Products table does not exist!</p>";
        echo "<p><strong>Solution:</strong> You need to import the database first.</p>";
        echo "<p>Import one of these files:</p>";
        echo "<ul>";
        echo "<li>database/naturals_db.sql</li>";
        echo "<li>database/naturals_simple.sql</li>";
        echo "</ul>";
        exit();
    } else {
        echo "<p style='color: green;'>✅ Products table exists</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking tables: " . $e->getMessage() . "</p>";
}

// 3. Check products table structure
echo "<h3>3. Products Table Structure</h3>";
try {
    $columns = $pdo->query("DESCRIBE products")->fetchAll();
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>" . $col['Field'] . "</td>";
        echo "<td>" . $col['Type'] . "</td>";
        echo "<td>" . $col['Null'] . "</td>";
        echo "<td>" . $col['Key'] . "</td>";
        echo "<td>" . $col['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking table structure: " . $e->getMessage() . "</p>";
}

// 4. Count products
echo "<h3>4. Products Count</h3>";
try {
    $count = $pdo->query("SELECT COUNT(*) as count FROM products")->fetch();
    echo "<p><strong>Total products:</strong> " . $count['count'] . "</p>";
    
    if ($count['count'] == 0) {
        echo "<p style='color: orange;'>⚠ No products found in database!</p>";
        echo "<p><strong>Solution:</strong> You need to add products or import sample data.</p>";
    }
    
    $active_count = $pdo->query("SELECT COUNT(*) as count FROM products WHERE status = 'active'")->fetch();
    echo "<p><strong>Active products:</strong> " . $active_count['count'] . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error counting products: " . $e->getMessage() . "</p>";
}

// 5. Test the exact query from index.php
echo "<h3>5. Testing Home Page Query</h3>";
try {
    $products = $pdo->query("
        SELECT p.*,
               AVG(pr.rating) as avg_rating,
               COUNT(pr.id) as review_count,
               COALESCE(p.sale_price, p.price) as display_price,
               CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price 
                    THEN p.price ELSE NULL END as original_price
        FROM products p
        LEFT JOIN product_reviews pr ON p.id = pr.product_id AND pr.status = 'approved'
        WHERE p.status = 'active'
        GROUP BY p.id
        ORDER BY p.product_code ASC
        LIMIT 6
    ")->fetchAll();
    
    echo "<p><strong>Query result:</strong> " . count($products) . " products found</p>";
    
    if (!empty($products)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Code</th><th>Title</th><th>Price</th><th>Sale Price</th><th>Status</th></tr>";
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td>" . $product['product_code'] . "</td>";
            echo "<td>" . htmlspecialchars($product['title']) . "</td>";
            echo "<td>Rs. " . $product['price'] . "</td>";
            echo "<td>" . ($product['sale_price'] ? "Rs. " . $product['sale_price'] : "None") . "</td>";
            echo "<td>" . $product['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error with home page query: " . $e->getMessage() . "</p>";
    echo "<p><strong>Error details:</strong> " . $e->getTraceAsString() . "</p>";
}

// 6. Check categories
echo "<h3>6. Categories Check</h3>";
try {
    $categories = $pdo->query("SELECT * FROM categories WHERE status = 'active'")->fetchAll();
    echo "<p><strong>Active categories:</strong> " . count($categories) . "</p>";
    
    if (!empty($categories)) {
        foreach ($categories as $cat) {
            echo "<p>• " . $cat['name'] . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking categories: " . $e->getMessage() . "</p>";
    echo "<p>Categories table might not exist. This is normal if you haven't imported the full database.</p>";
}

// 7. Check product_categories relationship
echo "<h3>7. Product-Category Relationships</h3>";
try {
    $relationships = $pdo->query("
        SELECT COUNT(*) as count 
        FROM product_categories pc 
        JOIN products p ON pc.product_id = p.id 
        WHERE p.status = 'active'
    ")->fetch();
    
    echo "<p><strong>Product-category relationships:</strong> " . $relationships['count'] . "</p>";
    
    if ($relationships['count'] == 0) {
        echo "<p style='color: orange;'>⚠ No product-category relationships found!</p>";
        echo "<p>This is why category pages are empty.</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking relationships: " . $e->getMessage() . "</p>";
    echo "<p>Product_categories table might not exist.</p>";
}

echo "<h3>8. 🎯 Recommendations</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff;'>";

// Check what needs to be done
$needs_database = false;
$needs_products = false;
$needs_categories = false;

try {
    $tables_check = $pdo->query("SHOW TABLES")->fetchAll();
    $table_names = array_column($tables_check, 'Tables_in_naturals');
    
    if (!in_array('products', $table_names)) {
        $needs_database = true;
    } else {
        $product_count = $pdo->query("SELECT COUNT(*) as count FROM products WHERE status = 'active'")->fetch();
        if ($product_count['count'] == 0) {
            $needs_products = true;
        }
        
        if (!in_array('categories', $table_names) || !in_array('product_categories', $table_names)) {
            $needs_categories = true;
        }
    }
} catch (Exception $e) {
    $needs_database = true;
}

if ($needs_database) {
    echo "<p style='color: red;'><strong>❌ Database not set up properly!</strong></p>";
    echo "<p><strong>Action needed:</strong> Import the database first</p>";
    echo "<ol>";
    echo "<li>Go to phpMyAdmin</li>";
    echo "<li>Create database 'naturals' if it doesn't exist</li>";
    echo "<li>Import: <code>database/naturals_db.sql</code></li>";
    echo "</ol>";
} elseif ($needs_products) {
    echo "<p style='color: orange;'><strong>⚠ Database exists but no products!</strong></p>";
    echo "<p><strong>Action needed:</strong> Add products or import sample data</p>";
} elseif ($needs_categories) {
    echo "<p style='color: orange;'><strong>⚠ Products exist but categories missing!</strong></p>";
    echo "<p><strong>Action needed:</strong> Run the fix script</p>";
    echo "<p><a href='fix-products-display.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Run Fix Script</a></p>";
} else {
    echo "<p style='color: green;'><strong>✅ Database looks good!</strong></p>";
    echo "<p>If products still not showing, there might be a PHP error. Check error logs.</p>";
}

echo "</div>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
h3 { border-bottom: 2px solid #ddd; padding-bottom: 5px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background: #f0f0f0; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
</style>
