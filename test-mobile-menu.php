<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Menu Test - Dr.<PERSON><PERSON>s</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background: #e3f2fd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .test-link {
            background: #007bff;
            color: white;
            padding: 15px;
            text-decoration: none;
            border-radius: 5px;
            text-align: center;
            transition: background 0.3s;
        }
        .test-link:hover {
            background: #0056b3;
            color: white;
        }
        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .mobile-demo {
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 10px;
            margin: 20px 0;
            background: white;
            max-width: 375px;
            margin: 20px auto;
        }
        .demo-header {
            background: #007bff;
            color: white;
            padding: 10px;
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .hamburger {
            font-size: 20px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📱 Mobile Menu Test Center</h1>
        <p>Use this page to test the mobile navigation menu across all pages of Dr.Zia Naturals website.</p>

        <div class="test-section success">
            <h2>✅ What Should Work</h2>
            <ul>
                <li><strong>Hamburger Menu Button:</strong> Three horizontal lines (☰) in top right corner</li>
                <li><strong>Menu Slide Animation:</strong> Menu should slide in from the left when clicked</li>
                <li><strong>Menu Items:</strong> Home, About, Hair Care, Skin Care, Health Care, Personal Care, Contact, Account</li>
                <li><strong>Navigation:</strong> All menu items should navigate to correct pages</li>
                <li><strong>Close Function:</strong> Menu should close when clicking outside or close button</li>
            </ul>
        </div>

        <div class="mobile-demo">
            <div class="demo-header">
                <span>Dr.Zia Naturals</span>
                <span class="hamburger">☰</span>
            </div>
            <p style="text-align: center; margin: 10px 0; color: #666;">
                ↑ This is how the hamburger menu should look on mobile
            </p>
        </div>

        <div class="instructions">
            <h3>🧪 How to Test Mobile Menu</h3>
            <ol>
                <li><strong>Resize Browser:</strong> Make your browser window narrow (mobile width) OR press F12 and select mobile view</li>
                <li><strong>Look for Hamburger:</strong> Find the ☰ button in the top right corner</li>
                <li><strong>Click Hamburger:</strong> The mobile menu should slide in from the left</li>
                <li><strong>Check Menu Items:</strong> Verify all categories are listed including "Personal Care"</li>
                <li><strong>Test Navigation:</strong> Click menu items to ensure they work</li>
                <li><strong>Test Close:</strong> Menu should close when clicking outside or the close button</li>
            </ol>
        </div>

        <h3>🔗 Test Mobile Menu on These Pages</h3>
        <div class="test-grid">
            <a href="index.php" class="test-link" target="_blank">🏠 Home Page</a>
            <a href="about-us.php" class="test-link" target="_blank">ℹ About Us</a>
            <a href="hair-care.php" class="test-link" target="_blank">💇 Hair Care</a>
            <a href="skin-care.php" class="test-link" target="_blank">🧴 Skin Care</a>
            <a href="health-care.php" class="test-link" target="_blank">💊 Health Care</a>
            <a href="personal-care.php" class="test-link" target="_blank">🧼 Personal Care</a>
            <a href="contact.php" class="test-link" target="_blank">📞 Contact</a>
            <a href="product-details.php?product=PROD001" class="test-link" target="_blank">📦 Product Details</a>
            <a href="cart.php" class="test-link" target="_blank">🛒 Shopping Cart</a>
            <a href="checkout.php" class="test-link" target="_blank">💳 Checkout</a>
            <a href="my-account.php" class="test-link" target="_blank">👤 My Account</a>
            <a href="login.php" class="test-link" target="_blank">🔐 Login</a>
        </div>

        <div class="test-section warning">
            <h3>⚠ Troubleshooting</h3>
            <p><strong>If mobile menu doesn't work:</strong></p>
            <ul>
                <li><strong>Check Console:</strong> Press F12 → Console tab → Look for JavaScript errors</li>
                <li><strong>Clear Cache:</strong> Press Ctrl+F5 to refresh and clear cache</li>
                <li><strong>Check File Paths:</strong> Ensure assets/js/ files exist and load properly</li>
                <li><strong>Test Different Browsers:</strong> Try Chrome, Firefox, Safari</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔧 Quick Fixes</h3>
            <p>If you need to apply fixes to pages:</p>
            <ul>
                <li><a href="fix-mobile-menu-all-pages.php" target="_blank">🔧 Run Mobile Menu Fix Script</a></li>
                <li><a href="update-mobile-menu.php" target="_blank">📝 Update Mobile Menu Structure</a></li>
                <li><a href="fix-mobile-menu-scripts.php" target="_blank">⚙ Fix JavaScript Issues</a></li>
            </ul>
        </div>

        <div class="test-section success">
            <h3>✅ Expected Mobile Menu Structure</h3>
            <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;">
MOBILE MENU
├── Home
├── About
├── Hair Care
├── Skin Care
├── Health Care
├── Personal Care  ← Should be visible!
├── Contact
└── Account
    ├── Shopping Cart
    ├── Checkout
    ├── My Account
    ├── Login
    └── Register
            </pre>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <p><strong>🎯 Goal:</strong> Mobile menu should work consistently across all pages with "Personal Care" visible!</p>
        </div>
    </div>
</body>
</html>
