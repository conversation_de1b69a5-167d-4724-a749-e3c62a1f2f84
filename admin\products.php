<?php
session_start();
require_once '../config/dbconfig.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit();
}

$success_message = $_SESSION['success_message'] ?? '';
$error_message = $_SESSION['error_message'] ?? '';

// Clear session messages
unset($_SESSION['success_message'], $_SESSION['error_message']);

// Handle delete product
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $product_id = intval($_GET['delete']);
    try {
        // Delete product (cascade will handle related records)
        $deleted = updateData("DELETE FROM products WHERE id = ?", [$product_id]);
        if ($deleted) {
            $success_message = 'Product deleted successfully!';
        } else {
            $error_message = 'Failed to delete product.';
        }
    } catch (Exception $e) {
        $error_message = 'Error deleting product: ' . $e->getMessage();
    }
}

// Get all products with their categories
$products = fetchAll("
    SELECT p.*, 
           GROUP_CONCAT(c.name SEPARATOR ', ') as categories,
           COUNT(pv.id) as volume_count
    FROM products p 
    LEFT JOIN product_categories pc ON p.id = pc.product_id 
    LEFT JOIN categories c ON pc.category_id = c.id 
    LEFT JOIN product_volumes pv ON p.id = pv.product_id
    GROUP BY p.id 
    ORDER BY p.product_code ASC
");

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Products Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="add-product.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Product
                    </a>
                </div>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0">All Products (<?php echo count($products); ?>)</h5>
                        </div>
                        <div class="col-auto">
                            <input type="text" id="product-search" class="form-control" placeholder="Search products...">
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped data-table">
                            <thead>
                                <tr>
                                    <th>Image</th>
                                    <th>Product Code</th>
                                    <th>Title</th>
                                    <th>Price</th>
                                    <th>Categories</th>
                                    <th>Volumes</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($products as $product): ?>
                                    <tr class="product-row">
                                        <td>
                                            <?php if ($product['image']): ?>
                                                <img src="../<?php echo htmlspecialchars($product['image']); ?>" 
                                                     alt="<?php echo htmlspecialchars($product['title']); ?>" 
                                                     style="width: 50px; height: 50px; object-fit: cover;" 
                                                     class="rounded">
                                            <?php else: ?>
                                                <div class="bg-light d-flex align-items-center justify-content-center rounded" 
                                                     style="width: 50px; height: 50px;">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($product['product_code']); ?></strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($product['title']); ?></strong>
                                                <?php if ($product['collection']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($product['collection']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <strong>Rs. <?php echo number_format($product['price'], 2); ?></strong>
                                        </td>
                                        <td>
                                            <?php if ($product['categories']): ?>
                                                <span class="badge bg-info"><?php echo htmlspecialchars($product['categories']); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">No categories</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo $product['volume_count']; ?> variants</span>
                                        </td>
                                        <td>
                                            <?php if ($product['status'] == 'active'): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php echo date('M d, Y', strtotime($product['created_at'])); ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="edit-product.php?id=<?php echo $product['id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="../product-details.php?product=<?php echo $product['product_code']; ?>" 
                                                   class="btn btn-sm btn-outline-info" title="View" target="_blank">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button type="button" 
                                                        class="btn btn-sm btn-outline-<?php echo $product['status'] == 'active' ? 'warning' : 'success'; ?>" 
                                                        onclick="toggleProductStatus(<?php echo $product['id']; ?>, '<?php echo $product['status']; ?>')"
                                                        title="<?php echo $product['status'] == 'active' ? 'Deactivate' : 'Activate'; ?>">
                                                    <i class="fas fa-<?php echo $product['status'] == 'active' ? 'pause' : 'play'; ?>"></i>
                                                </button>
                                                <a href="?delete=<?php echo $product['id']; ?>" 
                                                   class="btn btn-sm btn-outline-danger delete-btn" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary"><?php echo count($products); ?></h5>
                            <p class="card-text">Total Products</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">
                                <?php echo count(array_filter($products, function($p) { return $p['status'] == 'active'; })); ?>
                            </h5>
                            <p class="card-text">Active Products</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">
                                <?php echo count(array_filter($products, function($p) { return $p['status'] == 'inactive'; })); ?>
                            </h5>
                            <p class="card-text">Inactive Products</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-info">
                                <?php echo array_sum(array_column($products, 'volume_count')); ?>
                            </h5>
                            <p class="card-text">Total Variants</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
