    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        // Initialize DataTables
        $(document).ready(function() {
            $('.data-table').DataTable({
                "pageLength": 25,
                "responsive": true,
                "order": [[ 0, "desc" ]],
                "language": {
                    "search": "Search:",
                    "lengthMenu": "Show _MENU_ entries",
                    "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                    "paginate": {
                        "first": "First",
                        "last": "Last",
                        "next": "Next",
                        "previous": "Previous"
                    }
                }
            });
            
            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
            
            // Confirm delete actions
            $('.delete-btn').click(function(e) {
                if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                    e.preventDefault();
                }
            });
            
            // Image preview for file uploads
            $('input[type="file"]').change(function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const preview = $(this).siblings('.image-preview');
                        if (preview.length) {
                            preview.attr('src', e.target.result).show();
                        }
                    }.bind(this);
                    reader.readAsDataURL(file);
                }
            });
            
            // Form validation
            $('form').submit(function() {
                const requiredFields = $(this).find('[required]');
                let isValid = true;
                
                requiredFields.each(function() {
                    if (!$(this).val().trim()) {
                        $(this).addClass('is-invalid');
                        isValid = false;
                    } else {
                        $(this).removeClass('is-invalid');
                    }
                });
                
                if (!isValid) {
                    alert('Please fill in all required fields.');
                    return false;
                }
            });
            
            // Real-time search for products
            $('#product-search').on('keyup', function() {
                const value = $(this).val().toLowerCase();
                $('.product-row').filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                });
            });
        });
        
        // Function to update order status
        function updateOrderStatus(orderId, status) {
            if (confirm('Are you sure you want to update this order status?')) {
                $.ajax({
                    url: 'ajax/update-order-status.php',
                    method: 'POST',
                    data: {
                        order_id: orderId,
                        status: status
                    },
                    success: function(response) {
                        const result = JSON.parse(response);
                        if (result.success) {
                            location.reload();
                        } else {
                            alert('Error updating order status: ' + result.message);
                        }
                    },
                    error: function() {
                        alert('Error updating order status. Please try again.');
                    }
                });
            }
        }
        
        // Function to toggle product status
        function toggleProductStatus(productId, currentStatus) {
            const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
            const action = newStatus === 'active' ? 'activate' : 'deactivate';
            
            if (confirm(`Are you sure you want to ${action} this product?`)) {
                $.ajax({
                    url: 'ajax/toggle-product-status.php',
                    method: 'POST',
                    data: {
                        product_id: productId,
                        status: newStatus
                    },
                    success: function(response) {
                        const result = JSON.parse(response);
                        if (result.success) {
                            location.reload();
                        } else {
                            alert('Error updating product status: ' + result.message);
                        }
                    },
                    error: function() {
                        alert('Error updating product status. Please try again.');
                    }
                });
            }
        }
    </script>
</body>
</html>
