<?php
// Live Server Diagnostic Script
// Upload this file to your live server and access it to diagnose issues

echo "<h1>🔍 Live Server Diagnostic</h1>";

// 1. Check PHP version
echo "<h2>1. PHP Version</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";

// 2. Check if files exist
echo "<h2>2. File Existence Check</h2>";
$files_to_check = [
    'index.php',
    'config/dbconfig.php', 
    'header.php',
    'footer.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ {$file} - EXISTS</p>";
    } else {
        echo "<p style='color: red;'>❌ {$file} - MISSING</p>";
    }
}

// 3. Check database connection
echo "<h2>3. Database Connection Test</h2>";
try {
    // Try to include the config file
    if (file_exists('config/dbconfig.php')) {
        require_once 'config/dbconfig.php';
        echo "<p style='color: green;'>✅ Database config file loaded successfully</p>";
        
        // Test the connection
        if (isset($pdo) && $pdo) {
            $stmt = $pdo->query("SELECT 1");
            echo "<p style='color: green;'>✅ Database connection successful</p>";
        } else {
            echo "<p style='color: red;'>❌ Database connection failed - PDO not initialized</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Database config file not found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

// 4. Check directory permissions
echo "<h2>4. Directory Information</h2>";
echo "<p><strong>Current Directory:</strong> " . getcwd() . "</p>";
echo "<p><strong>Script Path:</strong> " . __FILE__ . "</p>";

// 5. Check for common issues
echo "<h2>5. Common Issues Check</h2>";

// Check if error reporting is on
echo "<p><strong>Error Reporting:</strong> " . (error_reporting() ? 'ON' : 'OFF') . "</p>";

// Check if display_errors is on
echo "<p><strong>Display Errors:</strong> " . (ini_get('display_errors') ? 'ON' : 'OFF') . "</p>";

// 6. Test a simple include
echo "<h2>6. Include Test</h2>";
try {
    if (file_exists('header.php')) {
        // Don't actually include it, just check if it's readable
        if (is_readable('header.php')) {
            echo "<p style='color: green;'>✅ header.php is readable</p>";
        } else {
            echo "<p style='color: red;'>❌ header.php is not readable</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Include test error: " . $e->getMessage() . "</p>";
}

// 7. Show PHP errors if any
echo "<h2>7. Recent PHP Errors</h2>";
$error_log = error_get_last();
if ($error_log) {
    echo "<pre style='background: #ffebee; padding: 10px; border-radius: 5px;'>";
    print_r($error_log);
    echo "</pre>";
} else {
    echo "<p style='color: green;'>✅ No recent PHP errors</p>";
}

echo "<hr>";
echo "<p><strong>Instructions:</strong></p>";
echo "<ol>";
echo "<li>Upload this file to your live server root directory</li>";
echo "<li>Access it via: http://yoursite.com/test-live.php</li>";
echo "<li>Check the results above</li>";
echo "<li>Share the results to get specific help</li>";
echo "</ol>";
?>
