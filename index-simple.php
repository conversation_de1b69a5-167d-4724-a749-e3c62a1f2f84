<!DOCTYPE html>
<html class="no-js" lang="zxx">
<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Dr.Zia Naturals | Home</title>
    <meta name="robots" content="index, follow" />
    <meta name="description" content="Dr. Zia Naturals - Premium organic cosmetics and natural beauty products.">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
</head>
<body>
    <h1>🧪 Testing Live Server</h1>
    <p>If you can see this page, PHP is working on your live server.</p>
    
    <?php
    echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
    echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
    
    // Test database connection
    echo "<h2>Database Test</h2>";
    try {
        if (file_exists('config/dbconfig.php')) {
            require_once 'config/dbconfig.php';
            echo "<p style='color: green;'>✅ Database config loaded</p>";
            
            if (isset($pdo) && $pdo) {
                echo "<p style='color: green;'>✅ Database connected successfully</p>";
                
                // Test a simple query
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
                $result = $stmt->fetch();
                echo "<p style='color: green;'>✅ Found " . $result['count'] . " active products</p>";
            } else {
                echo "<p style='color: red;'>❌ Database connection failed</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Database config file not found</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <hr>
    <p><strong>Next Steps:</strong></p>
    <ol>
        <li>If this page works, the issue is in your main index.php</li>
        <li>If database connection fails, update your database credentials</li>
        <li>Check the diagnostic results above</li>
    </ol>
    
    <p><a href="test-live.php">Run Full Diagnostic</a></p>
</body>
</html>
