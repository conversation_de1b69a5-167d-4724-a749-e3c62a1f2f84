  <!DOCTYPE html>
<html class="no-js" lang="zxx">

<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Dr.Zia Naturals | Home </title>
    <meta name="robots" content="index, follow" />
    <meta name="description" content="Dr. Zia Naturals - Premium organic cosmetics and natural beauty products. Discover our collection of chemical-free skincare, haircare, and wellness products made with finest natural ingredients.">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="keywords" content="Dr Zia Naturals, natural cosmetics, organic beauty products, natural skincare, organic makeup, cruelty-free cosmetics, natural beauty, botanical skincare, eco-friendly beauty, sustainable cosmetics, chemical-free products" />
    <meta name="author" content="codecarnival" />
<?php
// Include database configuration
require_once 'config/dbconfig.php';
include('header.php');
?>
<style>
/* Sale Price Styling for Home Page */
.prices {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.price-old {
    color: #999;
    text-decoration: line-through;
    font-size: 14px;
}

.price {
    color: #e74c3c;
    font-weight: 600;
}

.sale-badge {
    background: #e74c3c;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
    letter-spacing: 0.5px;
}
</style>
<?php

// Show success messages
if (isset($_GET['logout']) && $_GET['logout'] === 'success') {
    echo '<div class="alert alert-success alert-dismissible fade show" style="margin: 0; border-radius: 0;">
            <div class="container">
                <i class="fa fa-check-circle"></i> You have been logged out successfully.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
          </div>';
}

if (isset($_SESSION['signup_success']) && $_SESSION['signup_success'] === true) {
    echo '<div class="alert alert-success alert-dismissible fade show" style="margin: 0; border-radius: 0;">
            <div class="container">
                <i class="fa fa-check-circle"></i> Welcome to Dr. Zia Naturals! Your account has been created successfully.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
          </div>';
    unset($_SESSION['signup_success']);
}
?>
<body>

        <main class="main-content">

            <!--== Start Hero Area Wrapper ==-->
            <section class="hero-slider-area position-relative">
                <div class="swiper hero-slider-container">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide hero-slide-item">
                            <div class="container">
                                <div class="row align-items-center position-relative">
                                    <div class="col-12 col-md-6">
                                        <div class="hero-slide-content">
                                            <div class="hero-slide-text-img"><img src="assets/images/slider/text-theme1.png" width="450" height="250" alt="Image"></div>
                                            <h2 class="hero-slide-title">CLEAN FRESH</h2>
                                            <p class="hero-slide-desc">Experience the power of nature with our premium collection of organic beauty products, crafted with pure botanical ingredients for radiant, healthy skin.</p>
                                            <a class="btn btn-border-dark" href="skin-care.php">BUY NOW</a>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        <div class="hero-slide-thumb">
                                            <img src="assets/images/slider/slider1.png" width="841" height="832" alt="Image">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="hero-slide-text-shape"><img src="assets/images/slider/text1.webp" width="70" height="955" alt="Image"></div>
                            <div class="hero-slide-social-shape"></div>
                        </div>
                        <div class="swiper-slide hero-slide-item">
                            <div class="container">
                                <div class="row align-items-center position-relative">
                                    <div class="col-12 col-md-6">
                                        <div class="hero-slide-content">
                                            <div class="hero-slide-text-img"><img src="assets/images/slider/text-theme.png" width="427" height="232" alt="Image"></div>
                                            <h2 class="hero-slide-title">Facial Cream</h2>
                                            <p class="hero-slide-desc">Nourish your skin with our luxurious facial creams, formulated with organic botanicals and natural moisturizers for a youthful, glowing complexion.</p>
                                            <a class="btn btn-border-dark" href="skin-care.php">BUY NOW</a>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        <div class="hero-slide-thumb">
                                            <img src="assets/images/slider/slider2.png" width="841" height="832" alt="Image">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="hero-slide-text-shape"><img src="assets/images/slider/text1.png" width="70" height="955" alt="Image"></div>
                            <div class="hero-slide-social-shape"></div>
                        </div>
                    </div>
                    <!--== Add Pagination ==-->
                    <div class="hero-slider-pagination"></div>
                </div>
                <div class="hero-slide-social-media">
                  
                </div>
            </section>
            <!--== End Hero Area Wrapper ==-->

            <!--== Start Product Category Area Wrapper ==-->
        
            <!--== End Product Category Area Wrapper ==-->

            <!--== Start Product Area Wrapper ==-->
            <section class="section-space">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <div class="section-title text-center">
                                <h2 class="title">Top sale</h2>
                                <p>Discover our bestselling natural beauty products, loved by thousands of customers for their effectiveness and pure, organic ingredients.</p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-n4 mb-sm-n10 g-3 g-sm-6">
                        <?php
                        // Fetch active products from database
                        $products = fetchAll("
                            SELECT p.*,
                                   AVG(pr.rating) as avg_rating,
                                   COUNT(pr.id) as review_count,
                                   COALESCE(p.sale_price, p.price) as display_price,
                                   CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price
                                        THEN p.price ELSE NULL END as original_price
                            FROM products p
                            LEFT JOIN product_reviews pr ON p.id = pr.product_id AND pr.status = 'approved'
                            WHERE p.status = 'active'
                            GROUP BY p.id
                            ORDER BY p.product_code ASC
                            LIMIT 6
                        ");

                        // Check if products were found
                        if ($products && !empty($products)):
                            foreach ($products as $product):
                                $avg_rating = $product['avg_rating'] ? round($product['avg_rating'], 1) : 0;
                                $review_count = $product['review_count'] ?: 0;
                        ?>
                        <div class="col-6 col-lg-4 mb-4 mb-sm-9">
                            <!--== Start Product Item ==-->
                            <div class="product-item">
                                <div class="product-thumb">
                                    <a class="d-block" href="product-details.php?product=<?php echo htmlspecialchars($product['slug'] ?: $product['product_code']); ?>">
                                        <img src="<?php echo htmlspecialchars($product['image'] ?: 'assets/images/shop/default.png'); ?>"
                                             width="370" height="450" alt="<?php echo htmlspecialchars($product['title']); ?>">
                                    </a>
                                    <span class="flag-new">new</span>
                                    <div class="product-action">
                                       
                                        </button>
                                        <button type="button" class="product-action-btn action-btn-cart"
                                                onclick="addToCart(<?php echo $product['id']; ?>)">
                                            <span>Add to cart</span>
                                        </button>
                                       
                                    </div>
                                </div>
                                <div class="product-info">
                                    <div class="product-rating">
                                        <div class="rating">
                                            <?php
                                            // Use actual average rating or default to 5 stars if no reviews
                                            $display_rating = $avg_rating > 0 ? $avg_rating : 5;
                                            // Show all 5 stars, but highlight only the rating amount
                                            for ($i = 1; $i <= 5; $i++):
                                                if ($i <= $display_rating): ?>
                                                    <i class="fa fa-star" style="color: #ffc107;"></i>
                                                <?php else: ?>
                                                    <i class="fa fa-star" style="color: #ddd;"></i>
                                                <?php endif;
                                            endfor; ?>
                                        </div>
                                        <div class="reviews text-muted small"><?php echo $review_count; ?> review<?php echo $review_count != 1 ? 's' : ''; ?></div>
                                    </div>
                                    <h4 class="title">
                                        <a href="product-details.php?product=<?php echo htmlspecialchars($product['slug'] ?: $product['product_code']); ?>">
                                            <?php echo htmlspecialchars($product['title']); ?>
                                        </a>
                                    </h4>
                                    <div class="prices">
                                        <?php if ($product['original_price']): ?>
                                            <span class="price-old">Rs. <?php echo number_format($product['original_price'], 0); ?></span>
                                            <span class="price">Rs. <?php echo number_format($product['display_price'], 0); ?></span>
                                        <?php else: ?>
                                            <span class="price">Rs. <?php echo number_format($product['display_price'], 0); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="product-action-bottom">
                                    <button type="button" class="product-action-btn action-btn-quick-view"
                                            data-bs-toggle="modal" data-bs-target="#action-QuickViewModal"
                                            data-product-id="<?php echo $product['id']; ?>">
                                        <i class="fa fa-expand"></i>
                                    </button>
                                    <button type="button" class="product-action-btn action-btn-wishlist"
                                            data-bs-toggle="modal" data-bs-target="#action-WishlistModal">
                                        <i class="fa fa-heart-o"></i>
                                    </button>
                                    <button type="button" class="product-action-btn action-btn-cart"
                                            onclick="addToCart(<?php echo $product['id']; ?>)">
                                        <span>Add to cart</span>
                                    </button>
                                </div>
                            </div>
                            <!--== End Product Item ==-->
                        </div>
                        <?php
                            endforeach;
                        else:
                        ?>
                            <div class="col-12">
                                <div class="text-center py-5">
                                    <h4>No Products Available</h4>
                                    <p>Products will be available soon. Please check back later.</p>
                                    <p><a href="test-connection.php" class="btn btn-primary">Test Database Connection</a></p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    </div>
                </div>
            </section>
            <!--== End Product Area Wrapper ==-->

  
          

          

            <!--== Start News Letter Area Wrapper ==-->
            <section class="section-space pt-0">
                <div class="container">
                    <div class="newsletter-content-wrap" data-bg-img="assets/images/photos/bg1.png">
                        <div class="newsletter-content">
                            <div class="section-title mb-0">
                                <h2 class="title">Join with us</h2>
                                <p>Stay updated with our latest natural beauty tips, product launches, and exclusive offers for healthy, radiant skin.</p>
                            </div>
                        </div>
                        <div class="newsletter-form">
                            <form>
                                <input type="email" class="form-control" placeholder="enter your email">
                                <button class="btn-submit" type="submit"><i class="fa fa-paper-plane"></i></button>
                            </form>
                        </div>
                    </div>
                </div>
            </section>
            <!--== End News Letter Area Wrapper ==-->

        </main>

       <?php
       include('footer.php')
       ?>
        <!--== Scroll Top Button ==-->
        <div id="scroll-to-top" class="scroll-to-top"><span class="fa fa-angle-up"></span></div>

        <!--== Start Product Quick Wishlist Modal ==-->
        <aside class="product-action-modal modal fade" id="action-WishlistModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-body">
                        <div class="product-action-view-content">
                            <button type="button" class="btn-close" data-bs-dismiss="modal">
                                <i class="fa fa-times"></i>
                            </button>
                            <div class="modal-action-messages">
                                <i class="fa fa-check-square-o"></i> Added to wishlist successfully!
                            </div>
                            <div class="modal-action-product">
                                <div class="thumb">
                                    <img src="assets/images/shop/modal1.png" alt="Organic Food Juice" width="466" height="320">
                                </div>
                                <h4 class="product-name"><a href="product-details.php">Readable content DX22</a></h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>
        <!--== End Product Quick Wishlist Modal ==-->

        <!--== Start Product Quick Add Cart Modal ==-->
        <aside class="product-action-modal modal fade" id="action-CartAddModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-body">
                        <div class="product-action-view-content">
                            <button type="button" class="btn-close" data-bs-dismiss="modal">
                                <i class="fa fa-times"></i>
                            </button>
                            <div class="modal-action-messages">
                                <i class="fa fa-check-square-o"></i> Added to cart successfully!
                            </div>
                            <div class="modal-action-product">
                                <div class="thumb">
                                    <img src="assets/images/shop/modal1.png" alt="Organic Food Juice" width="466" height="320">
                                </div>
                                <h4 class="product-name"><a href="product-details.php">Readable content DX22</a></h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>
        <!--== End Product Quick Add Cart Modal ==-->

        <?php include 'includes/search-modal.php'; ?>

        <!--== Start Product Quick View Modal ==-->
        <aside class="product-cart-view-modal modal fade" id="action-QuickViewModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-body">
                        <div class="product-quick-view-content">
                            <button type="button" class="btn-close" data-bs-dismiss="modal">
                                <span class="fa fa-close"></span>
                            </button>
                            <div class="container">
                                <div class="row">
                                    <div class="col-lg-6">
                                        <!--== Start Product Thumbnail Area ==-->
                                        <div class="product-single-thumb">
                                            <img src="assets/images/shop/quick-view1.png" width="544" height="560" alt="Image-HasTech">
                                        </div>
                                        <!--== End Product Thumbnail Area ==-->
                                    </div>
                                    <div class="col-lg-6">
                                        <!--== Start Product Info Area ==-->
                                        <div class="product-details-content">
                                            <h5 class="product-details-collection">Premioum collection</h5>
                                            <h3 class="product-details-title">Offbline Instant Age Rewind Eraser.</h3>
                                            <div class="product-details-review mb-5">
                                                <div class="product-review-icon">
                                                    <i class="fa fa-star-o"></i>
                                                    <i class="fa fa-star-o"></i>
                                                    <i class="fa fa-star-o"></i>
                                                    <i class="fa fa-star-o"></i>
                                                    <i class="fa fa-star-half-o"></i>
                                                </div>
                                                <button type="button" class="product-review-show">150 reviews</button>
                                            </div>
                                            <p class="mb-6">Experience the transformative power of our premium natural skincare collection. This advanced anti-aging formula combines organic botanicals with cutting-edge natural science to deliver visible results while nourishing your skin with pure, gentle ingredients.</p>
                                            <div class="product-details-pro-qty">
                                                <div class="pro-qty">
                                                    <input type="text" title="Quantity" value="01">
                                                </div>
                                            </div>
                                            <div class="product-details-action">
                                                <h4 class="price">$254.22</h4>
                                                <div class="product-details-cart-wishlist">
                                                    <button type="button" class="btn" data-bs-toggle="modal" data-bs-target="#action-CartAddModal">Add to cart</button>
                                                </div>
                                            </div>
                                        </div>
                                        <!--== End Product Info Area ==-->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>
        <!--== End Product Quick View Modal ==-->

        <!--== Start Aside Cart ==-->
        <aside class="aside-cart-wrapper offcanvas offcanvas-end" tabindex="-1" id="AsideOffcanvasCart" aria-labelledby="offcanvasRightLabel">
            <div class="offcanvas-header">
                <h1 class="d-none" id="offcanvasRightLabel">Shopping Cart</h1>
                <button class="btn-aside-cart-close" data-bs-dismiss="offcanvas" aria-label="Close">Shopping Cart <i class="fa fa-chevron-right"></i></button>
            </div>
            <div class="offcanvas-body">
                <ul class="aside-cart-product-list">
                    <li class="aside-product-list-item">
                        <a href="#/" class="remove">×</a>
                        <a href="product-details.php">
                            <img src="assets/images/shop/cart1.png" width="68" height="84" alt="Image">
                            <span class="product-title">Leather Mens Slipper</span>
                        </a>
                        <span class="product-price">1 × £69.99</span>
                    </li>
                    <li class="aside-product-list-item">
                        <a href="#/" class="remove">×</a>
                        <a href="product-details.php">
                            <img src="assets/images/shop/cart2.png" width="68" height="84" alt="Image">
                            <span class="product-title">Quickiin Mens shoes</span>
                        </a>
                        <span class="product-price">1 × £20.00</span>
                    </li>
                </ul>
                <p class="cart-total"><span>Subtotal:</span><span class="amount">£89.99</span></p>
                <a class="btn-total" href="product-cart.php">View cart</a>
                <a class="btn-total" href="product-checkout.php">Checkout</a>
            </div>
        </aside>
        <!--== End Aside Cart ==-->

        <!--== Start Aside Menu ==-->
        <aside class="off-canvas-wrapper offcanvas offcanvas-start" tabindex="-1" id="AsideOffcanvasMenu" aria-labelledby="offcanvasExampleLabel">
            <div class="offcanvas-header">
                <h1 class="d-none" id="offcanvasExampleLabel">Aside Menu</h1>
                <button class="btn-menu-close" data-bs-dismiss="offcanvas" aria-label="Close">menu <i class="fa fa-chevron-left"></i></button>
            </div>
            <div class="offcanvas-body">
                <div id="offcanvasNav" class="offcanvas-menu-nav">
                    <ul>
                        <li class="offcanvas-nav-parent"><a class="offcanvas-nav-item" href="index.php">Home</a></li>
                        <li class="offcanvas-nav-parent"><a class="offcanvas-nav-item" href="about-us.php">About</a></li>
                        <li class="offcanvas-nav-parent"><a class="offcanvas-nav-item" href="hair-care.php">Hair Care</a></li>
                        <li class="offcanvas-nav-parent"><a class="offcanvas-nav-item" href="skin-care.php">Skin Care</a></li>
                        <li class="offcanvas-nav-parent"><a class="offcanvas-nav-item" href="health-care.php">Health Care</a></li>
                        <li class="offcanvas-nav-parent"><a class="offcanvas-nav-item" href="personal-care.php">Personal Care</a></li>
                        <li class="offcanvas-nav-parent"><a class="offcanvas-nav-item" href="contact.php">Contact</a></li>
                        <li class="offcanvas-nav-parent"><a class="offcanvas-nav-item" href="#">Account</a>
                            <ul>
                                <li><a href="cart.php">Shopping Cart</a></li>
                                <li><a href="checkout.php">Checkout</a></li>
                                <li><a href="my-account.php">My Account</a></li>
                                <li><a href="login.php">Login</a></li>
                                <li><a href="register.php">Register</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </aside>
        <!--== End Aside Menu ==-->

    </div>
    <!--== Wrapper End ==-->

    <!-- JS Vendor, Plugins & Activation Script Files -->

    <!-- Vendors JS -->
    <script src="./assets/js/vendor/modernizr-3.11.7.min.js"></script>
    <script src="./assets/js/vendor/jquery-3.6.0.min.js"></script>
    <script src="./assets/js/vendor/jquery-migrate-3.3.2.min.js"></script>
    <script src="./assets/js/vendor/bootstrap.bundle.min.js"></script>

    <!-- Plugins JS -->
    <script src="./assets/js/plugins/swiper-bundle.min.js"></script>
    <script src="./assets/js/plugins/fancybox.min.js"></script>
    <script src="./assets/js/plugins/jquery.nice-select.min.js"></script>

    <!-- Custom Main JS -->
    <script src="./assets/js/main.js"></script>

    <script>
        // Add to cart function
        function addToCart(productId) {
            // Show loading state
            const cartButtons = document.querySelectorAll(`[onclick="addToCart(${productId})"]`);
            cartButtons.forEach(btn => {
                btn.disabled = true;
                btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Adding...';
            });

            // Send AJAX request
            fetch('cart/add-to-cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: 1
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success notification
                    showCartNotification('Product added to cart successfully!', 'success');

                    // Update cart count
                    updateCartCount(data.cart_count);

                    // Optional: Show modal
                    // const modal = new bootstrap.Modal(document.getElementById('action-CartAddModal'));
                    // modal.show();
                } else {
                    showCartNotification('Error adding to cart: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error adding to cart. Please try again.');
            })
            .finally(() => {
                // Reset button state
                cartButtons.forEach(btn => {
                    btn.disabled = false;
                    btn.innerHTML = '<span>Add to cart</span>';
                });
            });
        }

        // Function to show cart notifications
        function showCartNotification(message, type = 'success') {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.cart-notification');
            existingNotifications.forEach(notification => notification.remove());

            // Create notification element
            const notification = document.createElement('div');
            notification.className = `cart-notification alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
            notification.innerHTML = `
                <i class="fa fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            // Add to page
            document.body.appendChild(notification);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        // Function to update cart count
        function updateCartCount(count) {
            const cartCountElement = document.querySelector('.cart-count');

            if (count > 0) {
                if (cartCountElement) {
                    cartCountElement.textContent = count;
                    cartCountElement.style.display = 'inline-block';
                } else {
                    // Create cart count badge if it doesn't exist
                    const cartButton = document.querySelector('a[href="cart.php"]');
                    if (cartButton) {
                        const badge = document.createElement('span');
                        badge.className = 'cart-count badge bg-danger position-absolute top-0 start-100 translate-middle rounded-pill';
                        badge.textContent = count;
                        cartButton.appendChild(badge);
                    }
                }
            } else {
                if (cartCountElement) {
                    cartCountElement.style.display = 'none';
                }
            }
        }

        // Load cart count on page load
        document.addEventListener('DOMContentLoaded', function() {
            fetch('cart/get-cart-count.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateCartCount(data.cart_count);
                    }
                })
                .catch(error => console.log('Error loading cart count:', error));
        });
    </script>

</body>

</html>