# Dr.<PERSON><PERSON> Naturals - Setup Guide

## Database Setup Instructions

### Method 1: Using phpMyAdmin (Recommended)

1. **Open phpMyAdmin**
   - Go to `http://localhost/phpmyadmin`

2. **Create Database**
   - Click on "Databases" tab
   - Enter database name: `naturals`
   - Select collation: `utf8mb4_unicode_ci`
   - Click "Create"

3. **Import Database**
   - Select the `naturals` database from the left sidebar
   - Click on "Import" tab
   - Click "Choose File" and select: `database/naturals_simple.sql`
   - Click "Go" to import

### 8Method 2: Using Automatic Setup Script

1. **Run Setup Script**
   - Open browser and go to: `http://localhost/zia/install/setup_database.php`
   - Follow the on-screen instructions

### Method 3: Manual SQL Execution

If you're still getting errors, try importing tables one by one:

1. **Create Database First**
   ```sql
   CREATE DATABASE IF NOT EXISTS `naturals` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   USE `naturals`;
   ```

2. **Create Tables One by One**
   Copy and paste each CREATE TABLE statement from `database/naturals_simple.sql` individually.

## Admin Panel Access

After successful database setup:

1. **Admin Login**
   - URL: `http://localhost/zia/admin/login.php`
   - Username: `admin`
   - Password: `admin123`

2. **Add Products**
   - Go to Admin Panel → Add Product
   - Fill in product details
   - Upload product image
   - Add volume variants
   - Save product

3. **View Website**
   - Go to: `http://localhost/zia/index.php`
   - Products added via admin will appear on homepage

## Testing the System

1. **Test Product Display**
   - Visit homepage
   - Check if products are displayed

2. **Test Cart Functionality**
   - Click "Add to Cart" on any product
   - Go to cart page
   - Update quantities
   - Proceed to checkout

3. **Test Checkout**
   - Fill in customer details
   - Place order
   - Check order confirmation page

4. **Test Admin Panel**
   - Login to admin panel
   - View orders in Orders section
   - Update order status

## Troubleshooting

### Database Import Errors

If you get SQL errors during import:

1. **Check MySQL Version**
   - Some syntax might not be compatible with older MySQL versions
   - Try using the simplified SQL file: `database/naturals_simple.sql`

2. **Import in Smaller Chunks**
   - Import CREATE TABLE statements first
   - Then import INSERT statements separately

3. **Check File Encoding**
   - Make sure the SQL file is saved in UTF-8 encoding

### Common Issues

1. **Blank Admin Login Page**
   - Check if database connection is working
   - Verify database credentials in `config/dbconfig.php`

2. **Products Not Showing**
   - Check if products table has data
   - Verify product status is 'active'

3. **Cart Not Working**
   - Check browser console for JavaScript errors
   - Verify cart tables exist in database

### File Permissions

Make sure these directories are writable:
- `assets/images/shop/` (for product image uploads)

### Database Connection

If you get database connection errors, check:
- MySQL service is running
- Database credentials in `config/dbconfig.php` are correct
- Database `naturals` exists

## Support

If you encounter any issues:

1. Check the browser console for JavaScript errors
2. Check PHP error logs
3. Verify all files are uploaded correctly
4. Ensure database tables are created successfully

## Next Steps

After successful setup:

1. **Customize Products**
   - Add your actual product images
   - Update product descriptions
   - Set correct prices

2. **Configure Settings**
   - Update shipping fees
   - Customize email templates
   - Set up payment methods

3. **Test Thoroughly**
   - Test all cart functionality
   - Test checkout process
   - Test admin order management

The system is now ready for use!
