<?php
session_start();
require_once 'config/dbconfig.php';

echo "<h2>🛒 Cart System Fix & Clear</h2>";

// Clear session-based cart
if (isset($_SESSION['cart'])) {
    unset($_SESSION['cart']);
    echo "<p style='color: green;'>✅ Cleared session-based cart (old system)</p>";
} else {
    echo "<p style='color: blue;'>ℹ️ No session-based cart found</p>";
}

// Clear database-based cart for current session
$session_id = session_id();
try {
    $deleted = executeQuery("DELETE FROM cart_items WHERE session_id = ?", [$session_id]);
    echo "<p style='color: green;'>✅ Cleared database-based cart for current session</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error clearing database cart: " . $e->getMessage() . "</p>";
}

// Clear cart count
if (isset($_SESSION['cart_count'])) {
    unset($_SESSION['cart_count']);
    echo "<p style='color: green;'>✅ Cleared cart count</p>";
}

echo "<hr>";
echo "<h3>🎯 Cart System Information</h3>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>✅ Correct Cart System (Fixed Pricing)</h4>";
echo "<p><strong>File:</strong> cart.php</p>";
echo "<p><strong>Storage:</strong> Database-based</p>";
echo "<p><strong>Features:</strong> Proper sale pricing, persistent across sessions</p>";
echo "<p><strong>Link:</strong> <a href='cart.php' style='color: #007bff;'>cart.php</a> (This is what your header links to)</p>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>⚠️ Old Cart System (Has Pricing Issues)</h4>";
echo "<p><strong>File:</strong> product-cart.php</p>";
echo "<p><strong>Storage:</strong> Session-based</p>";
echo "<p><strong>Issues:</strong> Uses old pricing, not connected to database</p>";
echo "<p><strong>Status:</strong> Should not be used</p>";
echo "</div>";

echo "<hr>";
echo "<h3>🧪 Test the Fixed Cart System</h3>";

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>Steps to Test:</h4>";
echo "<ol>";
echo "<li><strong>Go to a product page</strong> (Hair Care, Skin Care, etc.)</li>";
echo "<li><strong>Add a product to cart</strong> using the 'Add to Cart' button</li>";
echo "<li><strong>Click the cart icon</strong> in the header (goes to cart.php)</li>";
echo "<li><strong>Verify pricing</strong> - should show correct sale prices</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>❌ Don't Use These URLs:</h4>";
echo "<ul>";
echo "<li><del>product-cart.php</del> - Old session-based cart</li>";
echo "<li><del>Any direct links to product-cart.php</del></li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h3>🔗 Quick Links to Test</h3>";

echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; margin: 20px 0;'>";
echo "<a href='hair-care.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🧴 Hair Care Products</a>";
echo "<a href='skin-care.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🧴 Skin Care Products</a>";
echo "<a href='cart.php' style='background: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🛒 View Cart (Fixed)</a>";
echo "<a href='admin/products.php' style='background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>⚙️ Admin Panel</a>";
echo "</div>";

echo "<hr>";
echo "<h3>📊 Current Product Pricing</h3>";

try {
    $products = fetchAll("
        SELECT title, price, sale_price,
               COALESCE(sale_price, price) as display_price
        FROM products 
        WHERE status = 'active' 
        ORDER BY title ASC 
        LIMIT 5
    ");
    
    if ($products) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 15px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Product</th>";
        echo "<th style='padding: 8px;'>Original Price</th>";
        echo "<th style='padding: 8px;'>Sale Price</th>";
        echo "<th style='padding: 8px;'>Cart Will Show</th>";
        echo "</tr>";
        
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($product['title']) . "</td>";
            echo "<td style='padding: 8px;'>Rs. " . number_format($product['price'], 2) . "</td>";
            echo "<td style='padding: 8px;'>" . ($product['sale_price'] ? 'Rs. ' . number_format($product['sale_price'], 2) : 'No sale') . "</td>";
            echo "<td style='padding: 8px; font-weight: bold; color: #007bff;'>Rs. " . number_format($product['display_price'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error loading products: " . $e->getMessage() . "</p>";
}

echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center;'>";
echo "<h3>🎉 Cart Cleared & Ready!</h3>";
echo "<p>Your cart has been cleared. Now add products using the correct cart system.</p>";
echo "<p><strong>Next:</strong> Go to a product page and add items to cart, then check cart.php</p>";
echo "</div>";

// Auto-redirect after 5 seconds
echo "<script>";
echo "setTimeout(function() {";
echo "    window.location.href = 'hair-care.php';";
echo "}, 5000);";
echo "</script>";

echo "<p style='text-align: center; color: #666; margin-top: 30px;'>";
echo "Redirecting to Hair Care page in 5 seconds...";
echo "</p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
table { border-collapse: collapse; width: 100%; margin: 15px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; font-weight: bold; }
</style>
