<?php
require_once 'config/dbconfig.php';

echo "<h2>🧴 Setting Up Personal Care Category</h2>";

try {
    // Check if Personal Care category exists
    $existing_category = fetchSingle("SELECT * FROM categories WHERE name = 'Personal Care'");
    
    if (!$existing_category) {
        // Create Personal Care category
        $category_id = executeQuery("
            INSERT INTO categories (name, description, status, created_at) 
            VALUES (?, ?, 'active', NOW())
        ", [
            'Personal Care',
            'Premium personal care products for daily hygiene and wellness'
        ]);
        
        echo "<p style='color: green;'>✅ Created Personal Care category (ID: $category_id)</p>";
    } else {
        $category_id = $existing_category['id'];
        echo "<p style='color: blue;'>ℹ️ Personal Care category already exists (ID: $category_id)</p>";
    }
    
    // Sample Personal Care products to add
    $personal_care_products = [
        [
            'title' => 'Natural Body Soap',
            'description' => 'Gentle cleansing soap made with natural ingredients for soft and smooth skin.',
            'price' => 299.00,
            'sale_price' => 249.00,
            'product_code' => 'PC001',
            'image' => 'assets/images/products/body-soap.jpg',
            'key_benefits' => 'Deep cleansing • Moisturizing • Natural ingredients • Gentle on skin',
            'how_to_use' => 'Wet skin with water • Apply soap and create lather • Massage gently • Rinse thoroughly'
        ],
        [
            'title' => 'Herbal Deodorant',
            'description' => 'Long-lasting natural deodorant with herbal extracts for all-day freshness.',
            'price' => 399.00,
            'sale_price' => 349.00,
            'product_code' => 'PC002',
            'image' => 'assets/images/products/herbal-deodorant.jpg',
            'key_benefits' => '24-hour protection • Natural herbs • No harmful chemicals • Fresh fragrance',
            'how_to_use' => 'Apply to clean, dry underarms • Use daily for best results • Suitable for sensitive skin'
        ],
        [
            'title' => 'Moisturizing Body Lotion',
            'description' => 'Rich moisturizing lotion with natural oils to keep your skin hydrated all day.',
            'price' => 599.00,
            'sale_price' => 499.00,
            'product_code' => 'PC003',
            'image' => 'assets/images/products/body-lotion.jpg',
            'key_benefits' => 'Deep moisturizing • Natural oils • Non-greasy formula • Long-lasting hydration',
            'how_to_use' => 'Apply to clean skin • Massage gently until absorbed • Use daily for best results'
        ],
        [
            'title' => 'Natural Hand Sanitizer',
            'description' => 'Alcohol-based hand sanitizer with natural moisturizers to keep hands clean and soft.',
            'price' => 199.00,
            'sale_price' => 149.00,
            'product_code' => 'PC004',
            'image' => 'assets/images/products/hand-sanitizer.jpg',
            'key_benefits' => '99.9% germ protection • Natural moisturizers • Quick-drying • Portable size',
            'how_to_use' => 'Apply small amount to palm • Rub hands together • Allow to dry completely'
        ],
        [
            'title' => 'Refreshing Body Spray',
            'description' => 'Light and refreshing body spray with natural fragrance for instant freshness.',
            'price' => 449.00,
            'sale_price' => 399.00,
            'product_code' => 'PC005',
            'image' => 'assets/images/products/body-spray.jpg',
            'key_benefits' => 'Long-lasting fragrance • Natural ingredients • Refreshing scent • Travel-friendly',
            'how_to_use' => 'Spray from 6 inches away • Apply to pulse points • Use throughout the day as needed'
        ]
    ];
    
    echo "<h3>📦 Adding Sample Personal Care Products</h3>";
    
    foreach ($personal_care_products as $product) {
        // Check if product already exists
        $existing_product = fetchSingle("SELECT id FROM products WHERE product_code = ?", [$product['product_code']]);
        
        if (!$existing_product) {
            // Insert product
            $product_id = executeQuery("
                INSERT INTO products (
                    title, description, price, sale_price, product_code, image, 
                    key_benefits, how_to_use, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW())
            ", [
                $product['title'],
                $product['description'],
                $product['price'],
                $product['sale_price'],
                $product['product_code'],
                $product['image'],
                $product['key_benefits'],
                $product['how_to_use']
            ]);
            
            // Link product to Personal Care category
            executeQuery("
                INSERT INTO product_categories (product_id, category_id) 
                VALUES (?, ?)
            ", [$product_id, $category_id]);
            
            echo "<p style='color: green;'>✅ Added: " . htmlspecialchars($product['title']) . " (ID: $product_id)</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ Already exists: " . htmlspecialchars($product['title']) . "</p>";
        }
    }
    
    // Get count of personal care products
    $product_count = fetchSingle("
        SELECT COUNT(*) as count 
        FROM products p
        JOIN product_categories pc ON p.id = pc.product_id
        WHERE pc.category_id = ? AND p.status = 'active'
    ", [$category_id]);
    
    echo "<h3>📊 Personal Care Setup Complete</h3>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4 style='color: #155724; margin: 0 0 15px 0;'>✅ Setup Summary</h4>";
    echo "<ul style='margin: 0; color: #155724;'>";
    echo "<li><strong>Category:</strong> Personal Care (ID: $category_id)</li>";
    echo "<li><strong>Products Added:</strong> " . count($personal_care_products) . "</li>";
    echo "<li><strong>Total Active Products:</strong> " . $product_count['count'] . "</li>";
    echo "<li><strong>Page URL:</strong> <a href='personal-care.php' style='color: #155724;'>personal-care.php</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4 style='color: #721c24;'>❌ Error</h4>";
    echo "<p style='color: #721c24;'>Error setting up Personal Care: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Setup Personal Care - Dr. Zia Naturals</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h2, h3, h4 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
    </style>
</head>
<body>

<div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h4>🎯 What Was Created</h4>
    <ul>
        <li><strong>New Page:</strong> personal-care.php</li>
        <li><strong>Navigation:</strong> Added to main menu and mobile menu</li>
        <li><strong>Database:</strong> Personal Care category and sample products</li>
        <li><strong>Features:</strong> Same design as other category pages</li>
    </ul>
</div>

<div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h4>📋 Next Steps</h4>
    <ol>
        <li><strong>Visit the page:</strong> <a href="personal-care.php">personal-care.php</a></li>
        <li><strong>Add real product images</strong> to assets/images/products/</li>
        <li><strong>Update product details</strong> in admin panel</li>
        <li><strong>Test add to cart functionality</strong></li>
    </ol>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="personal-care.php" style="background: #667eea; color: white; padding: 12px 25px; text-decoration: none; border-radius: 25px; margin: 5px;">🧴 View Personal Care Page</a>
    <a href="admin/products.php" style="background: #28a745; color: white; padding: 12px 25px; text-decoration: none; border-radius: 25px; margin: 5px;">⚙️ Manage Products</a>
    <a href="index.php" style="background: #007bff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 25px; margin: 5px;">🏠 Home Page</a>
</div>

</body>
</html>
