<?php
echo "<h2>🤖 Fix All NoIndex Tags</h2>";

// List of all PHP files that might have noindex tags
$php_files = [
    'about-us.php',
    'account-login.php',
    'cart.php',
    'checkout.php',
    'contact.php',
    'hair-care.php',
    'health-care.php',
    'header.php',
    'index.php',
    'login.php',
    'logout.php',
    'my-account.php',
    'naturals.php',
    'order-success.php',
    'product-cart.php',
    'product-checkout.php',
    'product-details-group.php',
    'product-details-normal.php',
    'product-details-updated.php',
    'product-details.php',
    'product-four-columns.php',
    'product-left-sidebar.php',
    'product-updated.php',
    'product.details.affiliate.php',
    'product.php',
    'search.php',
    'search-simple.php',
    'signup.php',
    'skin-care.php'
];

$updated_files = [];
$error_files = [];
$no_change_files = [];

echo "<h3>🔍 Scanning Files for NoIndex Tags</h3>";

foreach ($php_files as $file) {
    if (file_exists($file)) {
        echo "<h4>📄 Processing: $file</h4>";
        
        $content = file_get_contents($file);
        $original_content = $content;
        
        // Check for various noindex patterns
        $patterns_to_fix = [
            // Pattern 1: noindex without follow
            '/<meta\s+name=["\']robots["\']\s+content=["\']noindex["\'][^>]*>/i',
            // Pattern 2: noindex, follow
            '/<meta\s+name=["\']robots["\']\s+content=["\']noindex,\s*follow["\'][^>]*>/i',
            // Pattern 3: noindex with other attributes
            '/<meta\s+name=["\']robots["\']\s+content=["\'][^"\']*noindex[^"\']*["\'][^>]*>/i'
        ];
        
        $found_noindex = false;
        
        foreach ($patterns_to_fix as $pattern) {
            if (preg_match($pattern, $content)) {
                $found_noindex = true;
                echo "<p style='color: orange;'>⚠️ Found noindex tag in $file</p>";
                
                // Replace with index, follow
                $content = preg_replace($pattern, '<meta name="robots" content="index, follow" />', $content);
                break;
            }
        }
        
        if ($found_noindex) {
            if ($content !== $original_content) {
                if (file_put_contents($file, $content)) {
                    echo "<p style='color: green;'>✅ Updated robots tag in $file</p>";
                    $updated_files[] = $file;
                } else {
                    echo "<p style='color: red;'>❌ Failed to update $file</p>";
                    $error_files[] = $file;
                }
            } else {
                echo "<p style='color: blue;'>ℹ️ No changes made to $file</p>";
                $no_change_files[] = $file;
            }
        } else {
            // Check if it already has index, follow
            if (preg_match('/<meta\s+name=["\']robots["\']\s+content=["\']index,\s*follow["\'][^>]*>/i', $content)) {
                echo "<p style='color: green;'>✅ $file already has correct robots tag</p>";
            } else {
                echo "<p style='color: blue;'>ℹ️ $file has no robots meta tag</p>";
            }
        }
        
        echo "<hr style='margin: 10px 0;'>";
        
    } else {
        echo "<p style='color: red;'>❌ File not found: $file</p>";
    }
}

echo "<h3>📊 Summary</h3>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>✅ Successfully Updated Files (" . count($updated_files) . "):</h4>";
if (!empty($updated_files)) {
    echo "<ul>";
    foreach ($updated_files as $file) {
        echo "<li>$file</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No files needed updating.</p>";
}
echo "</div>";

if (!empty($error_files)) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>❌ Files with Errors (" . count($error_files) . "):</h4>";
    echo "<ul>";
    foreach ($error_files as $file) {
        echo "<li>$file</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🎯 What Was Changed:</h4>";
echo "<ul>";
echo "<li><strong>From:</strong> <code>&lt;meta name=\"robots\" content=\"noindex\" /&gt;</code></li>";
echo "<li><strong>From:</strong> <code>&lt;meta name=\"robots\" content=\"noindex, follow\" /&gt;</code></li>";
echo "<li><strong>To:</strong> <code>&lt;meta name=\"robots\" content=\"index, follow\" /&gt;</code></li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>📋 Next Steps:</h4>";
echo "<ol>";
echo "<li><strong>Wait 24-48 hours</strong> for Google to re-crawl your pages</li>";
echo "<li><strong>Check Google Search Console</strong> for updated indexing status</li>";
echo "<li><strong>Submit sitemap</strong> to Google Search Console if you haven't already</li>";
echo "<li><strong>Request re-indexing</strong> for important pages in Search Console</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center;'>";
echo "<h3>🎉 NoIndex Tags Fixed!</h3>";
echo "<p>All pages should now be indexable by search engines.</p>";
echo "<p><strong>Total files updated:</strong> " . count($updated_files) . "</p>";
echo "</div>";
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Fix NoIndex Tags</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h2, h3, h4 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
        code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>

<div style="text-align: center; margin: 30px 0;">
    <a href="index.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🏠 Home Page</a>
    <a href="about-us.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">ℹ️ About Us</a>
    <a href="contact.php" style="background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">📞 Contact</a>
    <a href="skin-care.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🧴 Skin Care</a>
</div>

</body>
</html>
