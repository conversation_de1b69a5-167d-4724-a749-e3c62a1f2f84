<?php
session_start();
require_once 'config/dbconfig.php';

// Redirect if already logged in
if (isset($_SESSION['customer_logged_in']) && $_SESSION['customer_logged_in'] === true) {
    header('Location: index.php');
    exit;
}

$error_message = '';
$success_message = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'login') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($email) || empty($password)) {
        $error_message = 'Please enter both email and password.';
    } else {
        try {
            // Check if customer exists and is active
            $customer = fetchSingle("
                SELECT * FROM customers 
                WHERE email = ? AND status = 'active'
            ", [$email]);
            
            if ($customer && password_verify($password, $customer['password'])) {
                // Login successful
                $_SESSION['customer_logged_in'] = true;
                $_SESSION['customer_id'] = $customer['id'];
                $_SESSION['customer_name'] = $customer['first_name'] . ' ' . $customer['last_name'];
                $_SESSION['customer_email'] = $customer['email'];
                
                // Update last login
                executeQuery("UPDATE customers SET updated_at = NOW() WHERE id = ?", [$customer['id']]);
                
                // Redirect to intended page or homepage
                $redirect_url = $_SESSION['redirect_after_login'] ?? 'index.php';
                unset($_SESSION['redirect_after_login']);
                header('Location: ' . $redirect_url);
                exit;
            } else {
                $error_message = 'Invalid email or password.';
            }
        } catch (Exception $e) {
            $error_message = 'Login failed. Please try again.';
            error_log("Login error: " . $e->getMessage());
        }
    }
}

include 'header.php';
?>

<br><br><br>
<main class="main-content">
    <!--== Start Page Header Area Wrapper ==-->
    <section class="page-header-area pt-10 pb-9" data-bg-color="#FFF3DA">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <div class="page-header-st3-content text-center text-md-start">
                        <ol class="breadcrumb justify-content-center justify-content-md-start">
                            <li class="breadcrumb-item"><a class="text-dark" href="index.php">Home</a></li>
                            <li class="breadcrumb-item active text-dark" aria-current="page">Login</li>
                        </ol>
                        <h2 class="page-header-title">Customer Login</h2>
                        <div class="alert alert-info mt-3">
                            <i class="fa fa-info-circle"></i> <strong>Login is Optional!</strong> You can add products to cart and place orders without creating an account.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!--== End Page Header Area Wrapper ==-->

    <!--== Start Login Area ==-->
    <section class="section-space">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6 col-md-8">
                    <div class="login-form-wrapper">
                        <div class="card shadow-lg border-0">
                            <div class="card-body p-5">
                                <div class="text-center mb-4">
                                    <h3 class="card-title">Welcome Back!</h3>
                                    <p class="text-muted">Sign in to your account to continue shopping</p>
                                </div>

                                <?php if ($error_message): ?>
                                    <div class="alert alert-danger alert-dismissible fade show">
                                        <i class="fa fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                <?php endif; ?>

                                <?php if ($success_message): ?>
                                    <div class="alert alert-success alert-dismissible fade show">
                                        <i class="fa fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                <?php endif; ?>

                                <form method="POST" class="login-form">
                                    <input type="hidden" name="action" value="login">
                                    
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email Address *</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fa fa-envelope"></i></span>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                                   placeholder="Enter your email" required>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="password" class="form-label">Password *</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fa fa-lock"></i></span>
                                            <input type="password" class="form-control" id="password" name="password" 
                                                   placeholder="Enter your password" required>
                                            <button type="button" class="btn btn-outline-secondary" onclick="togglePassword()">
                                                <i class="fa fa-eye" id="password-toggle-icon"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                                        <label class="form-check-label" for="remember_me">
                                            Remember me
                                        </label>
                                    </div>

                                    <button type="submit" class="btn btn-primary w-100 mb-3">
                                        <i class="fa fa-sign-in"></i> Sign In
                                    </button>
                                </form>

                                <div class="text-center">
                                    <a href="index.php" class="btn btn-outline-primary w-100 mb-3">
                                        <i class="fa fa-shopping-bag"></i> Continue Shopping Without Login
                                    </a>
                                    <p class="mb-2">
                                        <a href="forgot-password.php" class="text-decoration-none">Forgot your password?</a>
                                    </p>
                                    <p class="mb-0">
                                        Don't have an account?
                                        <a href="signup.php" class="text-decoration-none fw-bold">Create Account</a>
                                    </p>
                                </div>

                                <hr class="my-4">

                                <div class="text-center">
                                    <p class="text-muted small">
                                        <i class="fa fa-shield"></i> Your information is secure and protected
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!--== End Login Area ==-->
</main>

<?php include 'footer.php'; ?>

<script>
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('password-toggle-icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fa fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fa fa-eye';
    }
}

// Auto-focus on email field
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('email').focus();
});
</script>

<style>
.login-form-wrapper {
    margin: 40px 0;
}

.card {
    border-radius: 15px;
}

.input-group-text {
    background-color: #f8f9fa;
    border-right: none;
}

.form-control {
    border-left: none;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 12px;
    font-weight: 600;
    border-radius: 8px;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

.alert {
    border-radius: 8px;
}

@media (max-width: 768px) {
    .card-body {
        padding: 2rem !important;
    }
}
</style>
