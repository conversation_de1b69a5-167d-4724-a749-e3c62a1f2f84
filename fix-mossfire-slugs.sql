-- This SQL script will fix the duplicate slug issue for MOSSFIRE products

-- First, let's see what we have
SELECT 'Current MOSSFIRE Products:' as info;
SELECT id, title, slug, product_code 
FROM products 
WHERE title LIKE '%MOSSFIRE%' 
ORDER BY id;

-- Update the LOTION product slug
UPDATE products 
SET slug = 'mossfire-mosquito-repellent-lotion' 
WHERE title LIKE '%MOSSFIRE%' AND title LIKE '%LOTION%';

-- Update the SPRAY product slug  
UPDATE products 
SET slug = 'mossfire-mosquito-repellent-spray' 
WHERE title LIKE '%MOSSFIRE%' AND title LIKE '%SPRAY%';

-- Verify the fix
SELECT 'After Fix - Updated MOSSFIRE Products:' as info;
SELECT id, title, slug, product_code 
FROM products 
WHERE title LIKE '%MOSSFIRE%' 
ORDER BY id;

-- Check for any remaining duplicate slugs
SELECT 'Checking for duplicate slugs:' as info;
SELECT slug, COUNT(*) as count, GROUP_CONCAT(id) as product_ids, G<PERSON>UP_CONCAT(title SEPARATOR ' | ') as titles
FROM products 
WHERE slug IS NOT NULL AND slug != '' AND status = 'active'
GROUP BY slug 
HAVING count > 1;
