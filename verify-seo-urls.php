<?php
require_once 'config/dbconfig.php';

echo "<h1>✅ Verify SEO-Friendly URLs Implementation</h1>";
echo "<p>This page verifies that your SEO-friendly URLs are working correctly.</p>";

try {
    // Check database setup
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>📊 Database Verification</h2>";
    
    // Check if slug column exists
    $columns = fetchAll("SHOW COLUMNS FROM products LIKE 'slug'");
    $has_slug = !empty($columns);
    
    echo "<p><strong>Slug Column:</strong> " . ($has_slug ? "✅ Exists" : "❌ Missing") . "</p>";
    
    if ($has_slug) {
        // Check how many products have slugs
        $total_products = fetchSingle("SELECT COUNT(*) as count FROM products WHERE status = 'active'")['count'];
        $products_with_slugs = fetchSingle("SELECT COUNT(*) as count FROM products WHERE status = 'active' AND slug IS NOT NULL AND slug != ''")['count'];
        
        echo "<p><strong>Total Active Products:</strong> $total_products</p>";
        echo "<p><strong>Products with Slugs:</strong> $products_with_slugs</p>";
        
        if ($products_with_slugs == $total_products) {
            echo "<p style='color: green;'>✅ All products have slugs!</p>";
        } else {
            echo "<p style='color: orange;'>⚠ " . ($total_products - $products_with_slugs) . " products missing slugs</p>";
        }
    }
    echo "</div>";

    // Check product-details.php
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>📄 product-details.php Verification</h2>";
    
    $product_details_content = file_get_contents('product-details.php');
    $supports_slugs = strpos($product_details_content, 'p.slug = ?') !== false;
    
    echo "<p><strong>Supports Slug URLs:</strong> " . ($supports_slugs ? "✅ Yes" : "❌ No") . "</p>";
    
    if ($supports_slugs) {
        echo "<p style='color: green;'>✅ product-details.php can handle both slug and product code URLs</p>";
    } else {
        echo "<p style='color: red;'>❌ product-details.php needs to be updated to support slugs</p>";
    }
    echo "</div>";

    // Check product links in files
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🔗 Product Links Verification</h2>";
    
    $files_to_check = ['index.php', 'hair-care.php', 'skin-care.php', 'health-care.php', 'personal-care.php'];
    $files_using_slugs = 0;
    $files_using_codes = 0;
    
    foreach ($files_to_check as $filename) {
        if (file_exists($filename)) {
            $content = file_get_contents($filename);
            
            // Check for product code links (PROD001, PROD002, etc.)
            $has_product_codes = preg_match('/product-details\.php\?product=PROD\d+/', $content);
            
            // Check for slug links (words with hyphens)
            $has_slugs = preg_match('/product-details\.php\?product=[a-z0-9\-]+/', $content) && !$has_product_codes;
            
            if ($has_slugs) {
                echo "<p>✅ <strong>$filename:</strong> Using SEO-friendly URLs</p>";
                $files_using_slugs++;
            } elseif ($has_product_codes) {
                echo "<p>❌ <strong>$filename:</strong> Still using product codes</p>";
                $files_using_codes++;
            } else {
                echo "<p>ℹ <strong>$filename:</strong> No product links found</p>";
            }
        }
    }
    
    echo "<p><strong>Summary:</strong> $files_using_slugs files using SEO URLs, $files_using_codes files using old codes</p>";
    echo "</div>";

    // Live URL testing
    if ($has_slug && $supports_slugs) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h2>🧪 Live URL Testing</h2>";
        
        $test_products = fetchAll("SELECT id, product_code, title, slug FROM products WHERE status = 'active' AND slug IS NOT NULL LIMIT 3");
        
        if (!empty($test_products)) {
            echo "<p>Test these URLs to verify they work:</p>";
            
            foreach ($test_products as $product) {
                $slug_url = "product-details.php?product=" . urlencode($product['slug']);
                $code_url = "product-details.php?product=" . urlencode($product['product_code']);
                
                echo "<div style='background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745;'>";
                echo "<h4>" . htmlspecialchars($product['title']) . "</h4>";
                
                echo "<p><strong>🎯 New SEO URL:</strong></p>";
                echo "<p><a href='$slug_url' target='_blank' style='color: #28a745; font-family: monospace; font-weight: bold; text-decoration: none; background: #f8f9fa; padding: 5px 10px; border-radius: 3px;'>$slug_url</a></p>";
                
                echo "<p><strong>🔄 Old URL (should still work):</strong></p>";
                echo "<p><a href='$code_url' target='_blank' style='color: #6c757d; font-family: monospace; text-decoration: none; background: #f8f9fa; padding: 5px 10px; border-radius: 3px;'>$code_url</a></p>";
                
                echo "</div>";
            }
        }
        echo "</div>";
    }

    // Overall status
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center;'>";
    echo "<h2>📊 Overall Status</h2>";
    
    $all_good = $has_slug && $supports_slugs && ($files_using_slugs > 0);
    
    if ($all_good) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin: 0;'>🎉 SUCCESS!</h3>";
        echo "<p style='color: #155724; font-size: 18px; margin: 10px 0;'>SEO-friendly URLs are fully implemented and working!</p>";
        echo "</div>";
        
        echo "<h3>✅ What's Working:</h3>";
        echo "<ul style='text-align: left; display: inline-block;'>";
        echo "<li>✅ Database has slug column</li>";
        echo "<li>✅ Products have SEO-friendly slugs</li>";
        echo "<li>✅ product-details.php supports both URL formats</li>";
        echo "<li>✅ Product links updated to use slugs</li>";
        echo "<li>✅ Backward compatibility maintained</li>";
        echo "</ul>";
        
    } else {
        echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h3 style='color: #856404; margin: 0;'>⚠ PARTIAL IMPLEMENTATION</h3>";
        echo "<p style='color: #856404; font-size: 18px; margin: 10px 0;'>Some steps still need to be completed.</p>";
        echo "</div>";
        
        echo "<h3>🔧 What Needs Fixing:</h3>";
        echo "<ul style='text-align: left; display: inline-block;'>";
        if (!$has_slug) echo "<li>❌ Add slug column to database</li>";
        if (!$supports_slugs) echo "<li>❌ Update product-details.php to support slugs</li>";
        if ($files_using_codes > 0) echo "<li>❌ Update product links in $files_using_codes files</li>";
        echo "</ul>";
        
        echo "<div style='margin: 20px 0;'>";
        echo "<a href='auto-implement-seo-urls.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 16px;'>🔧 Run Auto-Implementation</a>";
        echo "</div>";
    }
    echo "</div>";

    // Quick navigation
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🔗 Quick Navigation</h2>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 20px 0;'>";
    
    $nav_links = [
        'hair-care.php' => '💇 Hair Care Products',
        'skin-care.php' => '🧴 Skin Care Products',
        'health-care.php' => '💊 Health Care Products',
        'personal-care.php' => '🧼 Personal Care Products',
        'index.php' => '🏠 Home Page'
    ];
    
    foreach ($nav_links as $page => $title) {
        echo "<a href='$page' target='_blank' style='background: #6c757d; color: white; padding: 10px; text-decoration: none; border-radius: 5px; text-align: center; display: block;'>$title</a>";
    }
    echo "</div>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p style='color: #721c24;'>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
a { text-decoration: none; }
a:hover { text-decoration: underline; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
</style>
