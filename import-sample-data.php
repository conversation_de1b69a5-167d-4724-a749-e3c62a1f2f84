<?php
/**
 * Sample Data Import Tool
 * For populating the live database with sample products
 */

require_once 'config/dbconfig.php';

echo "<h1>📦 Sample Data Import Tool</h1>";
echo "<p>Import sample products and categories for drzianaturals.com</p>";
echo "<hr>";

if ($_POST['action'] ?? '' === 'import_data') {
    try {
        $pdo->beginTransaction();
        
        echo "<h2>Importing Sample Data...</h2>";
        
        // 1. Create/Update Categories
        echo "<h3>1. Creating Categories...</h3>";
        $categories = [
            ['Hair Care', 'Products for hair growth, hair fall control, and overall hair health'],
            ['Skin Care', 'Facial cleansers, moisturizers, and skincare products for healthy skin'],
            ['Face Care', 'Specialized facial care products for glowing skin'],
            ['Anti-Acne', 'Specialized products for acne treatment and prevention'],
            ['Health Care', 'Natural health and wellness products for overall well-being'],
            ['Natural Products', 'All natural and organic products without harmful chemicals']
        ];
        
        $category_ids = [];
        foreach ($categories as $cat) {
            $existing = fetchSingle("SELECT id FROM categories WHERE name = ?", [$cat[0]]);
            if ($existing) {
                $category_ids[$cat[0]] = $existing['id'];
                echo "<p>✅ Category '{$cat[0]}' already exists</p>";
            } else {
                $cat_id = insertData("INSERT INTO categories (name, description, status) VALUES (?, ?, 'active')", [$cat[0], $cat[1]]);
                $category_ids[$cat[0]] = $cat_id;
                echo "<p>✅ Created category '{$cat[0]}'</p>";
            }
        }
        
        // 2. Create Sample Products
        echo "<h3>2. Creating Sample Products...</h3>";
        $products = [
            [
                'product_code' => 'HAIR001',
                'title' => 'Hair Growth Serum Premium',
                'collection' => 'Premium Collection',
                'description' => 'Our premium hair growth serum is formulated with natural ingredients to promote healthy hair growth and reduce hair fall. Suitable for all hair types.',
                'price' => 1499.00,
                'image' => 'assets/images/shop/1.png',
                'categories' => ['Hair Care', 'Natural Products']
            ],
            [
                'product_code' => 'SKIN001',
                'title' => 'Natural Face Cleanser',
                'collection' => 'Skincare Essentials',
                'description' => 'Gentle face cleanser made with natural ingredients for daily skincare routine. Removes impurities while maintaining skin moisture.',
                'price' => 899.00,
                'image' => 'assets/images/shop/2.png',
                'categories' => ['Skin Care', 'Face Care', 'Natural Products']
            ],
            [
                'product_code' => 'ACNE001',
                'title' => 'Anti-Acne Treatment Gel',
                'collection' => 'Acne Solutions',
                'description' => 'Effective anti-acne gel that helps reduce acne and prevents future breakouts. Contains natural antibacterial ingredients.',
                'price' => 1299.00,
                'image' => 'assets/images/shop/3.png',
                'categories' => ['Anti-Acne', 'Skin Care', 'Natural Products']
            ],
            [
                'product_code' => 'HEALTH001',
                'title' => 'Natural Wellness Supplement',
                'collection' => 'Health & Wellness',
                'description' => 'Natural health supplement to boost immunity and overall wellness. Made with organic herbs and natural ingredients.',
                'price' => 1999.00,
                'image' => 'assets/images/shop/4.png',
                'categories' => ['Health Care', 'Natural Products']
            ],
            [
                'product_code' => 'HAIR002',
                'title' => 'Hair Oil Treatment',
                'collection' => 'Hair Care Essentials',
                'description' => 'Nourishing hair oil treatment for dry and damaged hair. Provides deep conditioning and strengthens hair roots.',
                'price' => 799.00,
                'image' => 'assets/images/shop/5.png',
                'categories' => ['Hair Care', 'Natural Products']
            ],
            [
                'product_code' => 'SKIN002',
                'title' => 'Moisturizing Face Cream',
                'collection' => 'Skincare Essentials',
                'description' => 'Rich moisturizing cream for daily facial care. Hydrates skin and provides long-lasting moisture protection.',
                'price' => 1199.00,
                'image' => 'assets/images/shop/6.png',
                'categories' => ['Skin Care', 'Face Care', 'Natural Products']
            ]
        ];
        
        foreach ($products as $product) {
            // Check if product already exists
            $existing = fetchSingle("SELECT id FROM products WHERE product_code = ?", [$product['product_code']]);
            
            if ($existing) {
                echo "<p>⚠️ Product '{$product['title']}' already exists</p>";
                $product_id = $existing['id'];
            } else {
                // Insert product
                $product_id = insertData("
                    INSERT INTO products (product_code, title, collection, description, price, image, status) 
                    VALUES (?, ?, ?, ?, ?, ?, 'active')
                ", [
                    $product['product_code'],
                    $product['title'],
                    $product['collection'],
                    $product['description'],
                    $product['price'],
                    $product['image']
                ]);
                
                echo "<p>✅ Created product '{$product['title']}'</p>";
            }
            
            // Link to categories
            foreach ($product['categories'] as $cat_name) {
                if (isset($category_ids[$cat_name])) {
                    $existing_link = fetchSingle("
                        SELECT id FROM product_categories 
                        WHERE product_id = ? AND category_id = ?
                    ", [$product_id, $category_ids[$cat_name]]);
                    
                    if (!$existing_link) {
                        insertData("
                            INSERT INTO product_categories (product_id, category_id) 
                            VALUES (?, ?)
                        ", [$product_id, $category_ids[$cat_name]]);
                    }
                }
            }
        }
        
        // 3. Create Admin User if not exists
        echo "<h3>3. Creating Admin User...</h3>";
        $admin_exists = fetchSingle("SELECT id FROM admin_users WHERE username = 'admin'");
        
        if (!$admin_exists) {
            $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
            insertData("
                INSERT INTO admin_users (username, email, password, full_name, role, status) 
                VALUES ('admin', '<EMAIL>', ?, 'Administrator', 'super_admin', 'active')
            ", [$admin_password]);
            echo "<p>✅ Created admin user (admin/admin123)</p>";
        } else {
            echo "<p>⚠️ Admin user already exists</p>";
        }
        
        $pdo->commit();
        
        echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h2>🎉 Import Completed Successfully!</h2>";
        echo "<p>Sample data has been imported to your live database.</p>";
        echo "<ul>";
        echo "<li>✅ " . count($categories) . " categories created</li>";
        echo "<li>✅ " . count($products) . " products created</li>";
        echo "<li>✅ Admin user ready (admin/admin123)</li>";
        echo "</ul>";
        echo "<p><strong>Next Steps:</strong></p>";
        echo "<ol>";
        echo "<li><a href='admin/login.php'>Login to Admin Panel</a></li>";
        echo "<li><a href='hair-care.php'>View Hair Care Products</a></li>";
        echo "<li><a href='skin-care.php'>View Skin Care Products</a></li>";
        echo "<li><a href='health-care.php'>View Health Care Products</a></li>";
        echo "<li><a href='index.php'>Check Homepage</a></li>";
        echo "</ol>";
        echo "</div>";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo "<p style='color: red;'>❌ Error importing data: " . $e->getMessage() . "</p>";
    }
}

// Show current status
echo "<h2>Current Database Status</h2>";

try {
    $product_count = fetchSingle("SELECT COUNT(*) as count FROM products WHERE status = 'active'")['count'] ?? 0;
    $category_count = fetchSingle("SELECT COUNT(*) as count FROM categories WHERE status = 'active'")['count'] ?? 0;
    $admin_count = fetchSingle("SELECT COUNT(*) as count FROM admin_users WHERE status = 'active'")['count'] ?? 0;
    
    echo "<ul>";
    echo "<li><strong>Products:</strong> $product_count active products</li>";
    echo "<li><strong>Categories:</strong> $category_count active categories</li>";
    echo "<li><strong>Admin Users:</strong> $admin_count active admin users</li>";
    echo "</ul>";
    
    if ($product_count == 0 || $admin_count == 0) {
        echo "<p style='color: orange;'>⚠️ Your database needs sample data to work properly.</p>";
    } else {
        echo "<p style='color: green;'>✅ Database looks good!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking database: " . $e->getMessage() . "</p>";
}

if (!isset($_POST['action'])) {
?>

<h2>Import Sample Data</h2>
<div style="background: #fff3cd; color: #856404; padding: 15px; border-radius: 8px; margin: 20px 0;">
    <h3>⚠️ Important Notes:</h3>
    <ul>
        <li>This will create sample products and categories</li>
        <li>Existing data will not be deleted</li>
        <li>Admin user will be created if it doesn't exist</li>
        <li>Safe to run multiple times</li>
    </ul>
</div>

<form method="POST">
    <input type="hidden" name="action" value="import_data">
    <button type="submit" style="background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: bold;">
        🚀 Import Sample Data Now
    </button>
</form>

<?php } ?>

<hr>
<h2>🔗 Quick Links</h2>
<p><a href="live-site-diagnostic.php">Full Site Diagnostic</a></p>
<p><a href="admin/login.php">Admin Login</a></p>
<p><a href="admin/fix-admin-password.php">Fix Admin Password</a></p>
<p><a href="hair-care.php">Hair Care Products</a></p>
<p><a href="skin-care.php">Skin Care Products</a></p>
<p><a href="health-care.php">Health Care Products</a></p>
<p><a href="index.php">Homepage</a></p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
hr { margin: 30px 0; border: none; border-top: 2px solid #eee; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
