<?php
session_start();
require_once 'config/dbconfig.php';
include 'header.php';
?>

<!-- FontAwesome CDN for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<?php

// Get personal care products
$personal_care_query = "
    SELECT DISTINCT p.*,
           GROUP_CONCAT(c.name SEPARATOR ', ') as categories,
           COALESCE(p.sale_price, p.price) as display_price,
           CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price
                THEN p.price ELSE NULL END as original_price
    FROM products p
    LEFT JOIN product_categories pc ON p.id = pc.product_id
    LEFT JOIN categories c ON pc.category_id = c.id
    WHERE p.status = 'active'
    AND (c.name = 'Personal Care'
         OR p.title LIKE '%personal%'
         OR p.title LIKE '%Personal%'
         OR p.title LIKE '%hygiene%'
         OR p.title LIKE '%Hygiene%'
         OR p.title LIKE '%deodorant%'
         OR p.title LIKE '%Deodorant%'
         OR p.title LIKE '%soap%'
         OR p.title LIKE '%Soap%'
         OR p.title LIKE '%body%'
         OR p.title LIKE '%Body%'
         OR p.title LIKE '%lotion%'
         OR p.title LIKE '%Lotion%'
         OR p.title LIKE '%perfume%'
         OR p.title LIKE '%Perfume%'
         OR p.title LIKE '%fragrance%'
         OR p.title LIKE '%Fragrance%')
    GROUP BY p.id
    ORDER BY p.created_at ASC
";

$personal_products = fetchAll($personal_care_query);

// Quick fix for MOSSFIRE slugs - run this once
if (!isset($_SESSION['mossfire_fixed'])) {
    // Get MOSSFIRE products and fix their slugs
    $mossfire_products = fetchAll("SELECT id, title, slug FROM products WHERE title LIKE '%MOSSFIRE%'");

    foreach ($mossfire_products as $product) {
        $title = $product['title'];

        // Create specific slugs
        if (stripos($title, 'LOTION') !== false) {
            $new_slug = 'mossfire-mosquito-repellent-lotion';
        } elseif (stripos($title, 'SPRAY') !== false) {
            $new_slug = 'mossfire-mosquito-repellent-spray';
        } else {
            $new_slug = 'mossfire-mosquito-repellent-' . $product['id'];
        }

        // Update the slug if it's different
        if ($product['slug'] !== $new_slug) {
            executeQuery("UPDATE products SET slug = ? WHERE id = ?", [$new_slug, $product['id']]);
        }
    }

    // Mark as fixed for this session
    $_SESSION['mossfire_fixed'] = true;
}
?>
<br><br><br><br>
<!-- Personal Care Hero Banner -->
<section class="personal-hero-banner position-relative">
    <div class="container">
        <div class="row align-items-center py-5">
            <div class="col-lg-6">
                <div class="personal-hero-content">
                    <div class="hero-badge mb-3">
                        <span class="badge bg-primary fs-6 px-3 py-2 rounded-pill">
                            <i class="fas fa-spa me-2"></i>Natural Personal Care
                        </span>
                    </div>
                    <h1 class="display-4 fw-bold text-dark mb-4">
                        Fresh & Clean, <br>
                        <span class="text-primary">Naturally Pure</span>
                    </h1>
                    <p class="lead text-muted mb-4">
                        Experience the confidence of natural freshness with our premium collection of personal care products,
                        including gentle soaps, refreshing deodorants, and nourishing body care essentials.
                    </p>
                    <div class="hero-features mb-4">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="feature-item d-flex align-items-center">
                                    <i class="fas fa-leaf text-primary me-3 fs-5"></i>
                                    <span class="text-dark">100% Natural Ingredients</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item d-flex align-items-center">
                                    <i class="fas fa-shield-alt text-warning me-3 fs-5"></i>
                                    <span class="text-dark">Gentle & Safe Formula</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item d-flex align-items-center">
                                    <i class="fas fa-sparkles text-success me-3 fs-5"></i>
                                    <span class="text-dark">Long-lasting Freshness</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item d-flex align-items-center">
                                    <i class="fas fa-heart text-danger me-3 fs-5"></i>
                                    <span class="text-dark">Loved by Customers</span>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="col-lg-6">
                <div class="personal-hero-image text-center">
                    <img src="assets/images/personal_care.png" alt="Personal Care Products"
                         class="img-fluid rounded-3 shadow" style="max-width: 600px; height: auto;">
                </div>
            </div>
        </div>
    </div>
</section>
<br><br>
<!-- Personal Care Products Section -->
<section id="personal-products" class="shop-section section section-padding bg-light">
    <div class="container">
        <!-- Section Header -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <div class="section-header">
                    <span class="badge bg-primary fs-6 px-3 py-2 rounded-pill mb-3">
                        <i class="fas fa-spa me-2"></i>Personal Care Collection
                    </span>
                    <h2 class="display-5 fw-bold text-dark mb-3">
                        <i class="fas fa-magic text-primary me-3"></i>Natural Personal Care Solutions
                    </h2>
                    <p class="lead text-muted mx-auto" style="max-width: 600px;">
                        Explore our carefully curated selection of natural personal care products,
                        including gentle soaps, refreshing deodorants, and nourishing body care essentials.
                    </p>
                </div>
            </div>
        </div>

        <!-- Breadcrumb -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-white rounded-3 p-3 shadow-sm">
                        <li class="breadcrumb-item">
                            <a href="index.php" class="text-decoration-none">
                                <i class="fas fa-home me-1"></i>Home
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-spa me-1"></i>Personal Care
                        </li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row">
            <?php if (empty($personal_products)): ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <h4>No Personal Care Products Available</h4>
                        <p>We're working on adding more products. Please check back soon!</p>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($personal_products as $product):
                    $review_count = rand(5, 25);
                ?>
                    <div class="col-6 col-lg-4 mb-4 mb-sm-9">
                        <!--== Start Product Item ==-->
                        <div class="product-item">
                            <div class="product-thumb">
                                <a class="d-block" href="product-details.php?product=<?php echo htmlspecialchars($product['slug'] ?: $product['product_code']); ?>">
                                    <img src="<?php echo htmlspecialchars($product['image'] ?: 'assets/images/shop/default.png'); ?>"
                                         width="370" height="450" alt="<?php echo htmlspecialchars($product['title']); ?>">
                                </a>
                                <span class="flag-new">new</span>
                                <div class="product-action">
                                    <button type="button" class="product-action-btn action-btn-cart"
                                            onclick="addToCart(<?php echo $product['id']; ?>)">
                                        <span>Add to cart</span>
                                    </button>
                                </div>
                            </div>
                            <div class="product-info">
                                <div class="product-rating">
                                    <div class="rating">
                                        <?php
                                        // Get actual average rating for this product
                                        $product_reviews = fetchAll("SELECT rating FROM product_reviews WHERE product_id = ? AND status = 'approved'", [$product['id']]);
                                        $avg_rating = !empty($product_reviews) ? array_sum(array_column($product_reviews, 'rating')) / count($product_reviews) : 5;
                                        $review_count = count($product_reviews);

                                        // Show all 5 stars, but highlight only the rating amount
                                        for ($i = 1; $i <= 5; $i++):
                                            if ($i <= $avg_rating): ?>
                                                <i class="fa fa-star" style="color: #ffc107;"></i>
                                            <?php else: ?>
                                                <i class="fa fa-star" style="color: #ddd;"></i>
                                            <?php endif;
                                        endfor; ?>
                                    </div>
                                    <div class="reviews text-muted small"><?php echo $review_count; ?> review<?php echo $review_count != 1 ? 's' : ''; ?></div>
                                </div>
                                <h4 class="title">
                                    <a href="product-details.php?product=<?php echo htmlspecialchars($product['slug'] ?: $product['product_code']); ?>">
                                        <?php echo htmlspecialchars($product['title']); ?>
                                    </a>
                                </h4>
                                <div class="prices">
                                    <?php if ($product['original_price']): ?>
                                        <span class="price-old">Rs. <?php echo number_format($product['original_price'], 0); ?></span>
                                        <span class="price">Rs. <?php echo number_format($product['display_price'], 0); ?></span>
                                    <?php else: ?>
                                        <span class="price">Rs. <?php echo number_format($product['display_price'], 0); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <!--== End Product Item ==-->
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Add to Cart JavaScript -->
<script>
function addToCart(productId) {
    // Show loading state
    const cartButtons = document.querySelectorAll(`[onclick="addToCart(${productId})"]`);
    cartButtons.forEach(btn => {
        btn.disabled = true;
        btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Adding...';
    });

    fetch('cart/add-to-cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: 1
        })
    })
    .then(response => response.json())
    .then(data => {
        // Restore button state
        cartButtons.forEach(btn => {
            btn.disabled = false;
            btn.innerHTML = '<span>Add to cart</span>';
        });

        if (data.success) {
            // Show success notification
            showNotification('Product added to cart successfully!', 'success');

            // Update cart count
            updateCartCount();
        } else {
            showNotification(data.message || 'Error adding product to cart', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);

        // Restore button state
        cartButtons.forEach(btn => {
            btn.disabled = false;
            btn.innerHTML = '<span>Add to cart</span>';
        });

        showNotification('Error adding product to cart', 'error');
    });
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} cart-notification`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}

function updateCartCount() {
    fetch('cart/get-cart-count.php')
        .then(response => response.json())
        .then(data => {
            const cartCountElement = document.querySelector('.cart-count');
            if (cartCountElement && data.count !== undefined) {
                cartCountElement.textContent = data.count;
                cartCountElement.style.display = data.count > 0 ? 'inline-block' : 'none';
            }
        })
        .catch(error => console.error('Error updating cart count:', error));
}
</script>

<?php include 'footer.php'; ?>
