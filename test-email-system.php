<?php
require_once 'config/dbconfig.php';

echo "<h2>📧 Email System Test & Configuration</h2>";

// Test PHPMailer installation
echo "<h3>1. 📦 PHPMailer Installation Check</h3>";

$phpmailer_files = [
    'PHPMailer/PHPMailer.php',
    'PHPMailer/SMTP.php', 
    'PHPMailer/Exception.php'
];

$phpmailer_installed = true;
foreach ($phpmailer_files as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $file - Found</p>";
    } else {
        echo "<p style='color: red;'>❌ $file - Missing</p>";
        $phpmailer_installed = false;
    }
}

if (!$phpmailer_installed) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p><strong>❌ PHPMailer not properly installed!</strong></p>";
    echo "<p>Please make sure PHPMailer files are in the PHPMailer/ directory.</p>";
    echo "</div>";
    exit;
}

// Test database connection and orders
echo "<h3>2. 🗄️ Database & Orders Check</h3>";

try {
    $order_count = fetchSingle("SELECT COUNT(*) as count FROM orders")['count'] ?? 0;
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    echo "<p style='color: blue;'>📊 Total orders in database: $order_count</p>";
    
    if ($order_count > 0) {
        $latest_order = fetchSingle("
            SELECT o.*, c.first_name, c.last_name, c.email 
            FROM orders o 
            LEFT JOIN customers c ON o.customer_id = c.id 
            ORDER BY o.created_at DESC 
            LIMIT 1
        ");
        
        if ($latest_order) {
            echo "<p style='color: green;'>✅ Latest order found: #" . htmlspecialchars($latest_order['order_number']) . "</p>";
            echo "<p style='color: blue;'>👤 Customer: " . htmlspecialchars(trim($latest_order['first_name'] . ' ' . $latest_order['last_name'])) . "</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ No orders found. Create a test order to test email functionality.</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

// Test email configuration
echo "<h3>3. ⚙️ Email Configuration Check</h3>";

if (file_exists('order-email.php')) {
    echo "<p style='color: green;'>✅ order-email.php file found</p>";
    
    // Check email settings
    $email_content = file_get_contents('order-email.php');
    
    if (strpos($email_content, '<EMAIL>') !== false) {
        echo "<p style='color: green;'>✅ Gmail SMTP configuration found</p>";
    }
    
    if (strpos($email_content, '<EMAIL>') !== false) {
        echo "<p style='color: green;'>✅ Admin email address configured</p>";
    }
    
    if (strpos($email_content, 'wkck bhxj uuyd tjqf') !== false) {
        echo "<p style='color: green;'>✅ App password configured</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ order-email.php file not found</p>";
}

// Test email sending (if orders exist)
echo "<h3>4. 📤 Email Sending Test</h3>";

if ($order_count > 0 && isset($latest_order)) {
    echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p><strong>🧪 Ready to test email sending</strong></p>";
    echo "<p>Latest order details:</p>";
    echo "<ul>";
    echo "<li><strong>Order Number:</strong> " . htmlspecialchars($latest_order['order_number']) . "</li>";
    echo "<li><strong>Customer:</strong> " . htmlspecialchars(trim($latest_order['first_name'] . ' ' . $latest_order['last_name'])) . "</li>";
    echo "<li><strong>Order Date:</strong> " . date('M d, Y g:i A', strtotime($latest_order['created_at'])) . "</li>";
    echo "<li><strong>Total:</strong> Rs. " . number_format($latest_order['final_amount'], 0) . "</li>";
    echo "</ul>";
    
    if (isset($_GET['send_test']) && $_GET['send_test'] == '1') {
        echo "<h4>📧 Sending Test Email...</h4>";
        
        try {
            require_once 'order-email.php';
            $result = sendOrderNotificationEmail($latest_order['id']);
            
            if ($result) {
                echo "<p style='color: green; font-weight: bold;'>✅ Test email sent successfully!</p>";
                echo "<p>Check your email inbox: <strong><EMAIL></strong></p>";
            } else {
                echo "<p style='color: red; font-weight: bold;'>❌ Test email failed to send.</p>";
                echo "<p>Check your email configuration and internet connection.</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red; font-weight: bold;'>❌ Error sending test email:</p>";
            echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
    } else {
        echo "<p><a href='?send_test=1' class='btn btn-primary' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📤 Send Test Email</a></p>";
    }
    echo "</div>";
    
} else {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p><strong>⚠ Cannot test email sending</strong></p>";
    echo "<p>No orders found in database. Please:</p>";
    echo "<ol>";
    echo "<li>Go to your website and place a test order</li>";
    echo "<li>Or create a sample order in the admin panel</li>";
    echo "<li>Then return here to test email functionality</li>";
    echo "</ol>";
    echo "</div>";
}

// Integration status
echo "<h3>5. 🔗 Integration Status</h3>";

// Check if checkout.php has email integration
if (file_exists('checkout.php')) {
    $checkout_content = file_get_contents('checkout.php');
    
    if (strpos($checkout_content, 'sendOrderNotificationEmail') !== false) {
        echo "<p style='color: green;'>✅ Email notification integrated in checkout process</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Email notification not integrated in checkout process</p>";
    }
} else {
    echo "<p style='color: red;'>❌ checkout.php file not found</p>";
}

// Check admin panel integration
if (file_exists('admin/send-order-email.php')) {
    echo "<p style='color: green;'>✅ Admin email management page created</p>";
} else {
    echo "<p style='color: orange;'>⚠ Admin email management page not found</p>";
}

if (file_exists('admin/orders.php')) {
    $admin_orders_content = file_get_contents('admin/orders.php');
    
    if (strpos($admin_orders_content, 'send-order-email.php') !== false) {
        echo "<p style='color: green;'>✅ Email button added to admin orders page</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Email button not added to admin orders page</p>";
    }
}

echo "<hr>";
echo "<h3>📋 Summary & Next Steps</h3>";

if ($phpmailer_installed && $order_count > 0) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p><strong>🎉 Email system is ready!</strong></p>";
    echo "<p><strong>What happens now:</strong></p>";
    echo "<ol>";
    echo "<li>✅ <strong>Automatic emails:</strong> When customers place orders, you'll receive email notifications</li>";
    echo "<li>✅ <strong>Manual emails:</strong> You can send order emails from admin panel</li>";
    echo "<li>✅ <strong>Email content:</strong> Includes complete order details, customer info, and next steps</li>";
    echo "</ol>";
    echo "<p><strong>Test the system:</strong></p>";
    echo "<ul>";
    echo "<li><a href='admin/orders.php' style='color: #155724; font-weight: bold;'>📋 View Orders in Admin Panel</a></li>";
    echo "<li><a href='checkout.php' style='color: #155724; font-weight: bold;'>🛒 Place a Test Order</a></li>";
    echo "<li><a href='?send_test=1' style='color: #155724; font-weight: bold;'>📤 Send Test Email</a></li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p><strong>⚠ Setup incomplete</strong></p>";
    echo "<p>Please complete the following steps:</p>";
    echo "<ul>";
    if (!$phpmailer_installed) {
        echo "<li>Install PHPMailer properly</li>";
    }
    if ($order_count == 0) {
        echo "<li>Create test orders to test email functionality</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<h3>🔧 Quick Links</h3>";
echo "<p><a href='admin/orders.php'>📋 Admin Orders</a></p>";
echo "<p><a href='admin/send-order-email.php'>📧 Send Order Email</a></p>";
echo "<p><a href='order-email.php'>🧪 Test Email Function</a></p>";
echo "<p><a href='index.php'>🏠 Website Home</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
.btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
.btn:hover { background: #0056b3; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
hr { margin: 30px 0; border: none; border-top: 2px solid #eee; }
</style>
