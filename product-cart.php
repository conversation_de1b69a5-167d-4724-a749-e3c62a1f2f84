<?php
session_start();

// Delete item if requested
if (isset($_GET['delete'])) {
    $delete_id = $_GET['delete'];
    foreach ($_SESSION['cart'] as $index => $item) {
        if ($item['id'] == $delete_id) {
            unset($_SESSION['cart'][$index]);
            $_SESSION['cart'] = array_values($_SESSION['cart']); // Reindex array
            break;
        }
    }
    header("Location: product-cart.php");
    exit;
}

// Initialize cart
$cart = $_SESSION['cart'] ?? [];
$total = 0;
?>

<h2>Your Cart</h2>

<?php if (empty($cart)): ?>
    <p>Your cart is empty.</p>
<?php else: ?>
    <table border="1" cellpadding="10" cellspacing="0">
        <tr>
            <th>Product</th>
            <th>Image</th>
            <th>Price</th>
            <th>Quantity</th>
            <th>Subtotal</th>
            <th>Action</th>
        </tr>

        <?php foreach ($cart as $item): 
            $subtotal = $item['price'] * $item['quantity'];
            $total += $subtotal;
        ?>
            <tr>
                <td><?= htmlspecialchars($item['name']) ?></td>
                <td><img src="<?= htmlspecialchars($item['image']) ?>" width="60"></td>
                <td><?= number_format($item['price'], 2) ?></td>
                <td><?= $item['quantity'] ?></td>
                <td><?= number_format($subtotal, 2) ?></td>
                <td><a href="?delete=<?= 
                $item['id'] ?>" onclick="return confirm('Remove this item?')">Delete</a></td>
            </tr>
        <?php endforeach; ?>

        <tr>
            <td colspan="4" align="right"><strong>Total:</strong></td>
            <td colspan="2"><strong><?= number_format($total, 2) ?></strong></td>
        </tr>
    </table>

    <br>
    <a href="product-checkout.php"><button>Proceed to Checkout</button></a>
<?php endif; ?>
