# Dr.Zia Naturals - E-commerce Website

## Database Setup Instructions

### Prerequisites
- XAMPP or WAMP server running
- MySQL database server
- PHP 7.4 or higher

### Database Configuration

The database configuration is located in `config/dbconfig.php`. The default settings are:

- **Host**: localhost
- **Username**: root
- **Password**: (empty)
- **Database Name**: naturals

### Installation Steps

1. **Start your local server** (XAMPP/WAMP)

2. **Setup Database** - Choose one of the following methods:

   **Method 1: Automatic Setup (Recommended)**
   - Open your browser and go to: `http://localhost/zia/install/setup_database.php`
   - Follow the on-screen instructions
   - The script will automatically create the database and tables

   **Method 2: Manual Setup**
   - Open phpMyAdmin (`http://localhost/phpmyadmin`)
   - Create a new database named `naturals`
   - Import the SQL file: `database/naturals_db.sql`

3. **Verify Installation**
   - Go to `http://localhost/zia/index.php`
   - Click on any product to test the product details page
   - The website should now work without blank screens

### Database Structure

The database includes the following main tables:

- **products** - Main product information
- **product_volumes** - Product size/volume variants
- **product_reviews** - Customer reviews
- **categories** - Product categories
- **customers** - Customer accounts
- **orders** - Order information
- **order_items** - Individual order items

### Features

- **Product Management**: Complete product catalog with variants
- **Customer Reviews**: Rating and review system
- **Order Management**: Full order processing system
- **Category System**: Organized product categorization
- **Responsive Design**: Mobile-friendly interface

### File Structure

```
zia/
├── config/
│   └── dbconfig.php          # Database configuration
├── database/
│   └── naturals_db.sql       # Database structure
├── install/
│   └── setup_database.php    # Database setup script
├── assets/                   # CSS, JS, Images
├── index.php                 # Homepage
├── product-details.php       # Product details page
├── header.php               # Common header
├── footer.php               # Common footer
└── README.md                # This file
```

### Troubleshooting

**Blank Screen Issues:**
- Ensure database connection is working
- Check if `config/dbconfig.php` has correct database credentials
- Verify that the `naturals` database exists
- Run the setup script if tables are missing

**Database Connection Errors:**
- Check if MySQL service is running
- Verify database credentials in `config/dbconfig.php`
- Ensure the database `naturals` exists

**Product Details Not Loading:**
- Check if product data exists in the database
- Verify the product URLs are correct
- Ensure all required tables are created

### Support

For any issues or questions, please check:
1. Database connection settings
2. PHP error logs
3. MySQL error logs
4. Browser console for JavaScript errors

### Development Notes

- All PHP files now include the database configuration
- The system uses PDO for secure database operations
- Prepared statements are used to prevent SQL injection
- Error handling is implemented for database operations
