<?php
// Debug script to check product display issues
require_once 'config/dbconfig.php';

echo "<h2>Product Display Debug</h2>";

try {
    // 1. Check database structure
    echo "<h3>1. Database Structure Check</h3>";
    $columns = fetchAll("SHOW COLUMNS FROM products");
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>" . $col['Field'] . "</td>";
        echo "<td>" . $col['Type'] . "</td>";
        echo "<td>" . $col['Null'] . "</td>";
        echo "<td>" . $col['Key'] . "</td>";
        echo "<td>" . $col['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. Check if new fields exist
    echo "<h3>2. New Fields Check</h3>";
    $new_fields = ['key_benefits', 'ingredients', 'how_to_use', 'sale_price'];
    foreach ($new_fields as $field) {
        $exists = fetchAll("SHOW COLUMNS FROM products LIKE '$field'");
        echo "<p><strong>$field:</strong> " . (empty($exists) ? "❌ Missing" : "✅ Exists") . "</p>";
    }
    
    // 3. Check products data
    echo "<h3>3. Products Data Check</h3>";
    $products = fetchAll("SELECT id, product_code, title, price, sale_price, status, key_benefits, ingredients, how_to_use FROM products LIMIT 5");
    
    if (empty($products)) {
        echo "<p style='color: red;'>❌ No products found in database!</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Code</th><th>Title</th><th>Price</th><th>Sale Price</th><th>Status</th><th>Benefits</th><th>Ingredients</th><th>Usage</th></tr>";
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td>" . $product['id'] . "</td>";
            echo "<td>" . $product['product_code'] . "</td>";
            echo "<td>" . htmlspecialchars($product['title']) . "</td>";
            echo "<td>Rs. " . $product['price'] . "</td>";
            echo "<td>" . ($product['sale_price'] ? "Rs. " . $product['sale_price'] : "None") . "</td>";
            echo "<td>" . $product['status'] . "</td>";
            echo "<td>" . (empty($product['key_benefits']) ? "Empty" : "Has content") . "</td>";
            echo "<td>" . (empty($product['ingredients']) ? "Empty" : "Has content") . "</td>";
            echo "<td>" . (empty($product['how_to_use']) ? "Empty" : "Has content") . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 4. Test specific product queries
    echo "<h3>4. Product Query Tests</h3>";
    
    // Test home page query
    echo "<h4>Home Page Query Test:</h4>";
    $home_products = fetchAll("
        SELECT p.*,
               AVG(pr.rating) as avg_rating,
               COUNT(pr.id) as review_count
        FROM products p
        LEFT JOIN product_reviews pr ON p.id = pr.product_id AND pr.status = 'approved'
        WHERE p.status = 'active'
        GROUP BY p.id
        ORDER BY p.product_code ASC
        LIMIT 6
    ");
    echo "<p>Found " . count($home_products) . " products for home page</p>";
    
    // Test hair care query
    echo "<h4>Hair Care Query Test:</h4>";
    $hair_products = fetchAll("
        SELECT DISTINCT p.*,
               GROUP_CONCAT(c.name SEPARATOR ', ') as categories,
               COALESCE(p.sale_price, p.price) as display_price,
               CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price 
                    THEN p.price ELSE NULL END as original_price
        FROM products p
        LEFT JOIN product_categories pc ON p.id = pc.product_id
        LEFT JOIN categories c ON pc.category_id = c.id
        WHERE p.status = 'active'
        AND (c.name = 'Hair Care'
             OR p.title LIKE '%hair%'
             OR p.title LIKE '%Hair%'
             OR p.title LIKE '%shampoo%'
             OR p.title LIKE '%Shampoo%'
             OR p.title LIKE '%conditioner%'
             OR p.title LIKE '%Conditioner%'
             OR p.title LIKE '%oil%'
             OR p.title LIKE '%Oil%'
             OR p.title LIKE '%serum%'
             OR p.title LIKE '%Serum%')
        GROUP BY p.id
        ORDER BY p.created_at ASC
    ");
    echo "<p>Found " . count($hair_products) . " hair care products</p>";
    
    // 5. Check categories
    echo "<h3>5. Categories Check</h3>";
    $categories = fetchAll("SELECT * FROM categories WHERE status = 'active'");
    echo "<p>Active categories: " . count($categories) . "</p>";
    foreach ($categories as $cat) {
        $product_count = fetchSingle("SELECT COUNT(*) as count FROM product_categories WHERE category_id = ?", [$cat['id']]);
        echo "<p><strong>" . $cat['name'] . ":</strong> " . $product_count['count'] . " products</p>";
    }
    
    // 6. Check product categories relationships
    echo "<h3>6. Product-Category Relationships</h3>";
    $relationships = fetchAll("
        SELECT p.title, c.name as category_name 
        FROM products p 
        JOIN product_categories pc ON p.id = pc.product_id 
        JOIN categories c ON pc.category_id = c.id 
        WHERE p.status = 'active' 
        LIMIT 10
    ");
    
    if (empty($relationships)) {
        echo "<p style='color: red;'>❌ No product-category relationships found!</p>";
        echo "<p><strong>This might be why products aren't showing on category pages.</strong></p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Product</th><th>Category</th></tr>";
        foreach ($relationships as $rel) {
            echo "<tr><td>" . htmlspecialchars($rel['title']) . "</td><td>" . $rel['category_name'] . "</td></tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>7. Recommendations</h3>";
    echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px;'>";
    
    // Check if migration is needed
    $needs_migration = false;
    foreach ($new_fields as $field) {
        $exists = fetchAll("SHOW COLUMNS FROM products LIKE '$field'");
        if (empty($exists)) {
            $needs_migration = true;
            break;
        }
    }
    
    if ($needs_migration) {
        echo "<p style='color: red;'><strong>❌ Database migration needed!</strong></p>";
        echo "<p><a href='run-migration.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Run Migration Now</a></p>";
    }
    
    if (empty($relationships)) {
        echo "<p style='color: orange;'><strong>⚠ Product categories need to be assigned!</strong></p>";
        echo "<p><a href='update-categories.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Update Categories</a></p>";
    }
    
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li>Run migration if needed</li>";
    echo "<li>Update product categories</li>";
    echo "<li>Check individual product pages</li>";
    echo "<li>Test category pages</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background: #f0f0f0; }
h3 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
h4 { color: #666; }
</style>
