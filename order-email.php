<?php
use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

require_once 'PHPMailer/PHPMailer.php';
require_once 'PHPMailer/SMTP.php';
require_once 'PHPMailer/Exception.php';
require_once 'config/dbconfig.php';

/**
 * Send order notification email to admin
 * @param int $order_id - The order ID from database
 * @return bool - True if email sent successfully, false otherwise
 */
function sendOrderNotificationEmail($order_id) {
    try {
        // Get order details from database
        $order = fetchSingle("
            SELECT o.*, c.first_name, c.last_name, c.email, c.phone
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.id
            WHERE o.id = ?
        ", [$order_id]);

        if (!$order) {
            throw new Exception('Order not found');
        }

        // Get order items
        $order_items = fetchAll("
            SELECT oi.*, p.title, p.product_code, pv.size
            FROM order_items oi
            JOIN products p ON oi.product_id = p.id
            LEFT JOIN product_volumes pv ON oi.volume_id = pv.id
            WHERE oi.order_id = ?
        ", [$order_id]);

        $mail = new PHPMailer(true);

        // Server settings
        $mail->isSMTP();
        $mail->Host       = 'smtp.gmail.com';
        $mail->SMTPAuth   = true;
        $mail->Username   = '<EMAIL>';
        $mail->Password   = 'evxf xfyu iyhh snvx'; // App password from Gmail
        $mail->SMTPSecure = 'tls';
        $mail->Port       = 587;

        // Recipients
        $mail->setFrom('<EMAIL>', 'Dr. Zia Naturals');
        $mail->addAddress('<EMAIL>', 'Dr. Zia Naturals Admin'); // Admin email
        $mail->addReplyTo('<EMAIL>', 'Dr. Zia Naturals');

        // Content
        $mail->isHTML(true);
        $mail->Subject = '🛒 New Order Received - Order #' . $order['order_number'];

        // Create email body
        $email_body = generateOrderEmailBody($order, $order_items);
        $mail->Body = $email_body;

        // Plain text version
        $mail->AltBody = generateOrderEmailPlainText($order, $order_items);

        $mail->send();
        return true;

    } catch (Exception $e) {
        error_log("Order email failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate HTML email body for order notification
 */
function generateOrderEmailBody($order, $order_items) {
    $customer_name = trim($order['first_name'] . ' ' . $order['last_name']);
    $order_date = date('F d, Y \a\t g:i A', strtotime($order['created_at']));

    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Order Notification</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; background-color: #f4f4f4; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
            .header { background: #2c5530; color: white; padding: 20px; text-align: center; border-radius: 5px; margin-bottom: 30px; }
            .order-info { background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
            .customer-info { background: #e3f2fd; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
            .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            .items-table th, .items-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
            .items-table th { background-color: #f8f9fa; font-weight: bold; }
            .total-section { background: #d4edda; padding: 20px; border-radius: 5px; text-align: right; }
            .status-badge { padding: 5px 10px; border-radius: 3px; color: white; font-weight: bold; }
            .status-pending { background-color: #ffc107; }
            .status-processing { background-color: #17a2b8; }
            .status-shipped { background-color: #28a745; }
            .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🛒 New Order Received!</h1>
                <p>Dr. Zia Naturals - Natural Beauty Products</p>
            </div>

            <div class="order-info">
                <h2>📋 Order Information</h2>
                <p><strong>Order Number:</strong> ' . htmlspecialchars($order['order_number']) . '</p>
                <p><strong>Order Date:</strong> ' . $order_date . '</p>
                <p><strong>Payment Method:</strong> ' . ucfirst($order['payment_method']) . '</p>
                <p><strong>Status:</strong> <span class="status-badge status-' . $order['status'] . '">' . ucfirst($order['status']) . '</span></p>
            </div>

            <div class="customer-info">
                <h2>👤 Customer Information</h2>
                <p><strong>Name:</strong> ' . htmlspecialchars($customer_name) . '</p>
                <p><strong>Email:</strong> ' . htmlspecialchars($order['email']) . '</p>
                <p><strong>Phone:</strong> ' . htmlspecialchars($order['phone']) . '</p>
                <p><strong>Shipping Address:</strong><br>' . nl2br(htmlspecialchars($order['shipping_address'])) . '</p>
                ' . ($order['notes'] ? '<p><strong>Order Notes:</strong><br>' . nl2br(htmlspecialchars($order['notes'])) . '</p>' : '') . '
            </div>

            <h2>🛍️ Order Items</h2>
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Product</th>
                        <th>Size/Volume</th>
                        <th>Quantity</th>
                        <th>Unit Price</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>';

    foreach ($order_items as $item) {
        $html .= '
                    <tr>
                        <td>
                            <strong>' . htmlspecialchars($item['title']) . '</strong><br>
                            <small>Code: ' . htmlspecialchars($item['product_code']) . '</small>
                        </td>
                        <td>' . ($item['size'] ? htmlspecialchars($item['size']) : 'Standard') . '</td>
                        <td>' . $item['quantity'] . '</td>
                        <td>Rs. ' . number_format($item['unit_price'], 0) . '</td>
                        <td>Rs. ' . number_format($item['total_price'], 0) . '</td>
                    </tr>';
    }

    $html .= '
                </tbody>
            </table>

            <div class="total-section">
                <p><strong>Subtotal:</strong> Rs. ' . number_format($order['total_amount'], 0) . '</p>
                <p><strong>Shipping Fee:</strong> Rs. ' . number_format($order['shipping_fee'], 0) . '</p>
                ' . ($order['discount_amount'] > 0 ? '<p><strong>Discount:</strong> -Rs. ' . number_format($order['discount_amount'], 0) . '</p>' : '') . '
                <h3 style="margin-top: 15px; padding-top: 15px; border-top: 2px solid #2c5530;">
                    <strong>Final Total: Rs. ' . number_format($order['final_amount'], 0) . '</strong>
                </h3>
            </div>

            <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin-top: 20px;">
                <h3>📞 Next Steps:</h3>
                <ul>
                    <li>Contact customer at: <strong>' . htmlspecialchars($order['phone']) . '</strong></li>
                    <li>Confirm order details and availability</li>
                    <li>Process payment if COD</li>
                    <li>Prepare items for shipping</li>
                    <li>Update order status in admin panel</li>
                </ul>
            </div>

            <div class="footer">
                <p><strong>Dr. Zia Naturals</strong><br>
                2nd floor, Plot No. 18, Sector 14, Korangi Industrial Area, Karachi<br>
                Email: <EMAIL> | Phone: +92 321 2634304</p>
                <p><a href="http://localhost/zia/admin/orders.php" style="color: #2c5530;">View Order in Admin Panel</a></p>
            </div>
        </div>
    </body>
    </html>';

    return $html;
}

/**
 * Generate plain text email body for order notification
 */
function generateOrderEmailPlainText($order, $order_items) {
    $customer_name = trim($order['first_name'] . ' ' . $order['last_name']);
    $order_date = date('F d, Y \a\t g:i A', strtotime($order['created_at']));

    $text = "NEW ORDER RECEIVED - Dr. Zia Naturals\n";
    $text .= "=====================================\n\n";

    $text .= "ORDER INFORMATION:\n";
    $text .= "Order Number: " . $order['order_number'] . "\n";
    $text .= "Order Date: " . $order_date . "\n";
    $text .= "Payment Method: " . ucfirst($order['payment_method']) . "\n";
    $text .= "Status: " . ucfirst($order['status']) . "\n\n";

    $text .= "CUSTOMER INFORMATION:\n";
    $text .= "Name: " . $customer_name . "\n";
    $text .= "Email: " . $order['email'] . "\n";
    $text .= "Phone: " . $order['phone'] . "\n";
    $text .= "Shipping Address: " . $order['shipping_address'] . "\n";
    if ($order['notes']) {
        $text .= "Order Notes: " . $order['notes'] . "\n";
    }
    $text .= "\n";

    $text .= "ORDER ITEMS:\n";
    $text .= "============\n";
    foreach ($order_items as $item) {
        $text .= "- " . $item['title'] . " (Code: " . $item['product_code'] . ")\n";
        $text .= "  Size: " . ($item['size'] ?: 'Standard') . "\n";
        $text .= "  Quantity: " . $item['quantity'] . "\n";
        $text .= "  Unit Price: Rs. " . number_format($item['unit_price'], 0) . "\n";
        $text .= "  Total: Rs. " . number_format($item['total_price'], 0) . "\n\n";
    }

    $text .= "ORDER TOTAL:\n";
    $text .= "============\n";
    $text .= "Subtotal: Rs. " . number_format($order['total_amount'], 0) . "\n";
    $text .= "Shipping Fee: Rs. " . number_format($order['shipping_fee'], 0) . "\n";
    if ($order['discount_amount'] > 0) {
        $text .= "Discount: -Rs. " . number_format($order['discount_amount'], 0) . "\n";
    }
    $text .= "FINAL TOTAL: Rs. " . number_format($order['final_amount'], 0) . "\n\n";

    $text .= "NEXT STEPS:\n";
    $text .= "===========\n";
    $text .= "1. Contact customer at: " . $order['phone'] . "\n";
    $text .= "2. Confirm order details and availability\n";
    $text .= "3. Process payment if COD\n";
    $text .= "4. Prepare items for shipping\n";
    $text .= "5. Update order status in admin panel\n\n";

    $text .= "Dr. Zia Naturals\n";
    $text .= "2nd floor, Plot No. 18, Sector 14, Korangi Industrial Area, Karachi\n";
    $text .= "Email: <EMAIL> | Phone: +92 321 2634304\n";

    return $text;
}

/**
 * Test function to send a sample order email
 */
function testOrderEmail() {
    // Get the latest order for testing
    $latest_order = fetchSingle("SELECT id FROM orders ORDER BY created_at DESC LIMIT 1");

    if ($latest_order) {
        $result = sendOrderNotificationEmail($latest_order['id']);
        if ($result) {
            echo "✅ Test email sent successfully!";
        } else {
            echo "❌ Test email failed to send.";
        }
    } else {
        echo "❌ No orders found in database for testing.";
    }
}

// If this file is accessed directly, run test
if (basename($_SERVER['PHP_SELF']) == 'order-email.php' && !isset($order_id)) {
    echo "<h2>📧 Order Email System Test</h2>";
    testOrderEmail();
    echo "<br><br><a href='admin/orders.php'>← Back to Orders</a>";
}
?>
