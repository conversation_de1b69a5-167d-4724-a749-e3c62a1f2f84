<?php
// Include database configuration
require_once 'config/dbconfig.php';

// Function to convert text to bullet points
function formatAsBulletPoints($text) {
    if (empty($text)) {
        return '';
    }

    // First try to split by line breaks
    $lines = preg_split('/\r\n|\r|\n/', trim($text));

    // If we only have one line, try to split by bullet characters
    if (count($lines) == 1) {
        // Split by bullet characters (•, *, -, etc.) followed by space
        $lines = preg_split('/[\s]*[•\*\-\→\►\▪\▫\‣\⁃]\s*/', $text);
    }

    // Filter out empty lines and trim whitespace
    $lines = array_filter(array_map('trim', $lines), function($line) {
        return !empty($line);
    });

    if (empty($lines)) {
        return '';
    }

    // If there's only one meaningful line, return as paragraph
    if (count($lines) == 1) {
        return '<p>' . htmlspecialchars($lines[0]) . '</p>';
    }

    // Convert to bullet points
    $html = '<ul class="product-bullet-list">';
    foreach ($lines as $line) {
        // Remove any remaining bullet characters at the start
        $line = preg_replace('/^[\s\-\*\•\→\►\▪\▫\‣\⁃]+\s*/', '', $line);
        $line = trim($line);
        if (!empty($line)) {
            $html .= '<li>' . htmlspecialchars($line) . '</li>';
        }
    }
    $html .= '</ul>';

    return $html;
}

// Get the product code from URL parameter
$productCode = isset($_GET['product']) ? trim($_GET['product']) : '';

// Check if product code is provided
if (!$productCode) {
    header('Location: index.php');
    exit();
}

// Fetch product from database - support both slugs and product codes
$product = null;

// Try to fetch by slug first (SEO-friendly URLs)
if (!empty($productCode)) {
    $product = fetchSingle("
        SELECT p.*,
               COALESCE(p.sale_price, p.price) as display_price,
               CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price
                    THEN p.price ELSE NULL END as original_price
        FROM products p
        WHERE p.slug = ? AND p.status = 'active'
    ", [$productCode]);
}

// If not found by slug, try by product_code (backward compatibility)
if (!$product && !empty($productCode)) {
    $product = fetchSingle("
        SELECT p.*,
               COALESCE(p.sale_price, p.price) as display_price,
               CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price
                    THEN p.price ELSE NULL END as original_price
        FROM products p
        WHERE p.product_code = ? AND p.status = 'active'
    ", [$productCode]);
}

// Check if product exists
if (!$product) {
    header('Location: index.php');
    exit();
}

// Fetch product volumes
$volumes = fetchAll("
    SELECT * FROM product_volumes
    WHERE product_id = ? AND status = 'active'
    ORDER BY price ASC
", [$product['id']]);

// Fetch product reviews
$reviews = fetchAll("
    SELECT * FROM product_reviews
    WHERE product_id = ? AND status = 'approved'
    ORDER BY created_at DESC
", [$product['id']]);

// Prepare product specs
$specs = [
    'Weight' => $product['weight'],
    'Dimensions' => $product['dimensions'],
    'Materials' => $product['materials'],
    'Other Info' => $product['other_info']
];

// Remove empty specs
$specs = array_filter($specs, function($value) {
    return !empty(trim($value));
});
?>
<!DOCTYPE html>
<html class="no-js" lang="zxx">

<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title><?php echo htmlspecialchars($product['title']); ?> | Dr.Zia Naturals</title>
    <meta name="robots" content="index, follow" />
    <meta name="description" content="<?php echo htmlspecialchars($product['description']); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="keywords" content="cosmetic, beauty products, natural skincare" />
    <meta name="author" content="Dr.Zia Naturals" />
<?php include('header.php'); ?>
    <style>
        .cart-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
        }

        #add-to-cart-btn {
            background-color: #ff6b6b !important;
            border-color: #ff6b6b !important;
            color: white !important;
            padding: 12px 24px;
            font-weight: 600;
            border-radius: 25px;
            transition: all 0.3s ease;
            min-width: 150px;
        }

        #add-to-cart-btn:hover {
            background-color: #ff5252 !important;
            border-color: #ff5252 !important;
            transform: translateY(-2px);
        }

        #add-to-cart-btn:disabled {
            background-color: #ccc !important;
            border-color: #ccc !important;
            cursor: not-allowed;
        }

        /* Product Details Tab Styling */
        .product-benefits-content, .product-usage-content {
            padding: 20px 0;
        }

        .benefits-section, .ingredients-section, .usage-instructions {
            margin-bottom: 25px;
        }

        .benefits-section h5, .ingredients-section h5, .usage-instructions h5 {
            color: #333;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #f0f0f0;
        }

        .benefits-text, .ingredients-text, .usage-text {
            line-height: 1.8;
            color: #666;
            font-size: 14px;
        }

        .benefits-text ul, .usage-text ul {
            padding-left: 0;
            list-style: none;
        }

        .benefits-text li, .usage-text li {
            padding: 5px 0;
            position: relative;
            padding-left: 20px;
        }

        .benefits-text li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .usage-text li:before {
            content: counter(step-counter);
            counter-increment: step-counter;
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: bold;
            position: absolute;
            left: 0;
            top: 5px;
        }

        .usage-text {
            counter-reset: step-counter;
        }

        /* Product Description Bullet List Styling */
        .product-bullet-list {
            list-style: none;
            padding-left: 0;
            margin: 15px 0;
        }

        .product-bullet-list li {
            position: relative;
            padding: 8px 0 8px 25px;
            line-height: 1.6;
            color: #555;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .product-bullet-list li:before {
            content: "•";
            position: absolute;
            left: 0;
            top: 8px;
            color: #FF6565;
            font-weight: bold;
            font-size: 16px;
        }

        /* Sale Price Styling */
        .price-container {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .original-price {
            font-size: 16px;
            color: #999;
            text-decoration: line-through;
            font-weight: 400;
        }

        .sale-price {
            color: #e74c3c !important;
            font-weight: 700;
            margin: 0;
        }

        .discount-badge {
            background: #e74c3c;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            letter-spacing: 0.5px;
        }

        .price {
            margin: 0;
            color: #333;
        }
    </style>
</head>
<br>
<br>
<br>
<body>
    <main class="main-content">
        <!--== Start Page Header Area Wrapper ==-->
        <div class="page-header-area" data-bg-img="assets/images/photos/bg3.jpg">
            <div class="container pt--0 pb--0">
                <div class="row">
                    <div class="col-12">
                        <div class="page-header-content">
                            <h2 class="title" data-aos="fade-down" data-aos-duration="1000"><?php echo htmlspecialchars($product['title']); ?></h2>
                            <nav class="breadcrumb-area" data-aos="fade-down" data-aos-duration="1200">
                                <ul class="breadcrumb">
                                    <li><a href="index.php">Home</a></li>
                                    <li class="breadcrumb-sep">//</li>
                                    <li>Product Details</li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--== End Page Header Area Wrapper ==-->

        <!--== Start Product Single Area Wrapper ==-->
        <section class="product-area product-single-area">
            <div class="container">
                <div class="row">
                    <div class="col-lg-6">
                        <!--== Start Product Thumbnail Area ==-->
                        <div class="product-single-thumb" data-aos="fade-right" data-aos-duration="1000">
                            <img src="<?php echo htmlspecialchars($product['image']); ?>" width="544" height="560" alt="<?php echo htmlspecialchars($product['title']); ?>">
                        </div>
                        <!--== End Product Thumbnail Area ==-->
                    </div>
                    <div class="col-lg-6">
                        <!--== Start Product Info Area ==-->
                        <div class="product-details-content" data-aos="fade-left" data-aos-duration="1000">
                            <h5 class="product-details-collection"><?php echo htmlspecialchars($product['collection'] ?: 'Premium Collection'); ?></h5>
                            <h3 class="product-details-title"><?php echo htmlspecialchars($product['title']); ?></h3>
                            <div class="product-details-review mb-5">
                                <div class="product-review-icon">
                                    <?php
                                    $avg_rating = 0;
                                    if (!empty($reviews)) {
                                        $avg_rating = array_sum(array_column($reviews, 'rating')) / count($reviews);
                                    }
                                    for ($i = 1; $i <= 5; $i++):
                                        if ($i <= $avg_rating): ?>
                                            <i class="fa fa-star" style="color: #ffc107;"></i>
                                        <?php elseif ($i - 0.5 <= $avg_rating): ?>
                                            <i class="fa fa-star-half-o" style="color: #ffc107;"></i>
                                        <?php else: ?>
                                            <i class="fa fa-star-o" style="color: #ddd;"></i>
                                        <?php endif;
                                    endfor; ?>
                                </div>
                                <button type="button" class="product-review-show"><?php echo count($reviews); ?> reviews</button>
                            </div>
                            <div class="mb-6 product-description">
                                <?php echo formatAsBulletPoints($product['description']); ?>
                            </div>

                            <!--== Product Volume Options ==-->
                            <?php if (!empty($volumes)): ?>
                            <div class="product-details-volume mb-4">
                                <h6>Volume Options:</h6>
                                <?php foreach($volumes as $index => $volume): ?>
                                <div class="volume-option mb-2">
                                    <label class="form-check-label">
                                        <input type="radio" name="volume" value="<?php echo $volume['id']; ?>"
                                               data-price="<?php echo $volume['price']; ?>"
                                               class="form-check-input volume-radio"
                                               <?php echo $index === 0 ? 'checked' : ''; ?>>
                                        <?php echo htmlspecialchars($volume['size']); ?> - Rs. <?php echo number_format($volume['price'], 2); ?>
                                        <?php if($volume['offer']): ?>
                                            <span class="offer-text text-success">(<?php echo htmlspecialchars($volume['offer']); ?>)</span>
                                        <?php endif; ?>
                                        <?php if($volume['stock_quantity'] <= 5 && $volume['stock_quantity'] > 0): ?>
                                            <span class="stock-warning text-warning">(Only <?php echo $volume['stock_quantity']; ?> left)</span>
                                        <?php elseif($volume['stock_quantity'] <= 0): ?>
                                            <span class="stock-out text-danger">(Out of stock)</span>
                                        <?php endif; ?>
                                    </label>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>

                            <!--== Shipping Info ==-->
                            <div class="shipping-info mb-4">
                                <p class="text-muted"><i class="fa fa-truck"></i> <?php echo htmlspecialchars($product['shipping_info'] ?: 'Shipping from Pakistan, Shipping Fees Rs. 200'); ?></p>
                            </div>

                            <div class="product-details-pro-qty">
                                <div class="pro-qty">
                                    <input type="number" id="quantity" title="Quantity" value="1" min="1" max="10">
                                </div>
                            </div>
                            <div class="product-details-action">
                                <div class="price-container">
                                    <?php if ($product['original_price']): ?>
                                        <span class="original-price">Rs. <?php echo number_format($product['original_price'], 0); ?></span>
                                        <h4 class="sale-price" id="display-price">Rs. <?php echo number_format($product['display_price'], 0); ?></h4>
                                    <?php else: ?>
                                        <h4 class="price" id="display-price">Rs. <?php echo number_format($product['display_price'], 0); ?></h4>
                                    <?php endif; ?>
                                </div>
                                <div class="product-details-cart-wishlist">
                                    <button type="button" class="btn btn-primary" id="add-to-cart-btn"
                                            data-product-id="<?php echo $product['id']; ?>"
                                            data-product-code="<?php echo htmlspecialchars($product['product_code']); ?>">
                                        <i class="fa fa-shopping-cart"></i> Add to cart
                                    </button>
                                    <button type="button" class="btn btn-black-border" data-bs-toggle="modal" data-bs-target="#action-WishlistModal"><i class="fa fa-heart-o"></i></button>
                                </div>
                            </div>
                        </div>
                        <!--== End Product Info Area ==-->
                    </div>
                </div>
            </div>
        </section>
        <!--== End Product Single Area Wrapper ==-->

        <!--== Start Product Description Area Wrapper ==-->
        <section class="section-space">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="product-description-review">
                            <ul class="nav nav-tabs product-description-tab-menu" id="myTab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="product-benefits-tab" data-bs-toggle="tab" data-bs-target="#product-benefits" type="button" role="tab" aria-controls="product-benefits" aria-selected="true">Key Benefits & Ingredients</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="product-usage-tab" data-bs-toggle="tab" data-bs-target="#product-usage" type="button" role="tab" aria-controls="product-usage" aria-selected="false">How to Use</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="product-review-tab" data-bs-toggle="tab" data-bs-target="#product-review" type="button" role="tab" aria-controls="product-review" aria-selected="false">Reviews (<?php echo count($reviews); ?>)</button>
                                </li>
                            </ul>
                            <div class="tab-content product-description-tab-content" id="myTabContent">
                                <div class="tab-pane fade show active" id="product-benefits" role="tabpanel" aria-labelledby="product-benefits-tab">
                                    <div class="product-benefits-content">
                                        <?php if (!empty($product['key_benefits'])): ?>
                                            <div class="benefits-section mb-4">
                                                <h5><i class="fas fa-star text-warning me-2"></i>Key Benefits</h5>
                                                <div class="benefits-text">
                                                    <?php echo nl2br(htmlspecialchars($product['key_benefits'])); ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <?php if (!empty($product['ingredients'])): ?>
                                            <div class="ingredients-section">
                                                <h5><i class="fas fa-leaf text-success me-2"></i>Ingredients</h5>
                                                <div class="ingredients-text">
                                                    <?php echo nl2br(htmlspecialchars($product['ingredients'])); ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <?php if (empty($product['key_benefits']) && empty($product['ingredients'])): ?>
                                            <p class="text-muted">Key benefits and ingredients information will be available soon.</p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="product-usage" role="tabpanel" aria-labelledby="product-usage-tab">
                                    <div class="product-usage-content">
                                        <?php if (!empty($product['how_to_use'])): ?>
                                            <div class="usage-instructions">
                                                <h5><i class="fas fa-info-circle text-primary me-2"></i>Usage Instructions</h5>
                                                <div class="usage-text">
                                                    <?php echo nl2br(htmlspecialchars($product['how_to_use'])); ?>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <p class="text-muted">Usage instructions will be available soon.</p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="product-review" role="tabpanel" aria-labelledby="product-review-tab">
                                    <div class="product-review-content">
                                        <?php if (!empty($reviews)): ?>
                                            <?php foreach($reviews as $review): ?>
                                            <div class="product-review-item">
                                                <div class="review-content">
                                                    <h6 class="reviewer-name mb-1"><?php echo htmlspecialchars($review['customer_name']); ?></h6>
                                                    <div class="rating mb-2">
                                                        <?php
                                                        $rating = floatval($review['rating']);
                                                        for($i = 1; $i <= 5; $i++):
                                                            if($i <= $rating): ?>
                                                                <i class="fa fa-star" style="color: #ffc107;"></i>
                                                            <?php elseif($i - 0.5 <= $rating): ?>
                                                                <i class="fa fa-star-half-o" style="color: #ffc107;"></i>
                                                            <?php else: ?>
                                                                <i class="fa fa-star-o" style="color: #ddd;"></i>
                                                            <?php endif;
                                                        endfor; ?>
                                                        <small class="text-muted ms-2"><?php echo date('M d, Y', strtotime($review['created_at'])); ?></small>
                                                    </div>
                                                </div>
                                                <p class="review-comment"><?php echo htmlspecialchars($review['comment']); ?></p>
                                            </div>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <p>No reviews yet. Be the first to review this product!</p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!--== End Product Description Area Wrapper ==-->

    </main>

    <?php include('footer.php'); ?>

    <!--== Scroll Top Button ==-->
    <div id="scroll-to-top" class="scroll-to-top"><span class="fa fa-angle-up"></span></div>

    <!--== Start Product Quick Wishlist Modal ==-->
    <aside class="product-action-modal modal fade" id="action-WishlistModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="product-action-view-content">
                        <button type="button" class="btn-close" data-bs-dismiss="modal">
                            <i class="fa fa-times"></i>
                        </button>
                        <div class="modal-action-messages">
                            <i class="fa fa-check-square-o"></i> Added to wishlist successfully!
                        </div>
                        <div class="modal-action-product">
                            <div class="thumb">
                                <img src="<?php echo htmlspecialchars($product['image']); ?>" alt="<?php echo htmlspecialchars($product['title']); ?>" width="466" height="320">
                            </div>
                            <h4 class="product-name"><a href="product-details.php?product=<?php echo $productCode; ?>"><?php echo htmlspecialchars($product['title']); ?></a></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </aside>
    <!--== End Product Quick Wishlist Modal ==-->

    <!--== Start Product Quick Add Cart Modal ==-->
    <aside class="product-action-modal modal fade" id="action-CartAddModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="product-action-view-content">
                        <button type="button" class="btn-close" data-bs-dismiss="modal">
                            <i class="fa fa-times"></i>
                        </button>
                        <div class="modal-action-messages">
                            <i class="fa fa-check-square-o"></i> Added to cart successfully!
                        </div>
                        <div class="modal-action-product">
                            <div class="thumb">
                                <img src="<?php echo htmlspecialchars($product['image']); ?>" alt="<?php echo htmlspecialchars($product['title']); ?>" width="466" height="320">
                            </div>
                            <h4 class="product-name"><a href="product-details.php?product=<?php echo $productCode; ?>"><?php echo htmlspecialchars($product['title']); ?></a></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </aside>
    <!--== End Product Quick Add Cart Modal ==-->

    <?php include 'includes/offcanvas-menu.php'; ?>
    <?php include 'includes/search-modal.php'; ?>

    <?php include 'includes/scripts.php'; ?>

    <script>
        // Test function to ensure JavaScript is working
        console.log('JavaScript is loaded and working!');

        // Test function
        function testFunction() {
            console.log('Test function called!');
            alert('Test function is working!');
        }

        // Update price when volume changes
        document.addEventListener('DOMContentLoaded', function() {
            const volumeRadios = document.querySelectorAll('.volume-radio');
            const displayPrice = document.getElementById('display-price');

            volumeRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.checked) {
                        const price = parseFloat(this.dataset.price);
                        displayPrice.textContent = 'Rs. ' + price.toLocaleString('en-PK', {minimumFractionDigits: 2});
                    }
                });
            });
        });

        // Add to cart function
        function addToCart(productId) {
            try {
                console.log('=== ADD TO CART FUNCTION CALLED ===');
                console.log('addToCart called with productId:', productId);

                // Get quantity (default to 1 if element doesn't exist)
                const quantityElement = document.getElementById('quantity');
                const quantity = quantityElement ? parseInt(quantityElement.value) || 1 : 1;
                console.log('Quantity:', quantity);

                // Get selected volume (if any)
                const selectedVolume = document.querySelector('input[name="volume"]:checked');
                const volumeId = selectedVolume ? selectedVolume.value : null;
                console.log('Selected volume ID:', volumeId);

                // Show loading state - find buttons more reliably
                const cartButtons = document.querySelectorAll('button[onclick*="addToCart"]');
                cartButtons.forEach(btn => {
                    btn.disabled = true;
                    btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Adding...';
                });

                // Prepare request data
                const requestData = {
                    product_id: productId,
                    volume_id: volumeId,
                    quantity: quantity
                };
                console.log('Request data:', requestData);

                // Send AJAX request
                fetch('cart/add-to-cart.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error('Network response was not ok: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);
                    if (data.success) {
                        // Show success notification
                        showCartNotification('Product added to cart successfully!', 'success');

                        // Update cart count
                        updateCartCount(data.cart_count);

                        // Optional: Show modal
                        // const modal = new bootstrap.Modal(document.getElementById('action-CartAddModal'));
                        // modal.show();
                    } else {
                        console.error('Server error:', data.message);
                        showCartNotification('Error adding to cart: ' + (data.message || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showCartNotification('Error adding to cart. Please try again.', 'error');
                })
                .finally(() => {
                    // Reset button state
                    cartButtons.forEach(btn => {
                        btn.disabled = false;
                        btn.innerHTML = '<i class="fa fa-shopping-cart"></i> Add to cart';
                    });
                });
            } catch (error) {
                console.error('JavaScript Error:', error);
                showCartNotification('Error adding to cart. Please try again.', 'error');
            }
        }

        // Function to show cart notifications
        function showCartNotification(message, type = 'success') {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.cart-notification');
            existingNotifications.forEach(notification => notification.remove());

            // Create notification element
            const notification = document.createElement('div');
            notification.className = `cart-notification alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
            notification.innerHTML = `
                <i class="fa fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            // Add to page
            document.body.appendChild(notification);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        // Function to update cart count
        function updateCartCount(count) {
            const cartCountElement = document.querySelector('.cart-count');

            if (count > 0) {
                if (cartCountElement) {
                    cartCountElement.textContent = count;
                    cartCountElement.style.display = 'inline-block';
                } else {
                    // Create cart count badge if it doesn't exist
                    const cartButton = document.querySelector('a[href="cart.php"]');
                    if (cartButton) {
                        const badge = document.createElement('span');
                        badge.className = 'cart-count badge bg-danger position-absolute top-0 start-100 translate-middle rounded-pill';
                        badge.textContent = count;
                        cartButton.appendChild(badge);
                    }
                }
            } else {
                if (cartCountElement) {
                    cartCountElement.style.display = 'none';
                }
            }
        }

        // Load cart count on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listener to the add to cart button
            const addToCartBtn = document.getElementById('add-to-cart-btn');
            if (addToCartBtn) {
                addToCartBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productId = this.getAttribute('data-product-id');
                    addToCart(parseInt(productId));
                });
            }

            fetch('cart/get-cart-count.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateCartCount(data.cart_count);
                    }
                })
                .catch(error => console.log('Error loading cart count:', error));
        });
    </script>

</body>
</html>
