<?php
require_once 'config/dbconfig.php';

echo "<h1>🔍 Debug Personal Care Products</h1>";

// Get all personal care products
$personal_care_query = "
    SELECT DISTINCT p.*,
           GROUP_CONCAT(c.name SEPARATOR ', ') as categories,
           COALESCE(p.sale_price, p.price) as display_price,
           CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price
                THEN p.price ELSE NULL END as original_price
    FROM products p
    LEFT JOIN product_categories pc ON p.id = pc.product_id
    LEFT JOIN categories c ON pc.category_id = c.id
    WHERE p.status = 'active'
    AND (c.name = 'Personal Care'
         OR p.title LIKE '%personal%'
         OR p.title LIKE '%Personal%'
         OR p.title LIKE '%hygiene%'
         OR p.title LIKE '%Hygiene%'
         OR p.title LIKE '%deodorant%'
         OR p.title LIKE '%Deodorant%'
         OR p.title LIKE '%soap%'
         OR p.title LIKE '%Soap%'
         OR p.title LIKE '%body%'
         OR p.title LIKE '%Body%'
         OR p.title LIKE '%lotion%'
         OR p.title LIKE '%Lotion%'
         OR p.title LIKE '%spray%'
         OR p.title LIKE '%Spray%'
         OR p.title LIKE '%repellent%'
         OR p.title LIKE '%Repellent%'
         OR p.title LIKE '%mosquito%'
         OR p.title LIKE '%Mosquito%'
         OR p.title LIKE '%mossfire%'
         OR p.title LIKE '%Mossfire%'
         OR p.title LIKE '%MOSSFIRE%')
    GROUP BY p.id
    ORDER BY p.created_at DESC
";

$personal_products = fetchAll($personal_care_query);

echo "<h2>Found " . count($personal_products) . " Personal Care Products:</h2>";

if (!empty($personal_products)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>ID</th><th>Product Code</th><th>Title</th><th>Slug</th><th>Categories</th><th>Test Link</th>";
    echo "</tr>";
    
    foreach ($personal_products as $product) {
        echo "<tr>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . $product['product_code'] . "</td>";
        echo "<td>" . htmlspecialchars($product['title']) . "</td>";
        echo "<td><strong>" . ($product['slug'] ?: 'NO SLUG') . "</strong></td>";
        echo "<td>" . ($product['categories'] ?: 'No categories') . "</td>";
        
        $link_param = $product['slug'] ?: $product['product_code'];
        $test_url = "product-details.php?product=" . urlencode($link_param);
        echo "<td><a href='{$test_url}' target='_blank' style='color: #007bff;'>Test Link</a></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check for duplicate slugs
    echo "<h2>🔍 Checking for Duplicate Slugs:</h2>";
    $duplicate_slugs = fetchAll("
        SELECT slug, COUNT(*) as count, GROUP_CONCAT(id) as product_ids, GROUP_CONCAT(title SEPARATOR ' | ') as titles
        FROM products 
        WHERE slug IS NOT NULL AND slug != '' AND status = 'active'
        GROUP BY slug 
        HAVING count > 1
    ");
    
    if (!empty($duplicate_slugs)) {
        echo "<div style='background: #ffebee; color: #c62828; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>⚠️ Found Duplicate Slugs:</h3>";
        foreach ($duplicate_slugs as $dup) {
            echo "<p><strong>Slug:</strong> {$dup['slug']} <strong>Count:</strong> {$dup['count']}<br>";
            echo "<strong>Product IDs:</strong> {$dup['product_ids']}<br>";
            echo "<strong>Titles:</strong> {$dup['titles']}</p>";
        }
        echo "</div>";
    } else {
        echo "<div style='background: #e8f5e8; color: #2e7d32; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ No duplicate slugs found!</p>";
        echo "</div>";
    }
    
    // Check products without slugs
    echo "<h2>🔍 Products Without Slugs:</h2>";
    $no_slug_products = fetchAll("SELECT * FROM products WHERE (slug IS NULL OR slug = '') AND status = 'active'");
    
    if (!empty($no_slug_products)) {
        echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>⚠️ Products Missing Slugs:</h3>";
        foreach ($no_slug_products as $product) {
            echo "<p>ID: {$product['id']} - {$product['title']} (Code: {$product['product_code']})</p>";
        }
        echo "</div>";
    } else {
        echo "<div style='background: #e8f5e8; color: #2e7d32; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ All products have slugs!</p>";
        echo "</div>";
    }
    
} else {
    echo "<p style='color: red;'>No personal care products found!</p>";
}

echo "<h2>🧪 Quick Tests:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Test these links to see which product they show:</strong></p>";
echo "<p>1. <a href='product-details.php?product=mossfire-mosquito-repellent-lotion' target='_blank'>Lotion Link</a></p>";
echo "<p>2. <a href='product-details.php?product=mossfire-mosquito-repellent-spray' target='_blank'>Spray Link</a></p>";
echo "<p>3. <a href='personal-care.php' target='_blank'>Personal Care Page</a></p>";
echo "</div>";
?>
