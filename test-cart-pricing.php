<?php
require_once 'config/dbconfig.php';

echo "<h2>🛒 Cart Pricing Fix Test</h2>";

// Test the pricing logic
try {
    // Get products with sale prices
    $products = fetchAll("
        SELECT id, title, price, sale_price,
               COALESCE(sale_price, price) as display_price,
               CASE WHEN sale_price IS NOT NULL AND sale_price < price
                    THEN price ELSE NULL END as original_price
        FROM products 
        WHERE status = 'active' 
        ORDER BY id DESC 
        LIMIT 5
    ");
    
    echo "<h3>📊 Product Pricing Analysis</h3>";
    
    if ($products) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>Product</th>";
        echo "<th style='padding: 10px;'>Original Price</th>";
        echo "<th style='padding: 10px;'>Sale Price</th>";
        echo "<th style='padding: 10px;'>Display Price</th>";
        echo "<th style='padding: 10px;'>Status</th>";
        echo "</tr>";
        
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($product['title']) . "</td>";
            echo "<td style='padding: 10px;'>Rs. " . number_format($product['price'], 2) . "</td>";
            echo "<td style='padding: 10px;'>" . ($product['sale_price'] ? 'Rs. ' . number_format($product['sale_price'], 2) : 'No sale') . "</td>";
            echo "<td style='padding: 10px;'>Rs. " . number_format($product['display_price'], 2) . "</td>";
            
            if ($product['sale_price'] && $product['sale_price'] < $product['price']) {
                $savings = $product['price'] - $product['sale_price'];
                $percentage = round(($savings / $product['price']) * 100);
                echo "<td style='padding: 10px; color: green;'>✅ ON SALE ({$percentage}% off)</td>";
            } else {
                echo "<td style='padding: 10px;'>Regular price</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test cart query
    echo "<h3>🧪 Cart Query Test</h3>";
    
    // Simulate a cart query
    $session_id = 'test_session_' . time();
    
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Cart Query (Updated):</h4>";
    echo "<pre style='background: #fff; padding: 10px; border-radius: 3px; overflow-x: auto;'>";
    echo "SELECT ci.*, p.title, p.image, p.product_code, p.price, p.sale_price,
       pv.size, pv.price as volume_price, pv.offer,
       COALESCE(pv.price, COALESCE(p.sale_price, p.price)) as item_price,
       CASE WHEN pv.id IS NULL AND p.sale_price IS NOT NULL AND p.sale_price < p.price
            THEN p.price ELSE NULL END as original_price
FROM cart_items ci
JOIN products p ON ci.product_id = p.id
LEFT JOIN product_volumes pv ON ci.volume_id = pv.id
WHERE ci.session_id = ?
ORDER BY ci.added_at DESC";
    echo "</pre>";
    echo "</div>";
    
    // Check if there are any cart items to test with
    $existing_cart = fetchAll("SELECT COUNT(*) as count FROM cart_items");
    $cart_count = $existing_cart[0]['count'] ?? 0;
    
    echo "<h3>🛒 Current Cart Status</h3>";
    echo "<p><strong>Total cart items in database:</strong> $cart_count</p>";
    
    if ($cart_count > 0) {
        // Get sample cart items
        $sample_cart = fetchAll("
            SELECT ci.*, p.title, p.price, p.sale_price,
                   COALESCE(COALESCE(p.sale_price, p.price)) as item_price,
                   CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price
                        THEN p.price ELSE NULL END as original_price
            FROM cart_items ci
            JOIN products p ON ci.product_id = p.id
            LIMIT 3
        ");
        
        echo "<h4>Sample Cart Items:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Product</th>";
        echo "<th style='padding: 8px;'>Quantity</th>";
        echo "<th style='padding: 8px;'>Unit Price</th>";
        echo "<th style='padding: 8px;'>Total</th>";
        echo "<th style='padding: 8px;'>Pricing</th>";
        echo "</tr>";
        
        foreach ($sample_cart as $item) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($item['title']) . "</td>";
            echo "<td style='padding: 8px;'>" . $item['quantity'] . "</td>";
            echo "<td style='padding: 8px;'>Rs. " . number_format($item['item_price'], 2) . "</td>";
            echo "<td style='padding: 8px;'>Rs. " . number_format($item['item_price'] * $item['quantity'], 2) . "</td>";
            
            if ($item['original_price']) {
                echo "<td style='padding: 8px; color: green;'>✅ Sale price (was Rs. " . number_format($item['original_price'], 2) . ")</td>";
            } else {
                echo "<td style='padding: 8px;'>Regular price</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Cart Pricing Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h2, h3, h4 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
        table { border-collapse: collapse; width: 100%; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .success { background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background-color: #d1ecf1; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; }
    </style>
</head>
<body>

<div class="success">
    <h4>✅ Cart Pricing Fixed!</h4>
    <p><strong>What was fixed:</strong></p>
    <ul>
        <li>Cart now uses <code>COALESCE(p.sale_price, p.price)</code> for correct pricing</li>
        <li>Checkout uses the same pricing logic</li>
        <li>Both original and sale prices are displayed properly</li>
        <li>Order items will be saved with correct prices</li>
    </ul>
</div>

<div class="info">
    <h4>🧪 How to Test:</h4>
    <ol>
        <li><strong>Update a product price:</strong> Go to Admin Panel → Products → Edit a product</li>
        <li><strong>Set sale price:</strong> Enter a lower price in the "Sale Price" field (e.g., change 1499 to 1099)</li>
        <li><strong>Add to cart:</strong> Add the product to cart from the product page</li>
        <li><strong>Check cart:</strong> Visit cart.php to see the updated pricing</li>
        <li><strong>Test checkout:</strong> Proceed to checkout to verify pricing is correct</li>
    </ol>
</div>

<div class="warning">
    <h4>⚠️ Important Notes:</h4>
    <ul>
        <li><strong>Clear existing cart items:</strong> Old cart items might still have cached prices</li>
        <li><strong>Add products fresh:</strong> Add products to cart after updating prices</li>
        <li><strong>Check admin panel:</strong> Make sure sale prices are saved correctly</li>
        <li><strong>Test with different products:</strong> Verify both sale and regular priced items</li>
    </ul>
</div>

<div class="info">
    <h4>🔗 Quick Links:</h4>
    <p>
        <a href="admin/products.php" target="_blank" style="color: #007bff; text-decoration: none;">🛠️ Admin Panel - Products</a> |
        <a href="cart.php" target="_blank" style="color: #007bff; text-decoration: none;">🛒 View Cart</a> |
        <a href="checkout.php" target="_blank" style="color: #007bff; text-decoration: none;">💳 Checkout</a> |
        <a href="hair-care.php" target="_blank" style="color: #007bff; text-decoration: none;">🧴 Hair Care Products</a>
    </p>
</div>

<div class="success">
    <h4>🎉 Pricing Issue Resolved!</h4>
    <p>Your cart and checkout will now show the correct discounted prices (1099 instead of 1499) when you have sale prices set!</p>
</div>

</body>
</html>
