<?php
echo "<h2>🔍 Search Debug Test</h2>";

// Test database connection
echo "<h3>1. Testing Database Connection</h3>";

try {
    require_once 'config/dbconfig.php';
    echo "<p style='color: green;'>✅ Database config loaded successfully</p>";
    
    // Test connection
    $pdo = getConnection();
    if ($pdo) {
        echo "<p style='color: green;'>✅ Database connection successful</p>";
    } else {
        echo "<p style='color: red;'>❌ Database connection failed</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

// Test search functionality
echo "<h3>2. Testing Search Functions</h3>";

try {
    // Test if functions exist
    if (function_exists('fetchAll')) {
        echo "<p style='color: green;'>✅ fetchAll function exists</p>";
    } else {
        echo "<p style='color: red;'>❌ fetchAll function missing</p>";
    }
    
    if (function_exists('executeQuery')) {
        echo "<p style='color: green;'>✅ executeQuery function exists</p>";
    } else {
        echo "<p style='color: red;'>❌ executeQuery function missing</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error testing functions: " . $e->getMessage() . "</p>";
}

// Test products table
echo "<h3>3. Testing Products Table</h3>";

try {
    $products = fetchAll("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
    if ($products && isset($products[0]['count'])) {
        $count = $products[0]['count'];
        echo "<p style='color: green;'>✅ Found $count active products in database</p>";
    } else {
        echo "<p style='color: orange;'>⚠ No active products found or table doesn't exist</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error querying products: " . $e->getMessage() . "</p>";
}

// Test search query
echo "<h3>4. Testing Search Query</h3>";

$search_query = 'hair';
echo "<p>Testing search for: '<strong>$search_query</strong>'</p>";

try {
    $search_sql = "
        SELECT DISTINCT p.*,
               GROUP_CONCAT(c.name SEPARATOR ', ') as categories,
               COALESCE(p.sale_price, p.price) as display_price,
               CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price
                    THEN p.price ELSE NULL END as original_price
        FROM products p
        LEFT JOIN product_categories pc ON p.id = pc.product_id
        LEFT JOIN categories c ON pc.category_id = c.id
        WHERE p.status = 'active'
        AND (p.title LIKE ? 
             OR p.description LIKE ? 
             OR p.key_benefits LIKE ?
             OR c.name LIKE ?)
        GROUP BY p.id
        ORDER BY p.title ASC
        LIMIT 5
    ";
    
    $search_term = '%' . $search_query . '%';
    $search_results = fetchAll($search_sql, [$search_term, $search_term, $search_term, $search_term]);
    
    if ($search_results && count($search_results) > 0) {
        echo "<p style='color: green;'>✅ Search query successful! Found " . count($search_results) . " results</p>";
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>Search Results:</h4>";
        foreach ($search_results as $product) {
            echo "<p><strong>" . htmlspecialchars($product['title']) . "</strong>";
            if ($product['categories']) {
                echo " - Categories: " . htmlspecialchars($product['categories']);
            }
            echo "</p>";
        }
        echo "</div>";
    } else {
        echo "<p style='color: orange;'>⚠ Search query returned no results</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error in search query: " . $e->getMessage() . "</p>";
}

// Test simple search
echo "<h3>5. Testing Simple Product Query</h3>";

try {
    $simple_products = fetchAll("SELECT id, title FROM products WHERE status = 'active' LIMIT 5");
    
    if ($simple_products && count($simple_products) > 0) {
        echo "<p style='color: green;'>✅ Simple product query successful! Found " . count($simple_products) . " products</p>";
        
        echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>Sample Products:</h4>";
        foreach ($simple_products as $product) {
            echo "<p>ID: " . $product['id'] . " - " . htmlspecialchars($product['title']) . "</p>";
        }
        echo "</div>";
    } else {
        echo "<p style='color: orange;'>⚠ No products found in simple query</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error in simple query: " . $e->getMessage() . "</p>";
}

// Test header include
echo "<h3>6. Testing Header Include</h3>";

if (file_exists('header.php')) {
    echo "<p style='color: green;'>✅ header.php file exists</p>";
} else {
    echo "<p style='color: red;'>❌ header.php file missing</p>";
}

// Test search modal include
echo "<h3>7. Testing Search Modal Include</h3>";

if (file_exists('includes/search-modal.php')) {
    echo "<p style='color: green;'>✅ includes/search-modal.php file exists</p>";
} else {
    echo "<p style='color: red;'>❌ includes/search-modal.php file missing</p>";
}

echo "<hr>";
echo "<h3>📊 Debug Summary</h3>";

echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>✅ If all tests pass, your search should work!</h4>";
echo "<p><strong>Next steps:</strong></p>";
echo "<ol>";
echo "<li>Try the search page directly: <a href='search.php?q=hair' target='_blank'>search.php?q=hair</a></li>";
echo "<li>Test the search button on your website pages</li>";
echo "<li>Check browser console for JavaScript errors if search modal doesn't open</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🔧 If tests fail:</h4>";
echo "<ul>";
echo "<li><strong>Database connection issues:</strong> Check your MySQL server is running</li>";
echo "<li><strong>No products found:</strong> Import sample data or add products via admin panel</li>";
echo "<li><strong>Function errors:</strong> Make sure config/dbconfig.php is complete</li>";
echo "<li><strong>File not found errors:</strong> Check file paths and permissions</li>";
echo "</ul>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
hr { margin: 30px 0; border: none; border-top: 2px solid #eee; }
</style>
