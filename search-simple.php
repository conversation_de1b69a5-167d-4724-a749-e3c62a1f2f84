<?php
// Simple search page for testing
require_once 'config/dbconfig.php';

// Get search query
$search_query = isset($_GET['q']) ? trim($_GET['q']) : '';
$search_results = [];
$total_results = 0;
$error_message = '';

if (!empty($search_query)) {
    try {
        // Simple search query
        $search_sql = "SELECT * FROM products WHERE status = 'active' AND title LIKE ? ORDER BY title ASC LIMIT 10";
        $search_term = '%' . $search_query . '%';
        $search_results = fetchAll($search_sql, [$search_term]);
        $total_results = count($search_results);
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Results - Dr. Zia Naturals</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">Search Results</h1>
                
                <!-- Search Form -->
                <form action="search-simple.php" method="GET" class="mb-4">
                    <div class="input-group">
                        <input type="search" 
                               name="q" 
                               class="form-control" 
                               placeholder="Search for products..." 
                               value="<?php echo htmlspecialchars($search_query); ?>">
                        <button class="btn btn-primary" type="submit">
                            <i class="fa fa-search"></i> Search
                        </button>
                    </div>
                </form>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <strong>Error:</strong> <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($search_query)): ?>
                    <h3>Search Results for "<?php echo htmlspecialchars($search_query); ?>"</h3>
                    <p class="text-muted">Found <?php echo $total_results; ?> result<?php echo $total_results !== 1 ? 's' : ''; ?></p>
                    
                    <?php if ($total_results > 0): ?>
                        <div class="row">
                            <?php foreach ($search_results as $product): ?>
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card h-100">
                                        <?php if ($product['image']): ?>
                                            <img src="<?php echo htmlspecialchars($product['image']); ?>" 
                                                 class="card-img-top" 
                                                 alt="<?php echo htmlspecialchars($product['title']); ?>"
                                                 style="height: 200px; object-fit: cover;">
                                        <?php endif; ?>
                                        <div class="card-body">
                                            <h5 class="card-title"><?php echo htmlspecialchars($product['title']); ?></h5>
                                            <?php if ($product['description']): ?>
                                                <p class="card-text"><?php echo htmlspecialchars(substr($product['description'], 0, 100)) . '...'; ?></p>
                                            <?php endif; ?>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="price">
                                                    <?php if ($product['sale_price'] && $product['sale_price'] < $product['price']): ?>
                                                        <span class="text-muted text-decoration-line-through">Rs. <?php echo number_format($product['price'], 2); ?></span>
                                                        <span class="text-danger fw-bold">Rs. <?php echo number_format($product['sale_price'], 2); ?></span>
                                                    <?php else: ?>
                                                        <span class="fw-bold">Rs. <?php echo number_format($product['price'], 2); ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                <a href="product-details.php?product=<?php echo htmlspecialchars($product['product_code']); ?>" 
                                                   class="btn btn-primary btn-sm">View Details</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <h4>No products found</h4>
                            <p>Sorry, we couldn't find any products matching "<?php echo htmlspecialchars($search_query); ?>".</p>
                            <p>Try searching for:</p>
                            <ul>
                                <li><a href="search-simple.php?q=hair">Hair products</a></li>
                                <li><a href="search-simple.php?q=skin">Skin care</a></li>
                                <li><a href="search-simple.php?q=natural">Natural products</a></li>
                            </ul>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="alert alert-info">
                        <h4>Search Our Products</h4>
                        <p>Enter a search term above to find products.</p>
                        <p>Popular searches:</p>
                        <ul>
                            <li><a href="search-simple.php?q=hair">Hair products</a></li>
                            <li><a href="search-simple.php?q=skin">Skin care</a></li>
                            <li><a href="search-simple.php?q=serum">Serums</a></li>
                            <li><a href="search-simple.php?q=natural">Natural products</a></li>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <div class="mt-4">
                    <a href="index.php" class="btn btn-secondary">← Back to Home</a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
