<?php
/**
 * Database Configuration File
 * Dr.<PERSON>ia Naturals - MySQL Database Connection
 */

// Database configuration constants
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');  // ✅ Valid
define('DB_PASSWORD', '');
 define('DB_NAME', 'naturals');
define('DB_CHARSET', 'utf8mb4');

// Create database connection class
class Database {
    private $host = DB_HOST;
    private $username = DB_USERNAME;
    private $password = DB_PASSWORD;
    private $database = DB_NAME;
    private $charset = DB_CHARSET;
    private $connection;
    
    /**
     * Create database connection
     */
    public function connect() {
        $this->connection = null;
        
        try {
            // Create PDO connection
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->database . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch(PDOException $e) {
            echo "Connection Error: " . $e->getMessage();
            die();
        }
        
        return $this->connection;
    }
    
    /**
     * Get the current connection
     */
    public function getConnection() {
        if ($this->connection === null) {
            return $this->connect();
        }
        return $this->connection;
    }
    
    /**
     * Close database connection
     */
    public function disconnect() {
        $this->connection = null;
    }
}

// Create global database instance
$database = new Database();
$pdo = $database->connect();

// Function to get database connection (for backward compatibility)
function getDBConnection() {
    global $pdo;
    return $pdo;
}

// Function to execute prepared statements safely
function executeQuery($sql, $params = []) {
    global $pdo;
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch(PDOException $e) {
        error_log("Database Query Error: " . $e->getMessage() . " | SQL: " . $sql);
        // In development, show the error
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            echo "<div style='background: #ffebee; color: #c62828; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<strong>Database Error:</strong> " . $e->getMessage() . "<br>";
            echo "<strong>SQL:</strong> " . $sql;
            echo "</div>";
        }
        return false;
    }
}

// Function to fetch single row
function fetchSingle($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    if ($stmt) {
        return $stmt->fetch();
    }
    return false;
}

// Function to fetch multiple rows
function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    if ($stmt) {
        $result = $stmt->fetchAll();
        return $result ? $result : [];
    }
    return [];
}

// Function to insert data and return last insert ID
function insertData($sql, $params = []) {
    global $pdo;
    $stmt = executeQuery($sql, $params);
    if ($stmt) {
        return $pdo->lastInsertId();
    }
    return false;
}

// Function to update/delete data and return affected rows
function updateData($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    if ($stmt) {
        return $stmt->rowCount();
    }
    return false;
}

// Test database connection
function testConnection() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT 1");
        return true;
    } catch(PDOException $e) {
        return false;
    }
}

// Function to check and add missing columns
function ensureRequiredColumns() {
    global $pdo;

    $required_columns = [
        'sale_price' => 'DECIMAL(10,2) NULL',
        'key_benefits' => 'TEXT NULL',
        'ingredients' => 'TEXT NULL',
        'how_to_use' => 'TEXT NULL'
    ];

    foreach ($required_columns as $column => $definition) {
        try {
            // Check if column exists
            $exists = $pdo->query("SHOW COLUMNS FROM products LIKE '$column'")->fetch();

            if (!$exists) {
                // Add the column
                $pdo->exec("ALTER TABLE products ADD COLUMN $column $definition");
                error_log("Added missing column: $column");
            }
        } catch (PDOException $e) {
            error_log("Error adding column $column: " . $e->getMessage());
        }
    }
}

// Auto-run column check when this file is included
ensureRequiredColumns();

// Error handling function
function handleDBError($message = "Database error occurred") {
    error_log($message);
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        echo $message;
    } else {
        echo "An error occurred. Please try again later.";
    }
}

?>
