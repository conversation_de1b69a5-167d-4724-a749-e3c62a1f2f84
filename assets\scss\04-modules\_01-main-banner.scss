/*----------------------------------------*/
/*  Home Slider CSS
/*----------------------------------------*/

.hero-two-slider-area {
  max-width: 1733px;
  margin: 0 auto;
  padding: 0 15px;
}

.hero-slide-item {
  background-color: #FAFAFA;
  height: 1000px !important;
  z-index: 1;
  display: flex;
  align-items: center;
  position: relative;
  @media #{$desktop-device, $tablet-device} {
    height: 660px !important;
  }
  @media #{$tablet-device} {
    height: 512px !important;
  }
  @media #{$large-mobile} {
    height: auto !important;
  }
  &:before {
    background-color: #D9F1E1;
    position: absolute;
    width: 50%;
    content: "";
    top: 0;
    right: 0;
    height: 100%;
    z-index: -1;
  }
}

.hero-two-slide-item {
  align-items: center;
  background-color: #FCEBED;
  border-radius: 30px;
  height: 700px !important;
  display: flex;
  position: relative;
  z-index: 1;
  @media #{$desktop-device} {
    height: 550px !important;
  }
  @media #{$tablet-device} {
    height: 500px !important;
  }
  @media #{$large-mobile} {
    border-radius: 20px;
    height: auto !important;
  }
}

.hero-slide-content {
  position: relative;
  padding-top: 156px;
  z-index: 1;
  @media #{$desktop-device, $tablet-device} {
    padding-top: 76px;
    margin-top: 50px;
  }
  @media #{$tablet-device} {
    padding-top: 60px;
    margin-top: 44px;
  }
  @media #{$large-mobile} {
    padding-top: 60px;
    margin-top: 134px;
    margin-bottom: 40px;
  }
  @media #{$extra-small-mobile} {
    text-align: center;
    margin-top: 82px;
  }

  .hero-slide-text-img {
    position: absolute;
    top: 7px;
    left: 2px;
    z-index: -1;
    pointer-events: none;
    @media #{$desktop-device, $tablet-device} {
      width: 230px;
      top: 0;
    }
    @media #{$tablet-device} {
      width: 200px;
      top: 0;
    }
    @media #{$large-mobile} {
      width: 180px;
      top: 0;
    }
    @media #{$extra-small-mobile} {
      margin: 0 auto;
      position: relative;
      top: 0;
    }
  }

  .hero-slide-title {
    font-size: 67px;
    font-weight: $font-weight-bold;
    margin-bottom: 14px;
    text-transform: uppercase;
    @media #{$desktop-device} {
      font-size: 45px;
      margin-bottom: 5px;
    }
    @media #{$tablet-device} {
      font-size: 38px;
      margin-bottom: 5px;
    }
    @media #{$large-mobile} {
      font-size: 32px;
      margin-bottom: 5px;
    }
  }

  .hero-slide-desc {
    font-size: 21px;
    line-height: 1.34;
    max-width: 410px;
    margin-bottom: 30px;
    @media #{$desktop-device} {
      font-size: 16px;
      margin-bottom: 20px;
    }
    @media #{$tablet-device} {
      font-size: 15px;
      margin-bottom: 18px;
    }
    @media #{$large-mobile} {
      font-size: 15px;
      margin-bottom: 18px;
    }
    @media #{$extra-small-mobile} {
      max-width: 100%;
    }
  }
}

.hero-two-slide-content {
  position: relative;
  padding-top: 44px;
  z-index: 1;
  @media #{$large-mobile} {
    padding-top: 98px;
  }
  @media #{$extra-small-mobile} {
    padding-top: 38px;
    text-align: center;
  }

  .hero-two-slide-text-img {
    position: absolute;
    top: -105px;
    left: 2px;
    z-index: -1;
    pointer-events: none;
    @media #{$desktop-device} {
      top: -38px;
      width: 280px;
    }
    @media #{$tablet-device} {
      width: 250px;
      top: -32px;
      left: 0px;
    }
    @media #{$large-mobile} {
      width: 200px;
      top: 38px;
    }
    @media #{$extra-small-mobile} {
      position: relative;
      margin: 0 auto;
    }
  }

  .hero-two-slide-title {
    font-size: 67px;
    font-weight: $font-weight-bold;
    margin-bottom: 14px;
    @media #{$desktop-device} {
      font-size: 56px;
    }
    @media #{$tablet-device} {
      font-size: 44px;
    }
    @media #{$large-mobile} {
      font-size: 40px;
    }
    @media #{$extra-small-mobile} {
      font-size: 30px;
      margin-bottom: 10px;
    }
  }

  .hero-two-slide-desc {
    font-size: 21px;
    line-height: 1.34;
    max-width: 410px;
    margin-bottom: 30px;
    @media #{$desktop-device} {
      font-size: 20px;
      margin-bottom: 22px;
    }
    @media #{$tablet-device} {
      font-size: 17px;
      margin-bottom: 22px;
    }
    @media #{$large-mobile} {
      font-size: 16px;
    }
    @media #{$extra-small-mobile} {
      font-size: 14px;
      margin-bottom: 22px;
      max-width: 100%;
    }
  }
}

.hero-two-slide-meta {
  align-items: center;
  display: flex;
  @media #{$extra-small-mobile} {
    display: block;
  }

  .ht-popup-video {
    margin-left: 24px;
    @media #{$tablet-device, $desktop-device, $large-mobile} {
      margin-left: 12px;
    }
    @media #{$extra-small-mobile} {
      margin-left: 0;
      margin-top: 18px;
      justify-content: center;
    }
  }
}

.hero-slide-thumb {
  position: absolute;
  right: 0;
  top: -162px;
  @media #{$desktop-device, $tablet-device} {
    position: relative;
    top: 20px;
  }
  @media #{$large-mobile} {
    position: relative;
    top: 0;
    margin-bottom: 50px;
  }
  img {
    max-width: none;
    @media #{$laptop-device} {
      width: 770px;
    }
    @media #{$desktop-device, $tablet-device, $large-mobile} {
      max-width: 100%;
    }
  }
}

.hero-two-slide-thumb {
  float: left;
  position: relative;
  bottom: 23px;
  right: 18px;
  @media #{$laptop-device} {
    bottom: -10px;
  }
  @media #{$desktop-device} {
    float: none;
    right: 0;
    bottom: -15px;
    margin-right: -150px;
  }
  @media #{$tablet-device} {
    float: none;
    bottom: -20px;
    right: 0;
    margin-right: -140px;
  }
  @media #{$large-mobile} {
    float: none;
    right: 0;
    bottom: 0;
  }
  @media #{$extra-small-mobile} {
    margin-right: -54px;
  }
  
  img {
    max-width: none;
    @media #{$desktop-device, $tablet-device, $large-mobile} {
      max-width: 100%;
    }
  }
}

.hero-slide-text-shape {
  left: calc(50% + 520px);
  position: absolute;
  pointer-events: none;
  top: 50%;
  @include transform(translate(0%, -50%) rotate(13deg) scale(.8));
  @include transition(all 3s ease-out);
  opacity: 0;
  z-index: -1;
  @media #{$desktop-device, $tablet-device} {
    display: none;
  }
}

.hero-slide-social-media {
  position: absolute;
  right: 82px;
  bottom: 48px;
  z-index: 1;
  @media #{$laptop-device, $desktop-device, $tablet-device} {
    display: flex;
    bottom: 24px;
  }
  @media #{$desktop-device, $tablet-device} {
    right: -5px;
  }
  @media #{$large-mobile} {
    right: 0;
    bottom: 3px;
  }

  a {
    color: #353434;
    font-size: 22px;
    width: 36px;
    height: 36px;
    display: block;
    text-align: center;
    line-height: 40px;
    margin-bottom: 18px;
    @media #{$laptop-device, $desktop-device, $tablet-device} {
      margin-bottom: 0;
      margin-right: 18px;
    }
    @media #{$tablet-device} {
      font-size: 18px;
      margin-right: 8px;
    }
    @media #{$large-mobile} {
      font-size: 15px;
      margin-bottom: 8px;
    }

    &:hover {
      color: $primary;
    }
  }
}

.hero-slide-social-shape {
  background-color: #fff;
  width: 1px;
  top: calc(0px + 171px);
  right: 100px;
  position: absolute;
  @media #{$laptop-device, $desktop-device, $tablet-device} {
    bottom: 40px;
    top: auto;
    right: 250px;
    height: 1px;
    width: 150px;
    animation-name: inherit !important;
  }
  @media #{$desktop-device} {
    right: 160px;
  }
  @media #{$tablet-device} {
    right: 138px;
  }
  @media #{$large-mobile} {
    right: 17px;
    top: auto;
    height: 160px;
    bottom: 152px;
    animation-name: inherit !important;
  }
}

.hero-slider-pagination {
  align-items: center;
  color: $black;
  display: flex;
  font-weight: $font-weight-light;
  font-size: 21px;
  line-height: 1;
  position: absolute;
  z-index: 1;
  left: -89px;
  @include rotate(-90);
  height: 20px;
  width: 363px;
  top: calc(50% - 12px);
  bottom: auto;
  @media #{$laptop-device, $desktop-device, $tablet-device} {
    transform: none;
    left: 30px;
    top: auto;
    bottom: 30px;
    width: 200px
  }
  @media #{$tablet-device} {
    font-size: 16px;
  }
  @media #{$large-mobile} {
    font-size: 16px;
    width: 200px;
    left: -80px;
    bottom: 110px;
    top: auto;
  }

  &:before, &:after {
    background-color: #F3B7A2;
    content: "";
    height: 2px;
    width: 111px;
    display: inline-block;
  }

  &:before {
    margin-right: 39px;
    @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
      margin-right: 10px;
    }
  }
  &:after {
    margin-left: 39px;
    @media #{$laptop-device, $desktop-device, $tablet-device, $large-mobile} {
      margin-left: 10px;
    }
  }

  .swiper-pagination-current {
    margin-right: 10px;
  }

  .swiper-pagination-total {
    margin-left: 10px;
  }
}

.hero-two-slider-pagination {
  position: absolute;
  bottom: 58px !important;
  left: 56px !important;
  line-height: 1;
  z-index: 1;
  @media #{$desktop-device, $tablet-device, $large-mobile} {
    text-align: center;
    left: 0 !important;
    bottom: 20px !important;
  }
  .swiper-pagination-bullet {
    background-color: #fff;
    height: 6px;
    border-radius: 50px;
    margin: 0 7.5px;
    opacity: 1;
    width: 40px;
    transition: $transition-base;
    &-active {
      background-color: $primary;
    }
  }
}

.hero-slide-item {
  &.swiper-slide-active {
    .hero-slide-thumb {
      img {
        -webkit-animation-name: zoomIn;
        animation-name: zoomIn;
        -webkit-animation-delay: .5s;
        animation-delay: .5s;
        -webkit-animation-duration: 2s;
        animation-duration: 2s;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
      }
    }
    .hero-slide-content {
      .hero-slide-text-img {
        -webkit-animation-name: fadeInLeft;
        animation-name: fadeInLeft;
        -webkit-animation-delay: 1.3s;
        animation-delay: 1.3s;
        -webkit-animation-duration: 1.5s;
        animation-duration: 1.5s;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
      }
      .hero-slide-title {
        -webkit-animation-name: fadeInUp;
        animation-name: fadeInUp;
        -webkit-animation-delay: 2.2s;
        animation-delay: 2.2s;
        -webkit-animation-duration: 1.5s;
        animation-duration: 1.5s;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
      }
      .hero-slide-desc {
        -webkit-animation-name: fadeInUp;
        animation-name: fadeInUp;
        -webkit-animation-delay: 2.8s;
        animation-delay: 2.8s;
        -webkit-animation-duration: 1.5s;
        animation-duration: 1.5s;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
      }
      .btn  {
        -webkit-animation-name: fadeInUp;
        animation-name: fadeInUp;
        -webkit-animation-delay: 3.2s;
        animation-delay: 3.2s;
        -webkit-animation-duration: 1.5s;
        animation-duration: 1.5s;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
      }
    }
    .hero-slide-text-shape {
      @include transform(translate(0%, -50%) rotate(0) scale(1));
      opacity: 1;
    }
    .hero-slide-social-shape {
      -webkit-animation-name: heightIn;
      animation-name: heightIn;
      -webkit-animation-delay: 1s;
      animation-delay: 1s;
      -webkit-animation-duration: 3s;
      animation-duration: 3s;
      -webkit-animation-fill-mode: both;
      animation-fill-mode: both;
    }
  }
}

.hero-two-slide-item {
  &.swiper-slide-active {
    .hero-two-slide-thumb {
      img {
        -webkit-animation-name: zoomIn;
        animation-name: zoomIn;
        -webkit-animation-delay: .5s;
        animation-delay: .5s;
        -webkit-animation-duration: 2s;
        animation-duration: 2s;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
      }
    }
    .hero-two-slide-content {
      .hero-two-slide-text-img {
        -webkit-animation-name: fadeInLeft;
        animation-name: fadeInLeft;
        -webkit-animation-delay: 1.3s;
        animation-delay: 1.3s;
        -webkit-animation-duration: 1.5s;
        animation-duration: 1.5s;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
      }
      .hero-two-slide-title {
        -webkit-animation-name: fadeInUp;
        animation-name: fadeInUp;
        -webkit-animation-delay: 2.2s;
        animation-delay: 2.2s;
        -webkit-animation-duration: 1.5s;
        animation-duration: 1.5s;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
      }
      .hero-two-slide-desc {
        -webkit-animation-name: fadeInUp;
        animation-name: fadeInUp;
        -webkit-animation-delay: 2.8s;
        animation-delay: 2.8s;
        -webkit-animation-duration: 1.5s;
        animation-duration: 1.5s;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
      }
      .btn  {
        -webkit-animation-name: fadeInUp;
        animation-name: fadeInUp;
        -webkit-animation-delay: 3.2s;
        animation-delay: 3.2s;
        -webkit-animation-duration: 1.5s;
        animation-duration: 1.5s;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
      }
      .ht-popup-video  {
        -webkit-animation-name: fadeInUp;
        animation-name: fadeInUp;
        -webkit-animation-delay: 3.3s;
        animation-delay: 3.3s;
        -webkit-animation-duration: 1.5s;
        animation-duration: 1.5s;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
      }
    }
  }
}

@-webkit-keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }

  50% {
    opacity: 1;
  }
}
@keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }

  50% {
    opacity: 1;
  }
}
@-webkit-keyframes heightIn {
  from {
    height: 0;
  }

  to {
    height: 524px;
  }
}
@keyframes heightIn {
  from {
    height: 0;
  }

  to {
    height: 524px;
  }
}
@-webkit-keyframes fadeInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-15%, 0, 0);
    transform: translate3d(-15%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes fadeInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-15%, 0, 0);
    transform: translate3d(-15%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@-webkit-keyframes fadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 80%, 0);
    transform: translate3d(0, 80%, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 80%, 0);
    transform: translate3d(0, 80%, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}