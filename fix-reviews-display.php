<?php
require_once 'config/dbconfig.php';

echo "<h2>🔧 Reviews Display Fix Summary</h2>";

echo "<h3>✅ Issues Fixed:</h3>";
echo "<ol>";
echo "<li><strong>Star Rating Highlighting:</strong> All star ratings now properly highlight with golden color (#ffc107) for filled stars and gray (#ddd) for empty stars</li>";
echo "<li><strong>Duplicate Name Display:</strong> Customer names no longer appear twice in reviews</li>";
echo "<li><strong>Real Ratings:</strong> All pages now use actual review ratings instead of hardcoded patterns</li>";
echo "<li><strong>Consistent Display:</strong> Star ratings are consistent across home page, category pages, and product details</li>";
echo "</ol>";

echo "<h3>🌟 Star Rating System:</h3>";
echo "<p><strong>How it works now:</strong></p>";
echo "<ul>";
echo "<li>⭐⭐⭐⭐⭐ = 5 stars (all golden)</li>";
echo "<li>⭐⭐⭐⭐☆ = 4 stars (4 golden, 1 gray)</li>";
echo "<li>⭐⭐⭐☆☆ = 3 stars (3 golden, 2 gray)</li>";
echo "<li>⭐⭐☆☆☆ = 2 stars (2 golden, 3 gray)</li>";
echo "<li>⭐☆☆☆☆ = 1 star (1 golden, 4 gray)</li>";
echo "</ul>";

// Check for any duplicate reviews
echo "<h3>🔍 Checking for Duplicate Reviews:</h3>";

$duplicates = fetchAll("
    SELECT product_id, customer_name, COUNT(*) as count
    FROM product_reviews 
    GROUP BY product_id, customer_name 
    HAVING COUNT(*) > 1
    ORDER BY count DESC
");

if (!empty($duplicates)) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>⚠ Found " . count($duplicates) . " duplicate review(s):</strong></p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>Product ID</th><th>Customer Name</th><th>Duplicate Count</th></tr>";
    
    foreach ($duplicates as $duplicate) {
        echo "<tr>";
        echo "<td>" . $duplicate['product_id'] . "</td>";
        echo "<td>" . htmlspecialchars($duplicate['customer_name']) . "</td>";
        echo "<td>" . $duplicate['count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<p><a href='check-duplicate-reviews.php' style='color: #856404; font-weight: bold;'>🔧 Click here to remove duplicates</a></p>";
    echo "</div>";
} else {
    echo "<p style='color: green;'>✅ No duplicate reviews found!</p>";
}

// Show current review statistics
echo "<h3>📊 Current Review Statistics:</h3>";

$stats = [
    'total' => fetchSingle("SELECT COUNT(*) as count FROM product_reviews")['count'] ?? 0,
    'approved' => fetchSingle("SELECT COUNT(*) as count FROM product_reviews WHERE status = 'approved'")['count'] ?? 0,
    'pending' => fetchSingle("SELECT COUNT(*) as count FROM product_reviews WHERE status = 'pending'")['count'] ?? 0,
    'rejected' => fetchSingle("SELECT COUNT(*) as count FROM product_reviews WHERE status = 'rejected'")['count'] ?? 0,
];

echo "<div style='display: flex; gap: 20px; margin: 20px 0;'>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; text-align: center; flex: 1;'>";
echo "<h4 style='margin: 0; color: #1976d2;'>" . $stats['total'] . "</h4>";
echo "<p style='margin: 5px 0; color: #1976d2;'>Total Reviews</p>";
echo "</div>";

echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; text-align: center; flex: 1;'>";
echo "<h4 style='margin: 0; color: #2e7d32;'>" . $stats['approved'] . "</h4>";
echo "<p style='margin: 5px 0; color: #2e7d32;'>Approved</p>";
echo "</div>";

echo "<div style='background: #fff3e0; padding: 15px; border-radius: 5px; text-align: center; flex: 1;'>";
echo "<h4 style='margin: 0; color: #f57c00;'>" . $stats['pending'] . "</h4>";
echo "<p style='margin: 5px 0; color: #f57c00;'>Pending</p>";
echo "</div>";

echo "<div style='background: #ffebee; padding: 15px; border-radius: 5px; text-align: center; flex: 1;'>";
echo "<h4 style='margin: 0; color: #c62828;'>" . $stats['rejected'] . "</h4>";
echo "<p style='margin: 5px 0; color: #c62828;'>Rejected</p>";
echo "</div>";
echo "</div>";

// Test star rating display
echo "<h3>🌟 Star Rating Display Test:</h3>";

if ($stats['approved'] > 0) {
    $sample_reviews = fetchAll("
        SELECT pr.*, p.title as product_title 
        FROM product_reviews pr
        LEFT JOIN products p ON pr.product_id = p.id
        WHERE pr.status = 'approved'
        ORDER BY pr.created_at DESC
        LIMIT 3
    ");
    
    echo "<p><strong>Sample reviews with star ratings:</strong></p>";
    
    foreach ($sample_reviews as $review) {
        echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h5>" . htmlspecialchars($review['customer_name']) . " - " . htmlspecialchars($review['product_title']) . "</h5>";
        echo "<div style='margin: 10px 0;'>";
        
        // Display stars
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $review['rating']) {
                echo "<span style='color: #ffc107; font-size: 18px;'>★</span>";
            } else {
                echo "<span style='color: #ddd; font-size: 18px;'>★</span>";
            }
        }
        echo " (" . $review['rating'] . "/5)";
        echo "</div>";
        echo "<p style='color: #666; font-style: italic;'>\"" . htmlspecialchars(substr($review['comment'], 0, 100)) . (strlen($review['comment']) > 100 ? '...' : '') . "\"</p>";
        echo "</div>";
    }
} else {
    echo "<p style='color: orange;'>No approved reviews found. <a href='add-sample-reviews.php'>Add some sample reviews</a> to test the display.</p>";
}

echo "<hr>";
echo "<h3>🎯 Next Steps:</h3>";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<p><strong>✅ Your reviews system is now working perfectly!</strong></p>";
echo "<p><strong>Test these pages to see the fixes:</strong></p>";
echo "<ol>";
echo "<li><a href='index.php' target='_blank' style='color: #155724; font-weight: bold;'>🏠 Home Page</a> - Check star ratings on product cards</li>";
echo "<li><a href='hair-care.php' target='_blank' style='color: #155724; font-weight: bold;'>💇 Hair Care Page</a> - Check product ratings</li>";
echo "<li><a href='skin-care.php' target='_blank' style='color: #155724; font-weight: bold;'>🧴 Skin Care Page</a> - Check product ratings</li>";
echo "<li><a href='health-care.php' target='_blank' style='color: #155724; font-weight: bold;'>💊 Health Care Page</a> - Check product ratings</li>";
echo "<li>Click on any product to see the <strong>Product Details Page</strong> - Check individual reviews</li>";
echo "</ol>";
echo "<p><strong>Admin Panel:</strong></p>";
echo "<ul>";
echo "<li><a href='admin/reviews.php' style='color: #155724; font-weight: bold;'>🔧 Manage All Reviews</a></li>";
echo "<li><a href='admin/products.php' style='color: #155724; font-weight: bold;'>📦 Manage Products & Reviews</a></li>";
echo "</ul>";
echo "</div>";

if ($duplicates) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p><strong>⚠ Action Required:</strong></p>";
    echo "<p>You have duplicate reviews that should be cleaned up. <a href='check-duplicate-reviews.php' style='color: #856404; font-weight: bold;'>Click here to remove duplicates</a></p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
table { border-collapse: collapse; width: 100%; margin: 15px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; font-weight: bold; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
hr { margin: 30px 0; border: none; border-top: 2px solid #eee; }
ol, ul { margin: 10px 0; }
</style>
