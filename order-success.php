<?php
session_start();
require_once 'config/dbconfig.php';

$order_number = $_GET['order'] ?? '';
$order = null;
$order_items = [];

if ($order_number) {
    // Get order details
    $order = fetchSingle("
        SELECT o.*, c.first_name, c.last_name, c.email, c.phone 
        FROM orders o 
        LEFT JOIN customers c ON o.customer_id = c.id 
        WHERE o.order_number = ?
    ", [$order_number]);
    
    if ($order) {
        // Get order items
        $order_items = fetchAll("
            SELECT oi.*, p.title, p.image, pv.size 
            FROM order_items oi 
            JOIN products p ON oi.product_id = p.id 
            LEFT JOIN product_volumes pv ON oi.volume_id = pv.id 
            WHERE oi.order_id = ?
        ", [$order['id']]);
    }
}

if (!$order) {
    header('Location: index.php');
    exit();
}

include 'header.php';
?>
<br>
<br>
<br>
<main class="main-content">
    <!--== Start Page Header Area Wrapper ==-->
    <div class="page-header-area" data-bg-img="assets/images/photos/bg3.jpg">
        <div class="container pt--0 pb--0">
            <div class="row">
                <div class="col-12">
                    <div class="page-header-content">
                        <h2 class="title" data-aos="fade-down" data-aos-duration="1000">Order Confirmation</h2>
                        <nav class="breadcrumb-area" data-aos="fade-down" data-aos-duration="1200">
                            <ul class="breadcrumb">
                                <li><a href="index.php">Home</a></li>
                                <li class="breadcrumb-sep">//</li>
                                <li>Order Success</li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--== End Page Header Area Wrapper ==-->

    <!--== Start Order Success Area Wrapper ==-->
    <section class="section-space">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="order-success-content text-center">
                        <div class="order-success-icon">
                            <i class="fa fa-check-circle fa-5x text-success mb-4"></i>
                        </div>
                        <h2 class="order-success-title">Thank you for your order!</h2>
                        <p class="order-success-message">
                            Your order has been placed successfully. We'll send you an email confirmation shortly.
                        </p>
                        
                        <div class="order-details-card">
                            <div class="card">
                                <div class="card-header">
                                    <h4>Order Details</h4>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Order Information</h6>
                                            <p><strong>Order Number:</strong> <?php echo htmlspecialchars($order['order_number']); ?></p>
                                            <p><strong>Order Date:</strong> <?php echo date('M d, Y', strtotime($order['created_at'])); ?></p>
                                            <p><strong>Payment Method:</strong> <?php echo ucfirst(str_replace('_', ' ', $order['payment_method'])); ?></p>
                                            <p><strong>Status:</strong> <span class="badge bg-warning"><?php echo ucfirst($order['status']); ?></span></p>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Customer Information</h6>
                                            <p><strong>Name:</strong> <?php echo htmlspecialchars($order['first_name'] . ' ' . $order['last_name']); ?></p>
                                            <p><strong>Email:</strong> <?php echo htmlspecialchars($order['email']); ?></p>
                                            <p><strong>Phone:</strong> <?php echo htmlspecialchars($order['phone']); ?></p>
                                            <p><strong>Address:</strong> <?php echo htmlspecialchars($order['shipping_address']); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="order-items-card mt-4">
                            <div class="card">
                                <div class="card-header">
                                    <h4>Order Items</h4>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>Product</th>
                                                    <th>Quantity</th>
                                                    <th>Price</th>
                                                    <th>Total</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($order_items as $item): ?>
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <img src="<?php echo htmlspecialchars($item['image'] ?: 'assets/images/shop/default.png'); ?>" 
                                                                     alt="<?php echo htmlspecialchars($item['title']); ?>" 
                                                                     width="50" height="50" class="me-3">
                                                                <div>
                                                                    <h6><?php echo htmlspecialchars($item['title']); ?></h6>
                                                                    <?php if ($item['size']): ?>
                                                                        <small class="text-muted"><?php echo htmlspecialchars($item['size']); ?></small>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td><?php echo $item['quantity']; ?></td>
                                                        <td>Rs. <?php echo number_format($item['unit_price'], 2); ?></td>
                                                        <td>Rs. <?php echo number_format($item['total_price'], 2); ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <th colspan="3">Subtotal</th>
                                                    <th>Rs. <?php echo number_format($order['total_amount'], 2); ?></th>
                                                </tr>
                                                <tr>
                                                    <th colspan="3">Shipping</th>
                                                    <th>Rs. <?php echo number_format($order['shipping_fee'], 2); ?></th>
                                                </tr>
                                                <tr class="table-active">
                                                    <th colspan="3">Total</th>
                                                    <th>Rs. <?php echo number_format($order['final_amount'], 2); ?></th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="order-success-actions mt-4">
                            <a href="index.php" class="btn btn-primary me-3">Continue Shopping</a>
                            <a href="contact.php" class="btn btn-outline-primary">Contact Us</a>
                        </div>

                        <div class="order-success-info mt-4">
                            <div class="alert alert-info">
                                <h6><i class="fa fa-info-circle"></i> What's Next?</h6>
                                <ul class="list-unstyled mb-0">
                                    <li>• You will receive an email confirmation shortly</li>
                                    <li>• We will process your order within 1-2 business days</li>
                                    <li>• You will receive tracking information once your order ships</li>
                                    <li>• Estimated delivery time: 3-5 business days</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!--== End Order Success Area Wrapper ==-->
</main>

<?php include 'footer.php'; ?>

<style>
.order-success-content {
    padding: 2rem 0;
}

.order-details-card .card,
.order-items-card .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.order-details-card .card-header,
.order-items-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
}

.order-success-actions .btn {
    min-width: 150px;
}

.table th {
    border-top: none;
    font-weight: 600;
}

.badge {
    font-size: 0.8rem;
}
</style>

    <?php include 'includes/offcanvas-menu.php'; ?>
    <?php include 'includes/search-modal.php'; ?>

</body>
</html>
