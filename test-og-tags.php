<?php
// Test Open Graph Tags - Check if social media sharing is working
require_once 'config/dbconfig.php';

// Get a sample product for testing
$product = fetchSingle("SELECT * FROM products WHERE status = 'active' AND image IS NOT NULL LIMIT 1");

if (!$product) {
    echo "<h1>❌ No products found with images</h1>";
    echo "<p>Please add some products with images first.</p>";
    exit;
}

// Prepare social media sharing data
$og_title = htmlspecialchars($product['title']);
$og_description = htmlspecialchars(strlen($product['description']) > 160 ? substr($product['description'], 0, 157) . '...' : $product['description']);
$og_image = $product['image'] ? 'https://' . $_SERVER['HTTP_HOST'] . '/' . $product['image'] : 'https://' . $_SERVER['HTTP_HOST'] . '/assets/images/shop/default.png';
$og_url = 'https://' . $_SERVER['HTTP_HOST'] . '/product-details.php?product=' . ($product['slug'] ?: $product['product_code']);
$og_price = $product['price'];
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Open Graph Test - <?php echo $og_title; ?></title>
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="product">
    <meta property="og:title" content="<?php echo $og_title; ?>">
    <meta property="og:description" content="<?php echo $og_description; ?>">
    <meta property="og:image" content="<?php echo $og_image; ?>">
    <meta property="og:url" content="<?php echo $og_url; ?>">
    <meta property="og:site_name" content="Dr.Zia Naturals">
    <meta property="product:price:amount" content="<?php echo $og_price; ?>">
    <meta property="product:price:currency" content="USD">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $og_title; ?>">
    <meta name="twitter:description" content="<?php echo $og_description; ?>">
    <meta name="twitter:image" content="<?php echo $og_image; ?>">
    
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test-card { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        img { max-width: 300px; height: auto; border-radius: 5px; }
        .meta-tag { background: #f8f9fa; padding: 5px; margin: 2px 0; font-family: monospace; font-size: 12px; }
        .test-links a { display: inline-block; margin: 10px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .test-links a:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🧪 Open Graph Tags Test</h1>
    
    <div class="test-card success">
        <h2>✅ Test Product Found</h2>
        <p><strong>Product:</strong> <?php echo $og_title; ?></p>
        <p><strong>Description:</strong> <?php echo $og_description; ?></p>
        <p><strong>Price:</strong> $<?php echo $og_price; ?></p>
        <p><strong>Image:</strong></p>
        <img src="<?php echo $og_image; ?>" alt="<?php echo $og_title; ?>">
    </div>
    
    <div class="test-card info">
        <h2>📋 Generated Open Graph Tags</h2>
        <div class="meta-tag">og:type = product</div>
        <div class="meta-tag">og:title = <?php echo $og_title; ?></div>
        <div class="meta-tag">og:description = <?php echo $og_description; ?></div>
        <div class="meta-tag">og:image = <?php echo $og_image; ?></div>
        <div class="meta-tag">og:url = <?php echo $og_url; ?></div>
        <div class="meta-tag">og:site_name = Dr.Zia Naturals</div>
        <div class="meta-tag">product:price:amount = <?php echo $og_price; ?></div>
        <div class="meta-tag">product:price:currency = USD</div>
    </div>
    
    <div class="test-card warning">
        <h2>🔗 Test Your Links</h2>
        <p>Use these tools to test how your product links will appear when shared:</p>
        
        <div class="test-links">
            <a href="https://developers.facebook.com/tools/debug/?q=<?php echo urlencode($og_url); ?>" target="_blank">
                📘 Facebook Debugger
            </a>
            <a href="https://cards-dev.twitter.com/validator?url=<?php echo urlencode($og_url); ?>" target="_blank">
                🐦 Twitter Card Validator
            </a>
            <a href="https://www.linkedin.com/post-inspector/inspect/<?php echo urlencode($og_url); ?>" target="_blank">
                💼 LinkedIn Post Inspector
            </a>
        </div>
        
        <p><strong>Test URL:</strong> <a href="<?php echo $og_url; ?>" target="_blank"><?php echo $og_url; ?></a></p>
    </div>
    
    <div class="test-card info">
        <h2>📱 WhatsApp Test Instructions</h2>
        <ol>
            <li><strong>Copy this URL:</strong> <code><?php echo $og_url; ?></code></li>
            <li><strong>Open WhatsApp</strong> on your phone or web</li>
            <li><strong>Paste the URL</strong> in any chat</li>
            <li><strong>Wait a few seconds</strong> - WhatsApp will fetch the preview</li>
            <li><strong>You should see:</strong>
                <ul>
                    <li>Product image</li>
                    <li>Product title</li>
                    <li>Product description</li>
                    <li>Website name (Dr.Zia Naturals)</li>
                </ul>
            </li>
        </ol>
    </div>
    
    <div class="test-card success">
        <h2>✅ Next Steps</h2>
        <p>If the preview looks good:</p>
        <ol>
            <li>✅ Open Graph tags are working correctly</li>
            <li>✅ All your product pages now support social media sharing</li>
            <li>✅ When customers share your products, they'll show nice previews</li>
        </ol>
        
        <p><strong>Test more products:</strong></p>
        <?php
        $more_products = fetchAll("SELECT title, slug, product_code FROM products WHERE status = 'active' LIMIT 5");
        foreach ($more_products as $p) {
            $test_url = 'https://' . $_SERVER['HTTP_HOST'] . '/product-details.php?product=' . ($p['slug'] ?: $p['product_code']);
            echo '<p><a href="' . $test_url . '" target="_blank">' . htmlspecialchars($p['title']) . '</a></p>';
        }
        ?>
    </div>
</body>
</html>
