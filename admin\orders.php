<?php
session_start();
require_once '../config/dbconfig.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit();
}

$success_message = '';
$error_message = '';

// Handle order status update
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'update_status') {
    $order_id = intval($_POST['order_id']);
    $new_status = $_POST['status'];
    $comment = trim($_POST['comment'] ?? '');
    
    $valid_statuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'];
    
    if (in_array($new_status, $valid_statuses)) {
        try {
            // Update order status
            $updated = updateData("UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?", [$new_status, $order_id]);
            
            if ($updated) {
                // Add status history
                executeQuery("
                    INSERT INTO order_status_history (order_id, status, comment, changed_by) 
                    VALUES (?, ?, ?, ?)
                ", [$order_id, $new_status, $comment, $_SESSION['admin_id']]);
                
                $success_message = 'Order status updated successfully!';
            } else {
                $error_message = 'Failed to update order status.';
            }
        } catch (Exception $e) {
            $error_message = 'Error updating order status: ' . $e->getMessage();
        }
    } else {
        $error_message = 'Invalid order status.';
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$search = $_GET['search'] ?? '';

// Build query
$where_conditions = [];
$params = [];

if ($status_filter) {
    $where_conditions[] = "o.status = ?";
    $params[] = $status_filter;
}

if ($date_from) {
    $where_conditions[] = "DATE(o.created_at) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $where_conditions[] = "DATE(o.created_at) <= ?";
    $params[] = $date_to;
}

if ($search) {
    $where_conditions[] = "(o.order_number LIKE ? OR c.first_name LIKE ? OR c.last_name LIKE ? OR c.email LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get orders with customer details
$orders = fetchAll("
    SELECT o.*, 
           c.first_name, c.last_name, c.email, c.phone,
           COUNT(oi.id) as item_count
    FROM orders o 
    LEFT JOIN customers c ON o.customer_id = c.id 
    LEFT JOIN order_items oi ON o.id = oi.order_id
    $where_clause
    GROUP BY o.id 
    ORDER BY o.created_at DESC
", $params);

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Orders Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                        <i class="fas fa-print"></i> Print
                    </button>
                </div>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                <option value="processing" <?php echo $status_filter === 'processing' ? 'selected' : ''; ?>>Processing</option>
                                <option value="shipped" <?php echo $status_filter === 'shipped' ? 'selected' : ''; ?>>Shipped</option>
                                <option value="delivered" <?php echo $status_filter === 'delivered' ? 'selected' : ''; ?>>Delivered</option>
                                <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                <option value="refunded" <?php echo $status_filter === 'refunded' ? 'selected' : ''; ?>>Refunded</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">From Date</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">To Date</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   placeholder="Order number, customer name, email..." value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">Filter</button>
                                <a href="orders.php" class="btn btn-outline-secondary">Clear</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Orders Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">All Orders (<?php echo count($orders); ?>)</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped data-table">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Customer</th>
                                    <th>Items</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                    <th>Payment</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orders as $order): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($order['order_number']); ?></strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars(($order['first_name'] ?? 'Guest') . ' ' . ($order['last_name'] ?? '')); ?></strong>
                                                <?php if ($order['email']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($order['email']); ?></small>
                                                <?php endif; ?>
                                                <?php if ($order['phone']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($order['phone']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $order['item_count']; ?> items</span>
                                        </td>
                                        <td>
                                            <strong>Rs. <?php echo number_format($order['final_amount'], 2); ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php 
                                                echo match($order['status']) {
                                                    'pending' => 'warning',
                                                    'processing' => 'info',
                                                    'shipped' => 'primary',
                                                    'delivered' => 'success',
                                                    'cancelled' => 'danger',
                                                    'refunded' => 'secondary',
                                                    default => 'secondary'
                                                };
                                            ?>">
                                                <?php echo ucfirst($order['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $order['payment_status'] === 'paid' ? 'success' : 'warning'; ?>">
                                                <?php echo ucfirst($order['payment_status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php echo date('M d, Y', strtotime($order['created_at'])); ?>
                                            <br><small class="text-muted"><?php echo date('H:i', strtotime($order['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="order-details.php?id=<?php echo $order['id']; ?>"
                                                   class="btn btn-sm btn-outline-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="send-order-email.php?id=<?php echo $order['id']; ?>"
                                                   class="btn btn-sm btn-outline-info" title="Send Email Notification">
                                                    <i class="fas fa-envelope"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-success"
                                                        data-bs-toggle="modal" data-bs-target="#updateStatusModal"
                                                        data-order-id="<?php echo $order['id']; ?>"
                                                        data-order-number="<?php echo htmlspecialchars($order['order_number']); ?>"
                                                        data-current-status="<?php echo $order['status']; ?>"
                                                        title="Update Status">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="row mt-4">
                <?php
                $stats = [
                    'pending' => ['count' => 0, 'color' => 'warning', 'icon' => 'clock'],
                    'processing' => ['count' => 0, 'color' => 'info', 'icon' => 'cog'],
                    'shipped' => ['count' => 0, 'color' => 'primary', 'icon' => 'truck'],
                    'delivered' => ['count' => 0, 'color' => 'success', 'icon' => 'check-circle']
                ];
                
                foreach ($orders as $order) {
                    if (isset($stats[$order['status']])) {
                        $stats[$order['status']]['count']++;
                    }
                }
                ?>
                
                <?php foreach ($stats as $status => $stat): ?>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-<?php echo $stat['icon']; ?> fa-2x text-<?php echo $stat['color']; ?> mb-2"></i>
                            <h5 class="card-title text-<?php echo $stat['color']; ?>"><?php echo $stat['count']; ?></h5>
                            <p class="card-text"><?php echo ucfirst($status); ?> Orders</p>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </main>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <div class="modal-header">
                    <h5 class="modal-title">Update Order Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="order_id" id="modal-order-id">
                    
                    <div class="mb-3">
                        <label for="modal-order-number" class="form-label">Order Number</label>
                        <input type="text" class="form-control" id="modal-order-number" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal-status" class="form-label">New Status</label>
                        <select class="form-control" id="modal-status" name="status" required>
                            <option value="pending">Pending</option>
                            <option value="processing">Processing</option>
                            <option value="shipped">Shipped</option>
                            <option value="delivered">Delivered</option>
                            <option value="cancelled">Cancelled</option>
                            <option value="refunded">Refunded</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal-comment" class="form-label">Comment (Optional)</label>
                        <textarea class="form-control" id="modal-comment" name="comment" rows="3" 
                                  placeholder="Add a note about this status change..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Handle update status modal
document.addEventListener('DOMContentLoaded', function() {
    const updateStatusModal = document.getElementById('updateStatusModal');
    updateStatusModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const orderId = button.getAttribute('data-order-id');
        const orderNumber = button.getAttribute('data-order-number');
        const currentStatus = button.getAttribute('data-current-status');
        
        document.getElementById('modal-order-id').value = orderId;
        document.getElementById('modal-order-number').value = orderNumber;
        document.getElementById('modal-status').value = currentStatus;
        document.getElementById('modal-comment').value = '';
    });
});
</script>

<?php include 'includes/footer.php'; ?>
