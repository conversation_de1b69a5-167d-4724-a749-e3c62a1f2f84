<?php
// Test the bullet point function
function formatAsBulletPoints($text) {
    if (empty($text)) {
        return '';
    }
    
    // Split text by line breaks
    $lines = preg_split('/\r\n|\r|\n/', trim($text));
    
    // Filter out empty lines and trim whitespace
    $lines = array_filter(array_map('trim', $lines), function($line) {
        return !empty($line);
    });
    
    if (empty($lines)) {
        return '';
    }
    
    // If there's only one line, return as paragraph
    if (count($lines) == 1) {
        return '<p>' . htmlspecialchars($lines[0]) . '</p>';
    }
    
    // Convert to bullet points
    $html = '<ul class="product-bullet-list">';
    foreach ($lines as $line) {
        // Remove any existing bullet characters (-, *, •, etc.)
        $line = preg_replace('/^[\s\-\*\•\→\►\▪\▫\‣\⁃]+\s*/', '', $line);
        $line = trim($line);
        if (!empty($line)) {
            $html .= '<li>' . htmlspecialchars($line) . '</li>';
        }
    }
    $html .= '</ul>';
    
    return $html;
}

// Test text from your screenshot
$test_text = "Deeply cleanses the skin, removing dirt and impurities
Brightens dull skin and promotes a radiant glow
Enriched with Vitamin C and Vitamin E for antioxidant protection
Helps restore smooth, refreshed, and revitalized skin
Gentle formula suitable for daily use
Hydrates and soothes for a soft, healthy complexion";

echo "<h2>Debug: Bullet Points Test</h2>";
echo "<h3>Input Text:</h3>";
echo "<pre>" . htmlspecialchars($test_text) . "</pre>";

echo "<h3>Function Output (Raw HTML):</h3>";
$output = formatAsBulletPoints($test_text);
echo "<pre>" . htmlspecialchars($output) . "</pre>";

echo "<h3>Rendered Output:</h3>";
?>
<!DOCTYPE html>
<html>
<head>
    <style>
        /* Product Description Bullet List Styling */
        .product-bullet-list {
            list-style: none;
            padding-left: 0;
            margin: 15px 0;
        }

        .product-bullet-list li {
            position: relative;
            padding: 8px 0 8px 25px;
            line-height: 1.6;
            color: #555;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .product-bullet-list li:before {
            content: "•";
            position: absolute;
            left: 0;
            top: 8px;
            color: #FF6565;
            font-weight: bold;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div style="border: 1px solid #ccc; padding: 20px; margin: 20px 0;">
        <?php echo $output; ?>
    </div>
    
    <h3>What you should see:</h3>
    <p>Each line should appear as a separate bullet point with red bullets (•)</p>
    
    <h3>If it's not working:</h3>
    <ul>
        <li>Check if CSS is loading properly</li>
        <li>Check browser developer tools for CSS conflicts</li>
        <li>Verify the HTML structure is correct</li>
    </ul>
</body>
</html>
