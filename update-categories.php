<?php
require_once 'config/dbconfig.php';

echo "<h1>Updating Categories for New Website Structure</h1>";

try {
    $pdo->beginTransaction();
    
    // 1. Ensure main categories exist
    echo "<h2>1. Creating Main Categories...</h2>";
    $main_categories = [
        ['Hair Care', 'Products for hair growth, hair fall control, and overall hair health'],
        ['Skin Care', 'Facial cleansers, moisturizers, and skincare products for healthy skin'],
        ['Health Care', 'Natural health and wellness products for overall well-being']
    ];
    
    $main_category_ids = [];
    foreach ($main_categories as $cat) {
        $existing = fetchSingle("SELECT id FROM categories WHERE name = ?", [$cat[0]]);
        if ($existing) {
            $main_category_ids[$cat[0]] = $existing['id'];
            echo "<p>✅ Main category '{$cat[0]}' already exists (ID: {$existing['id']})</p>";
        } else {
            $cat_id = insertData("INSERT INTO categories (name, description, status) VALUES (?, ?, 'active')", [$cat[0], $cat[1]]);
            $main_category_ids[$cat[0]] = $cat_id;
            echo "<p>✅ Created main category '{$cat[0]}' (ID: {$cat_id})</p>";
        }
    }
    
    // 2. Update existing products to be properly categorized
    echo "<h2>2. Re-categorizing Existing Products...</h2>";
    
    // Get all products
    $products = fetchAll("SELECT id, title, product_code FROM products WHERE status = 'active'");
    
    foreach ($products as $product) {
        $title_lower = strtolower($product['title']);
        $new_categories = [];
        
        // Clear existing categories for this product
        executeQuery("DELETE FROM product_categories WHERE product_id = ?", [$product['id']]);
        
        // Determine main category based on product title
        if (strpos($title_lower, 'hair') !== false || 
            strpos($title_lower, 'serum') !== false && strpos($title_lower, 'hair') !== false) {
            $new_categories[] = $main_category_ids['Hair Care'];
            echo "<p>📦 '{$product['title']}' → Hair Care</p>";
        }
        
        if (strpos($title_lower, 'face') !== false || 
            strpos($title_lower, 'skin') !== false || 
            strpos($title_lower, 'wash') !== false ||
            strpos($title_lower, 'cream') !== false ||
            strpos($title_lower, 'moisturizer') !== false ||
            strpos($title_lower, 'cleanser') !== false ||
            strpos($title_lower, 'acne') !== false) {
            // Exclude hair-related products from skin care
            if (strpos($title_lower, 'hair') === false) {
                $new_categories[] = $main_category_ids['Skin Care'];
                echo "<p>🧴 '{$product['title']}' → Skin Care</p>";
            }
        }
        
        if (strpos($title_lower, 'syrup') !== false ||
            strpos($title_lower, 'health') !== false ||
            strpos($title_lower, 'supplement') !== false ||
            strpos($title_lower, 'vitamin') !== false ||
            strpos($title_lower, 'medicine') !== false ||
            strpos($title_lower, 'kids') !== false ||
            strpos($title_lower, 'children') !== false ||
            strpos($title_lower, 'child') !== false) {
            // Exclude face wash from health care
            if (strpos($title_lower, 'wash') === false && strpos($title_lower, 'face') === false) {
                $new_categories[] = $main_category_ids['Health Care'];
                echo "<p>💊 '{$product['title']}' → Health Care</p>";
            }
        }
        
        // If no category was assigned, try to guess based on common patterns
        if (empty($new_categories)) {
            // Default assignment based on product code or other factors
            if (strpos($product['product_code'], 'HAIR') !== false) {
                $new_categories[] = $main_category_ids['Hair Care'];
                echo "<p>🔄 '{$product['title']}' → Hair Care (by product code)</p>";
            } elseif (strpos($product['product_code'], 'SKIN') !== false || strpos($product['product_code'], 'FACE') !== false) {
                $new_categories[] = $main_category_ids['Skin Care'];
                echo "<p>🔄 '{$product['title']}' → Skin Care (by product code)</p>";
            } else {
                // Default to Skin Care for uncategorized products
                $new_categories[] = $main_category_ids['Skin Care'];
                echo "<p>❓ '{$product['title']}' → Skin Care (default)</p>";
            }
        }
        
        // Insert new category relationships
        foreach ($new_categories as $cat_id) {
            executeQuery("INSERT INTO product_categories (product_id, category_id) VALUES (?, ?)", [$product['id'], $cat_id]);
        }
    }
    
    // 3. Update subcategories to also map to main categories
    echo "<h2>3. Updating Subcategory Mappings...</h2>";
    
    // Get Face Care products and add them to Skin Care
    $face_care_cat = fetchSingle("SELECT id FROM categories WHERE name = 'Face Care'");
    if ($face_care_cat) {
        $face_care_products = fetchAll("
            SELECT DISTINCT p.id, p.title 
            FROM products p 
            JOIN product_categories pc ON p.id = pc.product_id 
            WHERE pc.category_id = ?
        ", [$face_care_cat['id']]);
        
        foreach ($face_care_products as $product) {
            // Check if already in Skin Care
            $existing = fetchSingle("
                SELECT id FROM product_categories 
                WHERE product_id = ? AND category_id = ?
            ", [$product['id'], $main_category_ids['Skin Care']]);
            
            if (!$existing) {
                executeQuery("INSERT INTO product_categories (product_id, category_id) VALUES (?, ?)", 
                    [$product['id'], $main_category_ids['Skin Care']]);
                echo "<p>🔗 Added '{$product['title']}' to Skin Care (from Face Care)</p>";
            }
        }
    }
    
    // Get Anti-Acne products and add them to Skin Care
    $anti_acne_cat = fetchSingle("SELECT id FROM categories WHERE name = 'Anti-Acne'");
    if ($anti_acne_cat) {
        $anti_acne_products = fetchAll("
            SELECT DISTINCT p.id, p.title 
            FROM products p 
            JOIN product_categories pc ON p.id = pc.product_id 
            WHERE pc.category_id = ?
        ", [$anti_acne_cat['id']]);
        
        foreach ($anti_acne_products as $product) {
            // Check if already in Skin Care
            $existing = fetchSingle("
                SELECT id FROM product_categories 
                WHERE product_id = ? AND category_id = ?
            ", [$product['id'], $main_category_ids['Skin Care']]);
            
            if (!$existing) {
                executeQuery("INSERT INTO product_categories (product_id, category_id) VALUES (?, ?)", 
                    [$product['id'], $main_category_ids['Skin Care']]);
                echo "<p>🔗 Added '{$product['title']}' to Skin Care (from Anti-Acne)</p>";
            }
        }
    }
    
    $pdo->commit();
    
    echo "<h2>✅ Update Completed Successfully!</h2>";
    echo "<h3>Summary:</h3>";
    echo "<ul>";
    echo "<li>✅ Main categories (Hair Care, Skin Care, Health Care) are ready</li>";
    echo "<li>✅ Products re-categorized based on content</li>";
    echo "<li>✅ Face Care products mapped to Skin Care</li>";
    echo "<li>✅ Anti-Acne products mapped to Skin Care</li>";
    echo "<li>✅ Hair serum stays in Hair Care</li>";
    echo "<li>✅ Face wash moved to Skin Care</li>";
    echo "</ul>";
    
    // Show category counts
    echo "<h3>Category Product Counts:</h3>";
    $category_counts = fetchAll("
        SELECT c.name, COUNT(pc.product_id) as product_count
        FROM categories c
        LEFT JOIN product_categories pc ON c.id = pc.category_id
        WHERE c.name IN ('Hair Care', 'Skin Care', 'Health Care')
        GROUP BY c.id, c.name
        ORDER BY c.name
    ");
    
    foreach ($category_counts as $count) {
        echo "<p><strong>{$count['name']}</strong>: {$count['product_count']} products</p>";
    }
    
} catch (Exception $e) {
    $pdo->rollBack();
    echo "<h2>❌ Error: " . $e->getMessage() . "</h2>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Category Update Complete</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        h1 { color: #333; }
        h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        ul { background: #f9f9f9; padding: 20px; border-radius: 5px; }
        .btn { 
            display: inline-block; 
            padding: 10px 20px; 
            margin: 10px 5px; 
            text-decoration: none; 
            border-radius: 5px; 
            color: white; 
        }
        .btn-primary { background: #007bff; }
        .btn-success { background: #28a745; }
        .btn-info { background: #17a2b8; }
        .btn-secondary { background: #6c757d; }
    </style>
</head>
<body>
    <h2>🎉 Next Steps:</h2>
    <p>Your categories are now properly configured! Test the new structure:</p>
    
    <a href="hair-care.php" class="btn btn-primary">View Hair Care Products</a>
    <a href="skin-care.php" class="btn btn-success">View Skin Care Products</a>
    <a href="health-care.php" class="btn btn-info">View Health Care Products</a>
    <a href="admin/products.php" class="btn btn-secondary">Admin Panel</a>
    
    <h3>📋 Admin Instructions:</h3>
    <ol>
        <li><strong>Adding Face Care Products:</strong> Select "Face Care" category → Will automatically appear in Skin Care page</li>
        <li><strong>Adding Hair Care Products:</strong> Select "Hair Care" category → Will appear in Hair Care page</li>
        <li><strong>Adding Health Care Products:</strong> Select "Health Care" category → Will appear in Health Care page</li>
        <li><strong>Adding Anti-Acne Products:</strong> Select "Anti-Acne" category → Will automatically appear in Skin Care page</li>
    </ol>
</body>
</html>
