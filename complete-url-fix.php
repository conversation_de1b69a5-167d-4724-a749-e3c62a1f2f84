<?php
require_once 'config/dbconfig.php';

echo "<h1>🔧 Complete URL Fix - Force New URLs Only</h1>";
echo "<p>This will update all product links and set up redirects to force new SEO-friendly URLs.</p>";

try {
    // Step 1: Update all product links in files
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🔗 Step 1: Update All Product Links</h2>";
    
    // Get all products with slugs
    $products = fetchAll("SELECT product_code, slug FROM products WHERE status = 'active' AND slug IS NOT NULL");
    
    if (empty($products)) {
        echo "<p style='color: red;'>❌ No products with slugs found. Please run the database setup first.</p>";
        exit;
    }
    
    // Create mapping
    $codeToSlug = [];
    foreach ($products as $product) {
        $codeToSlug[$product['product_code']] = $product['slug'];
    }
    
    // Files to update
    $filesToUpdate = [
        'index.php',
        'hair-care.php', 
        'skin-care.php',
        'health-care.php',
        'personal-care.php',
        'search.php'
    ];
    
    $totalUpdates = 0;
    
    foreach ($filesToUpdate as $filename) {
        if (!file_exists($filename)) {
            echo "<p style='color: orange;'>⚠ File not found: $filename</p>";
            continue;
        }
        
        $content = file_get_contents($filename);
        $originalContent = $content;
        $fileUpdates = 0;
        
        // Update each product code to slug
        foreach ($codeToSlug as $productCode => $slug) {
            // Pattern to match product-details.php?product=PRODXXX
            $pattern = '/product-details\.php\?product=' . preg_quote($productCode, '/') . '(?=["\'\s&])/';
            $replacement = 'product-details.php?product=' . $slug;
            
            $matches = preg_match_all($pattern, $content);
            if ($matches > 0) {
                $content = preg_replace($pattern, $replacement, $content);
                $fileUpdates += $matches;
            }
        }
        
        // Save updated file
        if ($content !== $originalContent) {
            if (file_put_contents($filename, $content)) {
                echo "<p style='color: green;'>✅ Updated $filename ($fileUpdates links updated)</p>";
                $totalUpdates += $fileUpdates;
            } else {
                echo "<p style='color: red;'>❌ Failed to update $filename</p>";
            }
        } else {
            echo "<p style='color: #6c757d;'>ℹ No updates needed for $filename</p>";
        }
    }
    
    echo "<p><strong>Total links updated: $totalUpdates</strong></p>";
    echo "</div>";

    // Step 2: Create .htaccess redirects
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>⚙ Step 2: Create .htaccess Redirects</h2>";
    
    // Backup existing .htaccess
    if (file_exists('.htaccess')) {
        $backup_name = '.htaccess.backup.' . date('Y-m-d-H-i-s');
        copy('.htaccess', $backup_name);
        echo "<p>✅ Backed up existing .htaccess to $backup_name</p>";
    }
    
    // Create new .htaccess content
    $htaccess_content = "# SEO-Friendly Product URLs - Auto-generated\n";
    $htaccess_content .= "# Generated on " . date('Y-m-d H:i:s') . "\n\n";
    
    $htaccess_content .= "RewriteEngine On\n\n";
    
    // Add redirects from old product codes to new slugs
    $htaccess_content .= "# Redirect old product code URLs to new slug URLs (301 permanent redirects)\n";
    foreach ($products as $product) {
        $htaccess_content .= "RewriteCond %{QUERY_STRING} ^product=" . preg_quote($product['product_code'], '/') . "(&.*)?$\n";
        $htaccess_content .= "RewriteRule ^product-details\\.php$ /product-details.php?product=" . $product['slug'] . "&%2 [R=301,L]\n\n";
    }
    
    // Add general rules
    $htaccess_content .= "# General rules\n";
    $htaccess_content .= "RewriteCond %{QUERY_STRING} (.*)\n";
    $htaccess_content .= "RewriteRule ^product-details$ /product-details.php?%1 [R=301,L]\n\n";
    
    // Add security and performance rules
    $htaccess_content .= "# Security Rules\n";
    $htaccess_content .= "RewriteCond %{THE_REQUEST} /([^?\\s]*)\\.php[?\\s] [NC]\n";
    $htaccess_content .= "RewriteRule ^ /%1? [R=301,L]\n\n";
    
    $htaccess_content .= "# Performance - Enable Compression\n";
    $htaccess_content .= "<IfModule mod_deflate.c>\n";
    $htaccess_content .= "    AddOutputFilterByType DEFLATE text/plain\n";
    $htaccess_content .= "    AddOutputFilterByType DEFLATE text/html\n";
    $htaccess_content .= "    AddOutputFilterByType DEFLATE text/xml\n";
    $htaccess_content .= "    AddOutputFilterByType DEFLATE text/css\n";
    $htaccess_content .= "    AddOutputFilterByType DEFLATE application/xml\n";
    $htaccess_content .= "    AddOutputFilterByType DEFLATE application/xhtml+xml\n";
    $htaccess_content .= "    AddOutputFilterByType DEFLATE application/rss+xml\n";
    $htaccess_content .= "    AddOutputFilterByType DEFLATE application/javascript\n";
    $htaccess_content .= "    AddOutputFilterByType DEFLATE application/x-javascript\n";
    $htaccess_content .= "</IfModule>\n\n";
    
    $htaccess_content .= "# Browser Caching\n";
    $htaccess_content .= "<IfModule mod_expires.c>\n";
    $htaccess_content .= "    ExpiresActive on\n";
    $htaccess_content .= "    ExpiresByType text/css \"access plus 1 year\"\n";
    $htaccess_content .= "    ExpiresByType application/javascript \"access plus 1 year\"\n";
    $htaccess_content .= "    ExpiresByType image/png \"access plus 1 year\"\n";
    $htaccess_content .= "    ExpiresByType image/jpg \"access plus 1 year\"\n";
    $htaccess_content .= "    ExpiresByType image/jpeg \"access plus 1 year\"\n";
    $htaccess_content .= "</IfModule>\n";
    
    // Write .htaccess file
    if (file_put_contents('.htaccess', $htaccess_content)) {
        echo "<p style='color: green;'>✅ Created .htaccess file with " . count($products) . " redirect rules</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create .htaccess file</p>";
    }
    echo "</div>";

    // Step 3: Verification
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🎉 Implementation Complete!</h2>";
    echo "<p><strong>What was accomplished:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Updated product links in " . count($filesToUpdate) . " files</li>";
    echo "<li>✅ Created .htaccess with " . count($products) . " redirect rules</li>";
    echo "<li>✅ Old URLs now automatically redirect to new SEO-friendly URLs</li>";
    echo "<li>✅ Added performance and security optimizations</li>";
    echo "</ul>";
    echo "</div>";

    // Test examples
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🧪 Test Your Redirects</h2>";
    echo "<p>These old URLs should now automatically redirect to the new format:</p>";
    
    $sampleProducts = array_slice($products, 0, 3);
    foreach ($sampleProducts as $product) {
        $oldUrl = "product-details.php?product=" . $product['product_code'];
        $newUrl = "product-details.php?product=" . $product['slug'];
        
        echo "<div style='background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff;'>";
        echo "<p><strong>Test Redirect:</strong></p>";
        echo "<p>Old URL: <a href='$oldUrl' target='_blank' style='color: #dc3545; font-family: monospace;'>$oldUrl</a></p>";
        echo "<p>Should redirect to: <span style='color: #28a745; font-family: monospace; font-weight: bold;'>$newUrl</span></p>";
        echo "</div>";
    }
    echo "</div>";

    // Category page links
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>📱 Test Updated Category Pages</h2>";
    echo "<p>Visit these pages - all product links should now use SEO-friendly URLs:</p>";
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
    foreach ($filesToUpdate as $filename) {
        if (file_exists($filename) && $filename !== 'search.php') {
            $pageName = ucwords(str_replace(['-', '.php'], [' ', ''], $filename));
            echo "<a href='$filename' target='_blank' style='background: #28a745; color: white; padding: 15px; text-decoration: none; border-radius: 5px; text-align: center; font-weight: bold;'>🔗 $pageName</a>";
        }
    }
    echo "</div>";
    echo "</div>";

    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center;'>";
    echo "<h2>🎯 All Done!</h2>";
    echo "<p style='font-size: 18px; color: #0c5460;'>Your website now uses SEO-friendly URLs exclusively!</p>";
    echo "<p>Old URLs automatically redirect to new ones with 301 redirects for SEO benefits.</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p style='color: #721c24;'>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
a { text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
