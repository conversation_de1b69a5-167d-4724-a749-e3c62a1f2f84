<?php
session_start();
require_once '../config/dbconfig.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

echo "<h2>🧴 Setting Up Personal Care in Admin Panel</h2>";

try {
    // Check if Personal Care category exists
    $existing_category = fetchSingle("SELECT * FROM categories WHERE name = 'Personal Care'");
    
    if (!$existing_category) {
        // Create Personal Care category
        $category_id = insertData("
            INSERT INTO categories (name, description, status, created_at) 
            VALUES (?, ?, 'active', NOW())
        ", [
            'Personal Care',
            'Premium personal care products for daily hygiene and wellness'
        ]);
        
        echo "<p style='color: green;'>✅ Created Personal Care category (ID: $category_id)</p>";
    } else {
        $category_id = $existing_category['id'];
        echo "<p style='color: blue;'>ℹ️ Personal Care category already exists (ID: $category_id)</p>";
    }
    
    // Get all categories for display
    $all_categories = fetchAll("SELECT * FROM categories ORDER BY name ASC");
    
    echo "<h3>📊 Current Categories in Admin Panel</h3>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 12px; text-align: left;'>ID</th>";
    echo "<th style='padding: 12px; text-align: left;'>Category Name</th>";
    echo "<th style='padding: 12px; text-align: left;'>Description</th>";
    echo "<th style='padding: 12px; text-align: left;'>Status</th>";
    echo "<th style='padding: 12px; text-align: left;'>Products Count</th>";
    echo "</tr>";
    
    foreach ($all_categories as $category) {
        // Get product count for each category
        $product_count = fetchSingle("
            SELECT COUNT(*) as count 
            FROM products p
            JOIN product_categories pc ON p.id = pc.product_id
            WHERE pc.category_id = ? AND p.status = 'active'
        ", [$category['id']]);
        
        $row_style = $category['name'] === 'Personal Care' ? 'background: #d4edda;' : '';
        
        echo "<tr style='$row_style'>";
        echo "<td style='padding: 12px;'>" . $category['id'] . "</td>";
        echo "<td style='padding: 12px; font-weight: bold;'>" . htmlspecialchars($category['name']) . "</td>";
        echo "<td style='padding: 12px;'>" . htmlspecialchars($category['description']) . "</td>";
        echo "<td style='padding: 12px;'>";
        if ($category['status'] === 'active') {
            echo "<span style='color: green; font-weight: bold;'>✅ Active</span>";
        } else {
            echo "<span style='color: red; font-weight: bold;'>❌ Inactive</span>";
        }
        echo "</td>";
        echo "<td style='padding: 12px;'>" . $product_count['count'] . " products</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Sample Personal Care products to add (if needed)
    $personal_care_products = [
        [
            'title' => 'Natural Body Soap',
            'description' => 'Gentle cleansing soap made with natural ingredients for soft and smooth skin.',
            'price' => 299.00,
            'sale_price' => 249.00,
            'product_code' => 'PC001',
            'image' => 'assets/images/shop/body-soap.jpg',
            'key_benefits' => 'Deep cleansing • Moisturizing • Natural ingredients • Gentle on skin',
            'how_to_use' => 'Wet skin with water • Apply soap and create lather • Massage gently • Rinse thoroughly'
        ],
        [
            'title' => 'Herbal Deodorant',
            'description' => 'Long-lasting natural deodorant with herbal extracts for all-day freshness.',
            'price' => 399.00,
            'sale_price' => 349.00,
            'product_code' => 'PC002',
            'image' => 'assets/images/shop/herbal-deodorant.jpg',
            'key_benefits' => '24-hour protection • Natural herbs • No harmful chemicals • Fresh fragrance',
            'how_to_use' => 'Apply to clean, dry underarms • Use daily for best results • Suitable for sensitive skin'
        ],
        [
            'title' => 'Moisturizing Body Lotion',
            'description' => 'Rich moisturizing lotion with natural oils to keep your skin hydrated all day.',
            'price' => 599.00,
            'sale_price' => 499.00,
            'product_code' => 'PC003',
            'image' => 'assets/images/shop/body-lotion.jpg',
            'key_benefits' => 'Deep moisturizing • Natural oils • Non-greasy formula • Long-lasting hydration',
            'how_to_use' => 'Apply to clean skin • Massage gently until absorbed • Use daily for best results'
        ]
    ];
    
    echo "<h3>📦 Add Sample Personal Care Products</h3>";
    
    if (isset($_POST['add_sample_products'])) {
        foreach ($personal_care_products as $product) {
            // Check if product already exists
            $existing_product = fetchSingle("SELECT id FROM products WHERE product_code = ?", [$product['product_code']]);
            
            if (!$existing_product) {
                // Insert product
                $product_id = insertData("
                    INSERT INTO products (
                        title, description, price, sale_price, product_code, image, 
                        key_benefits, how_to_use, status, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW())
                ", [
                    $product['title'],
                    $product['description'],
                    $product['price'],
                    $product['sale_price'],
                    $product['product_code'],
                    $product['image'],
                    $product['key_benefits'],
                    $product['how_to_use']
                ]);
                
                // Link product to Personal Care category
                executeQuery("
                    INSERT INTO product_categories (product_id, category_id) 
                    VALUES (?, ?)
                ", [$product_id, $category_id]);
                
                echo "<p style='color: green;'>✅ Added: " . htmlspecialchars($product['title']) . " (ID: $product_id)</p>";
            } else {
                echo "<p style='color: blue;'>ℹ️ Already exists: " . htmlspecialchars($product['title']) . "</p>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4 style='color: #721c24;'>❌ Error</h4>";
    echo "<p style='color: #721c24;'>Error setting up Personal Care: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Setup Personal Care Admin - Dr. Zia Naturals</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h2, h3, h4 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
        table { border-collapse: collapse; width: 100%; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; font-weight: bold; }
    </style>
</head>
<body>

<div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h4 style="color: #155724; margin: 0 0 15px 0;">✅ Personal Care Admin Setup Complete!</h4>
    <ul style="margin: 0; color: #155724;">
        <li><strong>Category:</strong> Personal Care is now available in admin panel</li>
        <li><strong>Add Products:</strong> Use the "Add Product" page to create personal care items</li>
        <li><strong>Category Selection:</strong> Personal Care appears in the categories dropdown</li>
        <li><strong>Auto-mapping:</strong> Products assigned to Personal Care stay in that category</li>
    </ul>
</div>

<div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h4>📋 How to Add Personal Care Products</h4>
    <ol>
        <li><strong>Go to Add Product:</strong> <a href="add-product.php">add-product.php</a></li>
        <li><strong>Fill product details:</strong> Title, description, price, etc.</li>
        <li><strong>Select Personal Care:</strong> Choose "Personal Care" from categories dropdown</li>
        <li><strong>Upload image:</strong> Add product image</li>
        <li><strong>Save product:</strong> Product will appear on personal-care.php page</li>
    </ol>
</div>

<div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h4>🧪 Add Sample Products (Optional)</h4>
    <p>Click the button below to add 3 sample personal care products for testing:</p>
    <form method="POST">
        <button type="submit" name="add_sample_products" class="btn btn-warning">
            <i class="fas fa-plus me-2"></i>Add Sample Personal Care Products
        </button>
    </form>
    <p><small><strong>Note:</strong> This will add Natural Body Soap, Herbal Deodorant, and Moisturizing Body Lotion.</small></p>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="add-product.php" style="background: #28a745; color: white; padding: 12px 25px; text-decoration: none; border-radius: 25px; margin: 5px;">
        <i class="fas fa-plus me-2"></i>Add New Product
    </a>
    <a href="products.php" style="background: #007bff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 25px; margin: 5px;">
        <i class="fas fa-list me-2"></i>Manage Products
    </a>
    <a href="categories.php" style="background: #6c757d; color: white; padding: 12px 25px; text-decoration: none; border-radius: 25px; margin: 5px;">
        <i class="fas fa-tags me-2"></i>Manage Categories
    </a>
    <a href="../personal-care.php" style="background: #667eea; color: white; padding: 12px 25px; text-decoration: none; border-radius: 25px; margin: 5px;">
        <i class="fas fa-spa me-2"></i>View Personal Care Page
    </a>
</div>

<div style="background: #d1ecf1; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
    <h3>🎉 Admin Panel Ready for Personal Care!</h3>
    <p>You can now add and manage Personal Care products through the admin panel.</p>
    <p><strong>Personal Care category is fully integrated!</strong></p>
</div>

</body>
</html>
