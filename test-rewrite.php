<?php
// Test if mod_rewrite is working
echo "<h1>Rewrite Test</h1>";
echo "<p>If you can see this page by accessing 'test-rewrite' (without .php), then mod_rewrite is working.</p>";
echo "<p>Current URL: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>Script name: " . $_SERVER['SCRIPT_NAME'] . "</p>";

// Check if mod_rewrite is loaded
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "<p style='color: green;'>✓ mod_rewrite is loaded</p>";
    } else {
        echo "<p style='color: red;'>✗ mod_rewrite is NOT loaded</p>";
    }
} else {
    echo "<p>Cannot check if mod_rewrite is loaded (not running on Apache or function not available)</p>";
}
?>
