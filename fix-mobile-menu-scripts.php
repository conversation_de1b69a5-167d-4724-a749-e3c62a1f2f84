<?php
echo "<h2>🔧 Fixing Mobile Menu JavaScript Issues</h2>";

// List of pages to check and fix
$pages_to_check = [
    'cart.php',
    'checkout.php',
    'my-account.php',
    'login.php',
    'register.php',
    'product-details.php'
];

echo "<h3>1. Checking Pages for JavaScript Includes</h3>";

foreach ($pages_to_check as $page) {
    if (file_exists($page)) {
        echo "<h4>📄 Checking: $page</h4>";
        
        $content = file_get_contents($page);
        
        // Check if the page has bootstrap.bundle.min.js and main.js
        $has_bootstrap = strpos($content, 'bootstrap.bundle.min.js') !== false;
        $has_main_js = strpos($content, 'main.js') !== false;
        $has_scripts_include = strpos($content, "include 'includes/scripts.php'") !== false || 
                              strpos($content, 'include("includes/scripts.php")') !== false;
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>Bootstrap JS:</strong> " . ($has_bootstrap ? "✅ Found" : "❌ Missing") . "</p>";
        echo "<p><strong>Main JS:</strong> " . ($has_main_js ? "✅ Found" : "❌ Missing") . "</p>";
        echo "<p><strong>Scripts Include:</strong> " . ($has_scripts_include ? "✅ Found" : "❌ Missing") . "</p>";
        
        if (!$has_bootstrap || !$has_main_js) {
            echo "<p style='color: red;'><strong>⚠ This page needs JavaScript fixes!</strong></p>";
            
            // Try to add the scripts include before </body>
            if (strpos($content, '</body>') !== false && !$has_scripts_include) {
                $new_content = str_replace('</body>', "<?php include 'includes/scripts.php'; ?>\n</body>", $content);
                
                if (file_put_contents($page, $new_content)) {
                    echo "<p style='color: green;'>✅ Added scripts include to $page</p>";
                } else {
                    echo "<p style='color: red;'>❌ Failed to update $page</p>";
                }
            }
        } else {
            echo "<p style='color: green;'><strong>✅ This page has all required scripts!</strong></p>";
        }
        echo "</div>";
    } else {
        echo "<p style='color: orange;'>⚠ File not found: $page</p>";
    }
}

echo "<hr>";

echo "<h3>2. Testing Mobile Menu Button Functionality</h3>";

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🧪 Mobile Menu Test Instructions:</h4>";
echo "<ol>";
echo "<li><strong>Open your website</strong> in a browser</li>";
echo "<li><strong>Resize the browser window</strong> to mobile size (or use developer tools)</li>";
echo "<li><strong>Look for the hamburger menu button</strong> (☰) in the top right</li>";
echo "<li><strong>Click the hamburger button</strong> - the mobile menu should slide in from the left</li>";
echo "<li><strong>Test on different pages:</strong></li>";
echo "<ul>";
echo "<li>🏠 <a href='index.php' target='_blank'>Home Page</a></li>";
echo "<li>💇 <a href='hair-care.php' target='_blank'>Hair Care Page</a></li>";
echo "<li>🧴 <a href='skin-care.php' target='_blank'>Skin Care Page</a></li>";
echo "<li>💊 <a href='health-care.php' target='_blank'>Health Care Page</a></li>";
echo "<li>ℹ <a href='about-us.php' target='_blank'>About Page</a></li>";
echo "<li>📞 <a href='contact.php' target='_blank'>Contact Page</a></li>";
echo "</ul>";
echo "</ol>";
echo "</div>";

echo "<h3>3. Common Mobile Menu Issues & Solutions</h3>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🔍 If Mobile Menu Still Doesn't Work:</h4>";
echo "<h5>Issue 1: JavaScript Console Errors</h5>";
echo "<p><strong>Solution:</strong> Open browser developer tools (F12) → Console tab → Look for red errors</p>";
echo "<h5>Issue 2: Bootstrap Not Loading</h5>";
echo "<p><strong>Solution:</strong> Check if bootstrap.bundle.min.js file exists in assets/js/vendor/</p>";
echo "<h5>Issue 3: Main.js Not Loading</h5>";
echo "<p><strong>Solution:</strong> Check if main.js file exists in assets/js/</p>";
echo "<h5>Issue 4: Wrong File Paths</h5>";
echo "<p><strong>Solution:</strong> Ensure script paths don't start with './' - use 'assets/js/' instead</p>";
echo "</div>";

echo "<h3>4. Manual Fix for Specific Pages</h3>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>📝 If a page still doesn't work, manually add this before &lt;/body&gt;:</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto;'>";
echo htmlspecialchars('<!-- JS Vendor, Plugins & Activation Script Files -->
<script src="assets/js/vendor/modernizr-3.11.7.min.js"></script>
<script src="assets/js/vendor/jquery-3.6.0.min.js"></script>
<script src="assets/js/vendor/jquery-migrate-3.3.2.min.js"></script>
<script src="assets/js/vendor/bootstrap.bundle.min.js"></script>
<script src="assets/js/plugins/swiper-bundle.min.js"></script>
<script src="assets/js/plugins/fancybox.min.js"></script>
<script src="assets/js/plugins/jquery.nice-select.min.js"></script>
<script src="assets/js/main.js"></script>');
echo "</pre>";
echo "</div>";

echo "<h3>5. Verify Mobile Menu Structure</h3>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>✅ Mobile Menu Should Have:</h4>";
echo "<ul>";
echo "<li><strong>Hamburger Button:</strong> Three horizontal lines (☰) in header</li>";
echo "<li><strong>Button Attributes:</strong> data-bs-toggle='offcanvas' data-bs-target='#AsideOffcanvasMenu'</li>";
echo "<li><strong>Menu Container:</strong> &lt;aside id='AsideOffcanvasMenu'&gt;</li>";
echo "<li><strong>Menu Items:</strong> Home, About, Hair Care, Skin Care, Health Care, Contact, Account</li>";
echo "<li><strong>JavaScript:</strong> Bootstrap offcanvas functionality + main.js</li>";
echo "</ul>";
echo "</div>";

echo "<h3>6. Browser Developer Tools Debug</h3>";

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🔧 Debug Steps:</h4>";
echo "<ol>";
echo "<li><strong>Open Developer Tools:</strong> Press F12 or right-click → Inspect</li>";
echo "<li><strong>Go to Console tab:</strong> Look for JavaScript errors (red text)</li>";
echo "<li><strong>Go to Network tab:</strong> Refresh page and check if all JS files load (200 status)</li>";
echo "<li><strong>Go to Elements tab:</strong> Find the hamburger button and check its attributes</li>";
echo "<li><strong>Test click:</strong> Click hamburger button and see if any errors appear in console</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<h3>📊 Summary</h3>";
echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<p><strong>✅ Updated script includes for better compatibility</strong></p>";
echo "<p><strong>✅ Added missing JavaScript files to pages that need them</strong></p>";
echo "<p><strong>✅ Fixed relative path issues in script includes</strong></p>";
echo "<p><strong>✅ Provided debugging instructions for troubleshooting</strong></p>";
echo "</div>";

echo "<h3>🎯 Next Steps</h3>";
echo "<p><strong>1.</strong> Test the mobile menu on each page using the links above</p>";
echo "<p><strong>2.</strong> If any page still doesn't work, check browser console for errors</p>";
echo "<p><strong>3.</strong> Use the manual fix code if needed for specific pages</p>";
echo "<p><strong>4.</strong> Ensure all JavaScript files exist in the assets/js/ directory</p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
hr { margin: 30px 0; border: none; border-top: 2px solid #eee; }
pre { font-size: 12px; line-height: 1.4; }
</style>
