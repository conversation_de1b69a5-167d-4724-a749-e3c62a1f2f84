<?php
session_start();
require_once 'config/dbconfig.php';

// Get cart items and count
$session_id = session_id();
$cart_items = [];
$cart_count = 0;

if ($session_id) {
    $cart_items = fetchAll("
        SELECT ci.*, p.title, p.image, p.product_code 
        FROM cart_items ci
        JOIN products p ON ci.product_id = p.id
        WHERE ci.session_id = ?
    ", [$session_id]);
    
    $cart_count_result = fetchSingle("SELECT SUM(quantity) as total FROM cart_items WHERE session_id = ?", [$session_id]);
    $cart_count = $cart_count_result['total'] ?? 0;
}

// Get sample products for testing
$products = fetchAll("SELECT * FROM products WHERE status = 'active' LIMIT 3");

include 'header.php';
?>

<main class="main-content">
    <div class="container mt-5">
        <h2>Cart Functionality Test</h2>
        
        <!-- Cart Status -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Cart Status</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Session ID:</strong> <?php echo htmlspecialchars($session_id); ?></p>
                        <p><strong>Cart Count:</strong> <span id="current-cart-count"><?php echo $cart_count; ?></span></p>
                        <p><strong>Items in Cart:</strong> <?php echo count($cart_items); ?></p>
                        <button class="btn btn-primary" onclick="refreshCartStatus()">Refresh Status</button>
                        <a href="cart.php" class="btn btn-success">View Cart</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <a href="index.php" class="btn btn-info">Go to Homepage</a>
                        <a href="cart.php" class="btn btn-warning">View Cart</a>
                        <a href="checkout.php" class="btn btn-success">Checkout</a>
                        <button class="btn btn-danger" onclick="clearCart()">Clear Cart</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Products -->
        <div class="row mb-4">
            <div class="col-12">
                <h4>Test Products (Add to Cart)</h4>
            </div>
            <?php foreach ($products as $product): ?>
            <div class="col-md-4 mb-3">
                <div class="card">
                    <img src="<?php echo htmlspecialchars($product['image'] ?: 'assets/images/shop/default.png'); ?>" 
                         class="card-img-top" alt="<?php echo htmlspecialchars($product['title']); ?>" style="height: 200px; object-fit: cover;">
                    <div class="card-body">
                        <h6 class="card-title"><?php echo htmlspecialchars($product['title']); ?></h6>
                        <p class="card-text">Rs. <?php echo number_format($product['price'], 2); ?></p>
                        <button class="btn btn-primary btn-sm" onclick="addToCart(<?php echo $product['id']; ?>)">
                            Add to Cart
                        </button>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Current Cart Items -->
        <?php if (!empty($cart_items)): ?>
        <div class="row">
            <div class="col-12">
                <h4>Current Cart Items</h4>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Quantity</th>
                                <th>Added At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($cart_items as $item): ?>
                            <tr>
                                <td>
                                    <img src="<?php echo htmlspecialchars($item['image'] ?: 'assets/images/shop/default.png'); ?>" 
                                         width="50" height="50" class="me-2">
                                    <?php echo htmlspecialchars($item['title']); ?>
                                </td>
                                <td><?php echo $item['quantity']; ?></td>
                                <td><?php echo date('M d, Y H:i', strtotime($item['added_at'])); ?></td>
                                <td>
                                    <button class="btn btn-sm btn-danger" onclick="removeFromCart(<?php echo $item['id']; ?>)">
                                        Remove
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="alert alert-info">
            <h5>Cart is Empty</h5>
            <p>Add some products to test the cart functionality.</p>
        </div>
        <?php endif; ?>
    </div>
</main>

<?php include 'footer.php'; ?>

<script>
// Add to cart function
function addToCart(productId) {
    fetch('cart/add-to-cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: 1
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Product added to cart!', 'success');
            refreshCartStatus();
            updateCartCount(data.cart_count);
        } else {
            showNotification('Error: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error adding to cart', 'error');
    });
}

// Remove from cart function
function removeFromCart(itemId) {
    if (confirm('Remove this item from cart?')) {
        // This would need a separate endpoint
        showNotification('Remove functionality needs to be implemented', 'info');
    }
}

// Clear cart function
function clearCart() {
    if (confirm('Clear entire cart?')) {
        fetch('cart/clear-cart.php', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Cart cleared!', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification('Error clearing cart', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error clearing cart', 'error');
        });
    }
}

// Refresh cart status
function refreshCartStatus() {
    fetch('cart/get-cart-count.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('current-cart-count').textContent = data.cart_count;
                updateCartCount(data.cart_count);
            }
        })
        .catch(error => console.error('Error:', error));
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// Update cart count
function updateCartCount(count) {
    const cartCountElement = document.querySelector('.cart-count');
    
    if (count > 0) {
        if (cartCountElement) {
            cartCountElement.textContent = count;
            cartCountElement.style.display = 'inline-block';
        }
    } else {
        if (cartCountElement) {
            cartCountElement.style.display = 'none';
        }
    }
}
</script>

</body>
</html>
