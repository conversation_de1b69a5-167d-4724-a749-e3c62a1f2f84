<?php
require_once 'config/dbconfig.php';

// Function to convert text to bullet points (same as in product-details.php)
function formatAsBulletPoints($text) {
    if (empty($text)) {
        return '';
    }
    
    // First try to split by line breaks
    $lines = preg_split('/\r\n|\r|\n/', trim($text));
    
    // If we only have one line, try to split by bullet characters
    if (count($lines) == 1) {
        // Split by bullet characters (•, *, -, etc.) followed by space
        $lines = preg_split('/[\s]*[•\*\-\→\►\▪\▫\‣\⁃]\s*/', $text);
    }
    
    // Filter out empty lines and trim whitespace
    $lines = array_filter(array_map('trim', $lines), function($line) {
        return !empty($line);
    });
    
    if (empty($lines)) {
        return '';
    }
    
    // If there's only one meaningful line, return as paragraph
    if (count($lines) == 1) {
        return '<p>' . htmlspecialchars($lines[0]) . '</p>';
    }
    
    // Convert to bullet points
    $html = '<ul class="product-bullet-list">';
    foreach ($lines as $line) {
        // Remove any remaining bullet characters at the start
        $line = preg_replace('/^[\s\-\*\•\→\►\▪\▫\‣\⁃]+\s*/', '', $line);
        $line = trim($line);
        if (!empty($line)) {
            $html .= '<li>' . htmlspecialchars($line) . '</li>';
        }
    }
    $html .= '</ul>';
    
    return $html;
}

echo "<h2>🔍 Debug Your Specific Product</h2>";

try {
    // Get products to test
    $products = fetchAll("SELECT id, title, description FROM products WHERE status = 'active' ORDER BY id DESC LIMIT 5");
    
    if ($products) {
        foreach ($products as $product) {
            echo "<div style='border: 2px solid #007bff; padding: 20px; margin: 20px 0; border-radius: 8px;'>";
            echo "<h3>🧴 Product: " . htmlspecialchars($product['title']) . "</h3>";
            
            echo "<h4>📝 Raw Description from Database:</h4>";
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap;'>";
            echo htmlspecialchars($product['description']);
            echo "</div>";
            
            echo "<h4>🔢 Analysis:</h4>";
            echo "<ul>";
            echo "<li><strong>Length:</strong> " . strlen($product['description']) . " characters</li>";
            echo "<li><strong>Line breaks (\\n):</strong> " . substr_count($product['description'], "\n") . "</li>";
            echo "<li><strong>Line breaks (\\r\\n):</strong> " . substr_count($product['description'], "\r\n") . "</li>";
            echo "<li><strong>Bullet chars (•):</strong> " . substr_count($product['description'], "•") . "</li>";
            echo "</ul>";
            
            echo "<h4>📋 Split Lines:</h4>";
            $lines = preg_split('/\r\n|\r|\n/', trim($product['description']));
            echo "<ol>";
            foreach ($lines as $i => $line) {
                echo "<li><strong>Line " . ($i + 1) . ":</strong> '" . htmlspecialchars($line) . "'</li>";
            }
            echo "</ol>";
            
            echo "<h4>✨ Function Output (HTML):</h4>";
            $output = formatAsBulletPoints($product['description']);
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
            echo "<pre>" . htmlspecialchars($output) . "</pre>";
            echo "</div>";
            
            echo "<h4>🎯 Rendered Result:</h4>";
            echo "<div style='background: #fff; border: 1px solid #ddd; padding: 15px; border-radius: 5px;'>";
            echo $output;
            echo "</div>";
            
            echo "</div>";
        }
    } else {
        echo "<p style='color: red;'>No products found</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Debug Product Description</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h2, h3, h4 { color: #333; }
        
        /* Product Description Bullet List Styling */
        .product-bullet-list {
            list-style: none;
            padding-left: 0;
            margin: 15px 0;
        }

        .product-bullet-list li {
            position: relative;
            padding: 8px 0 8px 25px;
            line-height: 1.6;
            color: #555;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .product-bullet-list li:before {
            content: "•";
            position: absolute;
            left: 0;
            top: 8px;
            color: #FF6565;
            font-weight: bold;
            font-size: 16px;
        }
    </style>
</head>
<body>

<div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3>🎯 What to Look For:</h3>
    <ul>
        <li><strong>If you see multiple lines</strong> in "Split Lines" → Function should create bullet points</li>
        <li><strong>If you see only 1 line</strong> → Text is stored as one line, function will try to split by bullets</li>
        <li><strong>Check "Rendered Result"</strong> → This is how it should appear on your product page</li>
    </ul>
</div>

<div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3>🔧 If Still Not Working:</h3>
    <ol>
        <li><strong>Check if CSS is loading</strong> - Look for red bullets in "Rendered Result"</li>
        <li><strong>Clear browser cache</strong> - Hard refresh with Ctrl+F5</li>
        <li><strong>Check browser console</strong> - Press F12 for any errors</li>
        <li><strong>Verify product page</strong> - Make sure you're viewing the updated product</li>
    </ol>
</div>

</body>
</html>
