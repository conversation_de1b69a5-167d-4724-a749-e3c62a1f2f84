<?php
session_start();
require_once '../config/dbconfig.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// Get Personal Care category info
$personal_care_category = fetchSingle("SELECT * FROM categories WHERE name = 'Personal Care'");
$personal_care_products = [];

if ($personal_care_category) {
    $personal_care_products = fetchAll("
        SELECT p.*, GROUP_CONCAT(c.name SEPARATOR ', ') as categories
        FROM products p 
        JOIN product_categories pc ON p.id = pc.product_id 
        JOIN categories c ON pc.category_id = c.id 
        WHERE c.name = 'Personal Care' AND p.status = 'active'
        GROUP BY p.id 
        ORDER BY p.created_at DESC
    ");
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Personal Care Admin Guide - Dr. Zia Naturals</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .guide-card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .guide-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
            text-align: center;
        }
        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .product-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>

<div class="guide-header">
    <div class="container">
        <h1><i class="fas fa-spa me-3"></i>Personal Care Admin Guide</h1>
        <p class="lead">Complete guide to managing Personal Care products in the admin panel</p>
    </div>
</div>

<div class="container my-5">
    
    <!-- Status Overview -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card guide-card text-center">
                <div class="card-body">
                    <i class="fas fa-tags fa-2x text-primary mb-3"></i>
                    <h5>Category Status</h5>
                    <?php if ($personal_care_category): ?>
                        <span class="badge bg-success fs-6">✅ Active</span>
                        <p class="mt-2 text-muted">ID: <?php echo $personal_care_category['id']; ?></p>
                    <?php else: ?>
                        <span class="badge bg-danger fs-6">❌ Not Found</span>
                        <p class="mt-2 text-muted">Category needs to be created</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card guide-card text-center">
                <div class="card-body">
                    <i class="fas fa-box fa-2x text-success mb-3"></i>
                    <h5>Products Count</h5>
                    <span class="badge bg-info fs-6"><?php echo count($personal_care_products); ?> Products</span>
                    <p class="mt-2 text-muted">Active personal care items</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card guide-card text-center">
                <div class="card-body">
                    <i class="fas fa-globe fa-2x text-warning mb-3"></i>
                    <h5>Frontend Page</h5>
                    <a href="../personal-care.php" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>View Page
                    </a>
                    <p class="mt-2 text-muted">Public personal care page</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Step-by-Step Guide -->
    <div class="card guide-card">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0"><i class="fas fa-list-ol me-2"></i>How to Add Personal Care Products</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-4">
                        <h6><span class="step-number">1</span>Access Add Product Page</h6>
                        <p class="text-muted">Navigate to the "Add Product" section in the admin panel.</p>
                        <a href="add-product.php" class="btn btn-success btn-sm">
                            <i class="fas fa-plus me-1"></i>Add New Product
                        </a>
                    </div>
                    
                    <div class="mb-4">
                        <h6><span class="step-number">2</span>Fill Basic Information</h6>
                        <ul class="text-muted">
                            <li>Product Code (e.g., PC001, PC002)</li>
                            <li>Product Title</li>
                            <li>Description</li>
                            <li>Price and Sale Price</li>
                        </ul>
                    </div>
                    
                    <div class="mb-4">
                        <h6><span class="step-number">3</span>Select Personal Care Category</h6>
                        <p class="text-muted">In the Categories dropdown, select "Personal Care". You can hold Ctrl to select multiple categories if needed.</p>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-4">
                        <h6><span class="step-number">4</span>Add Product Details</h6>
                        <ul class="text-muted">
                            <li>Key Benefits (bullet points)</li>
                            <li>How to Use instructions</li>
                            <li>Upload product image</li>
                            <li>Set status to "Active"</li>
                        </ul>
                    </div>
                    
                    <div class="mb-4">
                        <h6><span class="step-number">5</span>Save and Verify</h6>
                        <p class="text-muted">Click "Add Product" to save. The product will automatically appear on the Personal Care page.</p>
                    </div>
                    
                    <div class="mb-4">
                        <h6><span class="step-number">6</span>Manage Products</h6>
                        <a href="products.php" class="btn btn-info btn-sm">
                            <i class="fas fa-edit me-1"></i>Manage All Products
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Personal Care Products -->
    <div class="card guide-card">
        <div class="card-header bg-info text-white">
            <h4 class="mb-0"><i class="fas fa-spa me-2"></i>Current Personal Care Products</h4>
        </div>
        <div class="card-body">
            <?php if (!empty($personal_care_products)): ?>
                <div class="product-grid">
                    <?php foreach ($personal_care_products as $product): ?>
                        <div class="product-card">
                            <img src="<?php echo htmlspecialchars($product['image'] ?: '../assets/images/shop/default.png'); ?>" 
                                 alt="<?php echo htmlspecialchars($product['title']); ?>" 
                                 class="product-image"
                                 onerror="this.src='../assets/images/shop/default.png'">
                            <h6><?php echo htmlspecialchars($product['title']); ?></h6>
                            <p class="text-muted small"><?php echo htmlspecialchars($product['product_code']); ?></p>
                            <div class="mb-2">
                                <?php if ($product['sale_price']): ?>
                                    <span class="text-success fw-bold">Rs. <?php echo number_format($product['sale_price'], 0); ?></span>
                                    <span class="text-muted text-decoration-line-through small">Rs. <?php echo number_format($product['price'], 0); ?></span>
                                <?php else: ?>
                                    <span class="fw-bold">Rs. <?php echo number_format($product['price'], 0); ?></span>
                                <?php endif; ?>
                            </div>
                            <a href="edit-product.php?id=<?php echo $product['id']; ?>" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-edit me-1"></i>Edit
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Personal Care Products Yet</h5>
                    <p class="text-muted">Start by adding your first personal care product!</p>
                    <a href="add-product.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add First Product
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="card guide-card">
        <div class="card-header bg-secondary text-white">
            <h4 class="mb-0"><i class="fas fa-tools me-2"></i>Quick Actions</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <a href="add-product.php" class="btn btn-success w-100">
                        <i class="fas fa-plus mb-2 d-block"></i>
                        Add New Product
                    </a>
                </div>
                <div class="col-md-3 mb-3">
                    <a href="products.php" class="btn btn-info w-100">
                        <i class="fas fa-list mb-2 d-block"></i>
                        Manage Products
                    </a>
                </div>
                <div class="col-md-3 mb-3">
                    <a href="categories.php" class="btn btn-warning w-100">
                        <i class="fas fa-tags mb-2 d-block"></i>
                        Manage Categories
                    </a>
                </div>
                <div class="col-md-3 mb-3">
                    <a href="../personal-care.php" class="btn btn-primary w-100">
                        <i class="fas fa-eye mb-2 d-block"></i>
                        View Frontend
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Tips and Best Practices -->
    <div class="card guide-card">
        <div class="card-header bg-warning text-dark">
            <h4 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Tips & Best Practices</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fas fa-check-circle text-success me-2"></i>Product Naming</h6>
                    <ul class="text-muted">
                        <li>Use clear, descriptive titles</li>
                        <li>Include key benefits in the name</li>
                        <li>Use consistent naming convention</li>
                    </ul>
                    
                    <h6><i class="fas fa-image text-primary me-2"></i>Images</h6>
                    <ul class="text-muted">
                        <li>Use high-quality product photos</li>
                        <li>Consistent image dimensions</li>
                        <li>White or neutral backgrounds</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6><i class="fas fa-tag text-info me-2"></i>Pricing</h6>
                    <ul class="text-muted">
                        <li>Set competitive prices</li>
                        <li>Use sale prices for promotions</li>
                        <li>Round numbers work better</li>
                    </ul>
                    
                    <h6><i class="fas fa-list text-secondary me-2"></i>Descriptions</h6>
                    <ul class="text-muted">
                        <li>Highlight natural ingredients</li>
                        <li>Focus on benefits, not just features</li>
                        <li>Use bullet points for key benefits</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
