<?php
session_start();
require_once '../config/dbconfig.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Allow guest users to add to cart - no login required

    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('Invalid input data');
    }
    
    $product_id = intval($input['product_id'] ?? 0);
    $volume_id = !empty($input['volume_id']) ? intval($input['volume_id']) : null;
    $quantity = intval($input['quantity'] ?? 1);
    
    // Validate input
    if ($product_id <= 0) {
        throw new Exception('Invalid product ID');
    }
    
    if ($quantity <= 0 || $quantity > 10) {
        throw new Exception('Invalid quantity. Must be between 1 and 10');
    }
    
    // Check if product exists and is active
    $product = fetchSingle("SELECT * FROM products WHERE id = ? AND status = 'active'", [$product_id]);
    if (!$product) {
        throw new Exception('Product not found or inactive');
    }
    
    // Check volume if specified
    if ($volume_id) {
        $volume = fetchSingle("SELECT * FROM product_volumes WHERE id = ? AND product_id = ? AND status = 'active'", [$volume_id, $product_id]);
        if (!$volume) {
            throw new Exception('Product volume not found');
        }
        
        // Check stock
        if ($volume['stock_quantity'] < $quantity) {
            throw new Exception('Insufficient stock. Only ' . $volume['stock_quantity'] . ' items available');
        }
    }
    
    // Get or create session ID for cart
    $session_id = session_id();
    if (empty($session_id)) {
        session_regenerate_id();
        $session_id = session_id();
    }
    
    // Check if cart session exists, create if not
    $cart_session = fetchSingle("SELECT * FROM cart_sessions WHERE session_id = ?", [$session_id]);
    if (!$cart_session) {
        executeQuery("INSERT INTO cart_sessions (session_id, expires_at) VALUES (?, DATE_ADD(NOW(), INTERVAL 7 DAY))", [$session_id]);
    } else {
        // Update expiry
        executeQuery("UPDATE cart_sessions SET expires_at = DATE_ADD(NOW(), INTERVAL 7 DAY) WHERE session_id = ?", [$session_id]);
    }
    
    // Check if item already exists in cart
    $existing_item = fetchSingle("
        SELECT * FROM cart_items 
        WHERE session_id = ? AND product_id = ? AND volume_id " . ($volume_id ? "= ?" : "IS NULL"),
        $volume_id ? [$session_id, $product_id, $volume_id] : [$session_id, $product_id]
    );
    
    if ($existing_item) {
        // Update quantity
        $new_quantity = $existing_item['quantity'] + $quantity;
        
        // Check stock again for new quantity
        if ($volume_id && $volume['stock_quantity'] < $new_quantity) {
            throw new Exception('Cannot add more items. Only ' . $volume['stock_quantity'] . ' items available');
        }
        
        executeQuery("UPDATE cart_items SET quantity = ?, updated_at = NOW() WHERE id = ?", [$new_quantity, $existing_item['id']]);
    } else {
        // Add new item
        executeQuery("INSERT INTO cart_items (session_id, product_id, volume_id, quantity) VALUES (?, ?, ?, ?)", 
            [$session_id, $product_id, $volume_id, $quantity]);
    }
    
    // Get cart count
    $cart_count = fetchSingle("SELECT SUM(quantity) as total FROM cart_items WHERE session_id = ?", [$session_id])['total'] ?? 0;
    
    // Store cart count in session for quick access
    $_SESSION['cart_count'] = $cart_count;
    
    echo json_encode([
        'success' => true,
        'message' => 'Product added to cart successfully',
        'cart_count' => $cart_count
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
