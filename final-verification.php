<?php
require_once 'config/dbconfig.php';

echo "<h1>✅ Final SEO URL Verification</h1>";
echo "<p>Let's verify that all your product URLs are now SEO-friendly.</p>";

try {
    // Check database setup
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>📊 Database Status</h2>";
    
    $total_products = fetchSingle("SELECT COUNT(*) as count FROM products WHERE status = 'active'")['count'];
    $products_with_slugs = fetchSingle("SELECT COUNT(*) as count FROM products WHERE status = 'active' AND slug IS NOT NULL AND slug != ''")['count'];
    
    echo "<div style='display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin: 20px 0;'>";
    
    echo "<div style='background: white; padding: 20px; border-radius: 5px; text-align: center; border-left: 4px solid #007bff;'>";
    echo "<h3 style='margin: 0; color: #007bff;'>$total_products</h3>";
    echo "<p style='margin: 5px 0; color: #6c757d;'>Total Products</p>";
    echo "</div>";
    
    echo "<div style='background: white; padding: 20px; border-radius: 5px; text-align: center; border-left: 4px solid #28a745;'>";
    echo "<h3 style='margin: 0; color: #28a745;'>$products_with_slugs</h3>";
    echo "<p style='margin: 5px 0; color: #6c757d;'>With SEO URLs</p>";
    echo "</div>";
    
    $completion_rate = $total_products > 0 ? round(($products_with_slugs / $total_products) * 100, 1) : 0;
    echo "<div style='background: white; padding: 20px; border-radius: 5px; text-align: center; border-left: 4px solid #ffc107;'>";
    echo "<h3 style='margin: 0; color: #ffc107;'>$completion_rate%</h3>";
    echo "<p style='margin: 5px 0; color: #6c757d;'>Completion Rate</p>";
    echo "</div>";
    
    echo "</div>";
    
    if ($completion_rate == 100) {
        echo "<p style='color: green; font-size: 18px; text-align: center;'>🎉 All products have SEO-friendly URLs!</p>";
    } else {
        echo "<p style='color: orange; font-size: 18px; text-align: center;'>⚠ Some products still need slugs</p>";
    }
    echo "</div>";

    // Test actual URLs from each category
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🧪 Live URL Testing</h2>";
    echo "<p>Here are actual product URLs from each category page:</p>";
    
    $categories = [
        'Hair Care' => 'hair-care.php',
        'Skin Care' => 'skin-care.php', 
        'Health Care' => 'health-care.php',
        'Personal Care' => 'personal-care.php'
    ];
    
    foreach ($categories as $category_name => $page_file) {
        echo "<div style='background: white; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<h4>$category_name Products</h4>";
        
        // Get sample products for this category
        $sample_products = fetchAll("
            SELECT p.id, p.product_code, p.title, p.slug, p.price
            FROM products p
            LEFT JOIN product_categories pc ON p.id = pc.product_id
            LEFT JOIN categories c ON pc.category_id = c.id
            WHERE p.status = 'active' 
            AND (c.name = ? OR p.title LIKE ?)
            AND p.slug IS NOT NULL
            LIMIT 2
        ", [$category_name, "%$category_name%"]);
        
        if (empty($sample_products)) {
            echo "<p style='color: #6c757d;'>No products found for this category</p>";
        } else {
            foreach ($sample_products as $product) {
                $seo_url = "product-details.php?product=" . urlencode($product['slug']);
                $old_url = "product-details.php?product=" . urlencode($product['product_code']);
                
                echo "<div style='margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px;'>";
                echo "<p><strong>" . htmlspecialchars($product['title']) . "</strong></p>";
                echo "<p>✅ SEO URL: <a href='$seo_url' target='_blank' style='color: #28a745; font-family: monospace; font-weight: bold;'>$seo_url</a></p>";
                echo "<p>🔄 Old URL: <span style='color: #6c757d; font-family: monospace;'>$old_url</span></p>";
                echo "</div>";
            }
        }
        
        echo "<p style='text-align: center; margin: 15px 0;'>";
        echo "<a href='$page_file' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔗 Test $category_name Page</a>";
        echo "</p>";
        
        echo "</div>";
    }
    echo "</div>";

    // Check .htaccess file
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>⚙ .htaccess Status</h2>";
    
    if (file_exists('.htaccess')) {
        $htaccess_content = file_get_contents('.htaccess');
        $redirect_count = substr_count($htaccess_content, 'RewriteRule');
        
        echo "<p style='color: green;'>✅ .htaccess file exists</p>";
        echo "<p>📋 Contains $redirect_count redirect rules</p>";
        
        if (strpos($htaccess_content, 'SEO-Friendly Product URLs') !== false) {
            echo "<p style='color: green;'>✅ Contains SEO redirect rules</p>";
        } else {
            echo "<p style='color: orange;'>⚠ May need SEO redirect rules</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ .htaccess file not found</p>";
        echo "<p>Consider creating .htaccess for URL redirects</p>";
    }
    echo "</div>";

    // Instructions for testing
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🧪 How to Test</h2>";
    echo "<ol style='font-size: 16px; line-height: 1.8;'>";
    echo "<li><strong>Visit any category page</strong> using the links below</li>";
    echo "<li><strong>Click on a product</strong> - Check the URL in your browser</li>";
    echo "<li><strong>Look for product names in URL</strong> instead of PROD001, PROD002, etc.</li>";
    echo "<li><strong>Test Add to Cart</strong> - Should work normally</li>";
    echo "<li><strong>Test old URLs</strong> - Should redirect to new format</li>";
    echo "</ol>";
    echo "</div>";

    // Quick test links
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🔗 Quick Test Links</h2>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    $test_links = [
        'hair-care.php' => '💇 Hair Care',
        'skin-care.php' => '🧴 Skin Care', 
        'health-care.php' => '💊 Health Care',
        'personal-care.php' => '🧼 Personal Care',
        'index.php' => '🏠 Home Page'
    ];
    
    foreach ($test_links as $page => $title) {
        echo "<a href='$page' target='_blank' style='background: #28a745; color: white; padding: 15px; text-decoration: none; border-radius: 5px; text-align: center; display: block; font-weight: bold;'>$title</a>";
    }
    echo "</div>";
    echo "</div>";

    // Success/failure summary
    echo "<div style='background: " . ($completion_rate == 100 ? "#d4edda" : "#fff3cd") . "; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center;'>";
    
    if ($completion_rate == 100) {
        echo "<h2 style='color: #155724;'>🎉 SUCCESS!</h2>";
        echo "<p style='color: #155724; font-size: 18px;'>Your website now uses SEO-friendly URLs!</p>";
        echo "<p style='color: #155724;'>✅ All product links updated</p>";
        echo "<p style='color: #155724;'>✅ Database properly configured</p>";
        echo "<p style='color: #155724;'>✅ Cart functionality preserved</p>";
        echo "<p style='color: #155724;'>✅ Backward compatibility maintained</p>";
    } else {
        echo "<h2 style='color: #856404;'>⚠ PARTIAL SUCCESS</h2>";
        echo "<p style='color: #856404; font-size: 18px;'>Some products still need attention</p>";
        echo "<p><a href='fix-urls-now.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px;'>🔧 Complete Setup</a></p>";
    }
    echo "</div>";

    // Example URLs
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>📋 URL Format Examples</h2>";
    
    $example_products = fetchAll("SELECT product_code, title, slug FROM products WHERE status = 'active' AND slug IS NOT NULL LIMIT 3");
    
    echo "<div style='display: grid; grid-template-columns: 1fr; gap: 15px; margin: 20px 0;'>";
    foreach ($example_products as $product) {
        echo "<div style='background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff;'>";
        echo "<h4 style='margin: 0 0 10px 0;'>" . htmlspecialchars($product['title']) . "</h4>";
        echo "<p style='margin: 5px 0;'><strong>❌ Old:</strong> <span style='color: #dc3545; font-family: monospace;'>product-details.php?product=" . $product['product_code'] . "</span></p>";
        echo "<p style='margin: 5px 0;'><strong>✅ New:</strong> <span style='color: #28a745; font-family: monospace; font-weight: bold;'>product-details.php?product=" . $product['slug'] . "</span></p>";
        echo "</div>";
    }
    echo "</div>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p style='color: #721c24;'>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    line-height: 1.6; 
    background: #f8f9fa;
}
h1, h2, h3 { color: #333; }
a { text-decoration: none; }
a:hover { opacity: 0.9; }
ol li { margin: 8px 0; }
</style>
