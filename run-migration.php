<?php
// Run database migration to add new product content fields
require_once 'config/dbconfig.php';

echo "<h2>Database Migration: Adding Product Content Fields</h2>";

try {
    // Check if columns already exist
    $columns_check = fetchAll("SHOW COLUMNS FROM products LIKE 'key_benefits'");
    $sale_price_check = fetchAll("SHOW COLUMNS FROM products LIKE 'sale_price'");

    if (empty($columns_check) || empty($sale_price_check)) {
        echo "<p>Adding new columns to products table...</p>";

        // Add new columns if they don't exist
        if (empty($columns_check)) {
            executeQuery("ALTER TABLE products
                         ADD COLUMN key_benefits TEXT NULL AFTER description,
                         ADD COLUMN ingredients TEXT NULL AFTER key_benefits,
                         ADD COLUMN how_to_use TEXT NULL AFTER ingredients");
            echo "<p style='color: green;'>✓ Added content fields: key_benefits, ingredients, how_to_use</p>";
        }

        if (empty($sale_price_check)) {
            executeQuery("ALTER TABLE products
                         ADD COLUMN sale_price DECIMAL(10,2) NULL AFTER price");
            echo "<p style='color: green;'>✓ Added sale_price field</p>";
        }
        
        echo "<p style='color: green;'>✓ Successfully added new columns: key_benefits, ingredients, how_to_use, sale_price</p>";
        
        // Update existing products with sample content
        echo "<p>Adding sample content to existing products...</p>";
        
        executeQuery("UPDATE products SET 
            key_benefits = CASE 
                WHEN title LIKE '%hair%' OR title LIKE '%Hair%' THEN 
                    '• Promotes healthy hair growth\n• Reduces hair fall and breakage\n• Strengthens hair follicles\n• Adds natural shine and volume\n• Suitable for all hair types'
                WHEN title LIKE '%face%' OR title LIKE '%Face%' OR title LIKE '%skin%' OR title LIKE '%Skin%' THEN 
                    '• Deep cleanses and purifies skin\n• Removes dirt and impurities\n• Maintains skin''s natural moisture\n• Suitable for daily use\n• Gentle on sensitive skin'
                WHEN title LIKE '%syrup%' OR title LIKE '%Syrup%' OR title LIKE '%health%' OR title LIKE '%Health%' THEN 
                    '• Boosts immune system naturally\n• Provides essential nutrients\n• Safe for regular consumption\n• Made with natural ingredients\n• No artificial preservatives'
                ELSE 
                    '• Made with premium natural ingredients\n• Safe and effective formula\n• Dermatologically tested\n• Suitable for regular use\n• No harmful chemicals'
            END,
            ingredients = CASE 
                WHEN title LIKE '%hair%' OR title LIKE '%Hair%' THEN 
                    'Natural oils, Vitamin E, Biotin, Keratin proteins, Essential amino acids, Herbal extracts'
                WHEN title LIKE '%face%' OR title LIKE '%Face%' OR title LIKE '%skin%' OR title LIKE '%Skin%' THEN 
                    'Natural cleansing agents, Aloe vera extract, Vitamin C, Glycerin, Essential oils, Herbal extracts'
                WHEN title LIKE '%syrup%' OR title LIKE '%Syrup%' OR title LIKE '%health%' OR title LIKE '%Health%' THEN 
                    'Natural fruit extracts, Vitamins, Minerals, Herbal concentrates, Natural sweeteners'
                ELSE 
                    'Premium natural ingredients, Essential vitamins, Herbal extracts, Natural preservatives'
            END,
            how_to_use = CASE 
                WHEN title LIKE '%hair%' OR title LIKE '%Hair%' THEN 
                    '1. Apply a small amount to clean, damp hair\n2. Massage gently into scalp and hair\n3. Leave for 2-3 minutes\n4. Rinse thoroughly with water\n5. Use 2-3 times per week for best results'
                WHEN title LIKE '%face%' OR title LIKE '%Face%' OR title LIKE '%skin%' OR title LIKE '%Skin%' THEN 
                    '1. Wet your face with lukewarm water\n2. Apply a small amount to your palm\n3. Gently massage in circular motions\n4. Rinse thoroughly with water\n5. Pat dry with a clean towel\n6. Use twice daily for best results'
                WHEN title LIKE '%syrup%' OR title LIKE '%Syrup%' OR title LIKE '%health%' OR title LIKE '%Health%' THEN 
                    '1. Shake well before use\n2. Take 1-2 teaspoons daily\n3. Can be taken with water or directly\n4. Best taken after meals\n5. Store in a cool, dry place\n6. Consult physician for children under 2 years'
                ELSE 
                    '1. Read instructions carefully before use\n2. Apply as directed on packaging\n3. Use regularly for best results\n4. Store in a cool, dry place\n5. Discontinue if irritation occurs'
            END
            WHERE status = 'active'");
        
        echo "<p style='color: green;'>✓ Successfully added sample content to existing products</p>";
        
        // Show updated products
        $updated_products = fetchAll("SELECT id, title, 
                                     LEFT(key_benefits, 50) as benefits_preview,
                                     LEFT(ingredients, 50) as ingredients_preview,
                                     LEFT(how_to_use, 50) as usage_preview
                                     FROM products 
                                     WHERE status = 'active' 
                                     LIMIT 5");
        
        echo "<h3>Sample of Updated Products:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Benefits Preview</th><th>Ingredients Preview</th><th>Usage Preview</th></tr>";
        
        foreach ($updated_products as $product) {
            echo "<tr>";
            echo "<td>" . $product['id'] . "</td>";
            echo "<td>" . htmlspecialchars($product['title']) . "</td>";
            echo "<td>" . htmlspecialchars($product['benefits_preview']) . "...</td>";
            echo "<td>" . htmlspecialchars($product['ingredients_preview']) . "...</td>";
            echo "<td>" . htmlspecialchars($product['usage_preview']) . "...</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p style='color: orange;'>⚠ Columns already exist. Migration has already been run.</p>";
    }
    
    echo "<h3>Migration Complete!</h3>";
    echo "<p><strong>What's New:</strong></p>";
    echo "<ul>";
    echo "<li>✓ Product details page now shows 'Key Benefits & Ingredients', 'How to Use', and 'Reviews' tabs</li>";
    echo "<li>✓ Admin panel can now manage these new content fields</li>";
    echo "<li>✓ Existing products have been populated with sample content</li>";
    echo "<li>✓ New products can have custom content added from admin panel</li>";
    echo "</ul>";
    
    echo "<p><a href='product-details.php?product=PROD001' target='_blank'>View Sample Product Details</a></p>";
    echo "<p><a href='admin/products.php' target='_blank'>Manage Products in Admin Panel</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
