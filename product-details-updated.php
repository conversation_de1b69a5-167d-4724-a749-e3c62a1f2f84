<?php
session_start();
include('naturals.php');

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_to_cart'])) {
    $item = [
        'id' => $_POST['product_id'],
        'name' => $_POST['product_name'],
        'price' => $_POST['product_price'],
        'image' => $_POST['product_image'],
        'quantity' => (int)$_POST['quantity']
    ];

    if (!isset($_SESSION['cart'])) {
        $_SESSION['cart'] = [];
    }

    $found = false;
    foreach ($_SESSION['cart'] as &$cartItem) {
        if ($cartItem['id'] === $item['id']) {
            $cartItem['quantity'] += $item['quantity'];
            $found = true;
            break;
        }
    }
    if (!$found) {
        $_SESSION['cart'][] = $item;
    }

    header("Location: " . $_SERVER['REQUEST_URI']);
    exit;
}

$product_id = $_GET['product'] ?? 1;

// Fetch product details from database
$sql = "SELECT * FROM products WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $product_id);
$stmt->execute();
$result = $stmt->get_result();
$current = $result->fetch_assoc();

if (!$current) {
    die("Product not found.");
}

// Fetch product volumes
$sql_volumes = "SELECT * FROM product_volumes WHERE product_id = ?";
$stmt_volumes = $conn->prepare($sql_volumes);
$stmt_volumes->bind_param("i", $product_id);
$stmt_volumes->execute();
$volumes = $stmt_volumes->get_result()->fetch_all(MYSQLI_ASSOC);

// Fetch product specs
$sql_specs = "SELECT spec_key, spec_value FROM product_specs WHERE product_id = ?";
$stmt_specs = $conn->prepare($sql_specs);
$stmt_specs->bind_param("i", $product_id);
$stmt_specs->execute();
$specs = $stmt_specs->get_result()->fetch_all(MYSQLI_ASSOC);

// Fetch product reviews
$sql_reviews = "SELECT name, designation, image, rating, comment FROM product_reviews WHERE product_id = ?";
$stmt_reviews = $conn->prepare($sql_reviews);
$stmt_reviews->bind_param("i", $product_id);
$stmt_reviews->execute();
$reviews = $stmt_reviews->get_result()->fetch_all(MYSQLI_ASSOC);

include('header.php');
?>

<main class="main-content">

<!--== Start Product Details Area Wrapper ==-->
<section class="section-space">
    <div class="container">
        <div class="row product-details">
            <div class="col-lg-6">
                <div class="product-details-thumb">
                    <img src="<?= htmlspecialchars($current['image']) ?>" width="570" height="693" alt="Image">
                    <span class="flag-new">new</span>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="product-details-content">
                    <h5 class="product-details-collection"><?= htmlspecialchars($current['collection']) ?></h5>
                    <h3 class="product-details-title"><?= htmlspecialchars($current['title']) ?></h3>
                    <div class="product-details-review">
                        <div class="product-review-icon">
                            <?php
                            $fullStars = floor($reviews[0]['rating'] ?? 0);
                            $halfStar = (($reviews[0]['rating'] ?? 0) - $fullStars) >= 0.5;
                            for ($i = 0; $i < 5; $i++) {
                                if ($i < $fullStars) {
                                    echo '<i class="fa fa-star"></i>';
                                } elseif ($halfStar && $i == $fullStars) {
                                    echo '<i class="fa fa-star-half-o"></i>';
                                } else {
                                    echo '<i class="fa fa-star-o"></i>';
                                }
                            }
                            ?>
                        </div>
                        <button type="button" class="product-review-show"><?= count($reviews) ?> reviews</button>
                    </div>

                    <!-- Volume options -->
                    <div class="product-details-qty-list">
                        <?php foreach ($volumes as $index => $option): ?>
                        <div class="qty-list-check">
                            <input class="form-check-input" type="radio" name="flexRadioDefault" id="qtyList<?= $index + 1 ?>" <?= $index === 0 ? 'checked' : '' ?>>
                            <label class="form-check-label" for="qtyList<?= $index + 1 ?>">
                                <?= htmlspecialchars($option['size']) ?> <b><?= htmlspecialchars($option['price']) ?></b>
                                <?php if (!empty($option['offer'])): ?>
                                    <span class="extra-offer"><?= htmlspecialchars($option['offer']) ?></span>
                                <?php endif; ?>
                            </label>
                        </div>
                        <?php endforeach; ?>
                    </div>

                    <br><br><br>
                    <form method="POST" action="">
                        <div class="product-details-pro-qty">
                            <div class="pro-qty">
                                <input type="number" name="quantity" title="Quantity" value="1" min="1" required>
                            </div>
                        </div>

                        <input type="hidden" name="add_to_cart" value="1">
                        <input type="hidden" name="product_id" value="<?= htmlspecialchars($product_id) ?>">
                        <input type="hidden" name="product_name" value="<?= htmlspecialchars($current['title']) ?>">
                        <input type="hidden" name="product_price" value="<?= htmlspecialchars($current['price']) ?>">
                        <input type="hidden" name="product_image" value="<?= htmlspecialchars($current['image']) ?>">

                        <div class="product-details-action">
                            <h4 class="price"><?= htmlspecialchars($current['price']) ?></h4>
                            <div class="product-details-cart-wishlist">
                                <button type="button" class="btn-wishlist"><i class="fa fa-heart-o"></i></button>
                                <button type="submit" class="btn">Add to cart</button>
                            </div>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>
</section>
        <!--== End Product Details Area Wrapper ==-->

<?php
include('footer.php')
