-- Add product slug field for SEO-friendly URLs
-- This script adds a slug column and generates slugs from existing product titles

USE naturals;

-- Add slug column to products table
ALTER TABLE products 
ADD COLUMN slug VARCHAR(255) NULL AFTER product_code,
ADD UNIQUE KEY unique_slug (slug);

-- Function to create URL-friendly slugs
DELIMITER //
CREATE FUNCTION create_slug(input_text VARCHAR(255)) 
RETURNS VARCHAR(255)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result VARCHAR(255);
    
    -- Convert to lowercase
    SET result = LOWER(input_text);
    
    -- Replace spaces and special characters with hyphens
    SET result = REPLACE(result, ' ', '-');
    SET result = REPLACE(result, '&', 'and');
    SET result = REPLACE(result, '+', 'plus');
    SET result = REPLACE(result, '%', 'percent');
    SET result = REPLACE(result, '/', '-');
    SET result = REPLACE(result, '\\', '-');
    SET result = REPLACE(result, '(', '');
    SET result = REPLACE(result, ')', '');
    SET result = REPLACE(result, '[', '');
    SET result = REPLACE(result, ']', '');
    SET result = REPLACE(result, '{', '');
    SET result = REPLACE(result, '}', '');
    SET result = REPLACE(result, '"', '');
    SET result = REPLACE(result, "'", '');
    SET result = REPLACE(result, '`', '');
    SET result = REPLACE(result, '!', '');
    SET result = REPLACE(result, '@', '');
    SET result = REPLACE(result, '#', '');
    SET result = REPLACE(result, '$', '');
    SET result = REPLACE(result, '^', '');
    SET result = REPLACE(result, '*', '');
    SET result = REPLACE(result, '=', '');
    SET result = REPLACE(result, '|', '');
    SET result = REPLACE(result, ':', '');
    SET result = REPLACE(result, ';', '');
    SET result = REPLACE(result, '<', '');
    SET result = REPLACE(result, '>', '');
    SET result = REPLACE(result, '?', '');
    SET result = REPLACE(result, ',', '');
    SET result = REPLACE(result, '.', '');
    
    -- Remove multiple consecutive hyphens
    WHILE LOCATE('--', result) > 0 DO
        SET result = REPLACE(result, '--', '-');
    END WHILE;
    
    -- Remove leading and trailing hyphens
    SET result = TRIM(BOTH '-' FROM result);
    
    -- Limit length to 200 characters
    IF LENGTH(result) > 200 THEN
        SET result = LEFT(result, 200);
        -- Make sure we don't cut in the middle of a word
        SET result = SUBSTRING(result, 1, LOCATE('-', REVERSE(result)) - 1);
    END IF;
    
    RETURN result;
END//
DELIMITER ;

-- Generate slugs for existing products
UPDATE products 
SET slug = create_slug(title) 
WHERE slug IS NULL OR slug = '';

-- Handle duplicate slugs by appending numbers
UPDATE products p1 
JOIN (
    SELECT slug, COUNT(*) as cnt, MIN(id) as min_id
    FROM products 
    WHERE slug IS NOT NULL 
    GROUP BY slug 
    HAVING cnt > 1
) p2 ON p1.slug = p2.slug 
SET p1.slug = CONCAT(p1.slug, '-', p1.id)
WHERE p1.id != p2.min_id;

-- Show the results
SELECT id, product_code, title, slug 
FROM products 
ORDER BY id;

-- Drop the function as it's no longer needed
DROP FUNCTION create_slug;

-- Verify no duplicate slugs exist
SELECT slug, COUNT(*) as count 
FROM products 
WHERE slug IS NOT NULL 
GROUP BY slug 
HAVING count > 1;

-- Show final structure
DESCRIBE products;
