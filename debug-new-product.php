<?php
require_once 'config/dbconfig.php';

echo "<h1>Debug New Product Issue</h1>";

// Get all products to see what's in the database
echo "<h2>All Products in Database:</h2>";
$products = fetchAll("SELECT * FROM products ORDER BY id DESC LIMIT 10");

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Product Code</th><th>Title</th><th>Slug</th><th>Status</th><th>Price</th><th>Image</th></tr>";

foreach ($products as $product) {
    echo "<tr>";
    echo "<td>" . $product['id'] . "</td>";
    echo "<td>" . $product['product_code'] . "</td>";
    echo "<td>" . $product['title'] . "</td>";
    echo "<td>" . ($product['slug'] ?? 'NO SLUG') . "</td>";
    echo "<td>" . $product['status'] . "</td>";
    echo "<td>" . $product['price'] . "</td>";
    echo "<td>" . $product['image'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check the specific product that's causing issues
echo "<h2>Check Specific Product (MOSSFIRE MOSQUITO REPELLENT LOTION):</h2>";

// Try to find by different methods
$product_by_slug = fetchSingle("SELECT * FROM products WHERE slug = 'mossfire-mosquito-repellent-lotion'");
$product_by_code = fetchSingle("SELECT * FROM products WHERE product_code = 'PROD015'");
$product_by_title = fetchSingle("SELECT * FROM products WHERE title LIKE '%MOSSFIRE%LOTION%'");

echo "<h3>By Slug (mossfire-mosquito-repellent-lotion):</h3>";
if ($product_by_slug) {
    echo "<pre>" . print_r($product_by_slug, true) . "</pre>";
} else {
    echo "NOT FOUND";
}

echo "<h3>By Product Code (PROD015):</h3>";
if ($product_by_code) {
    echo "<pre>" . print_r($product_by_code, true) . "</pre>";
} else {
    echo "NOT FOUND";
}

echo "<h3>By Title (MOSSFIRE LOTION):</h3>";
if ($product_by_title) {
    echo "<pre>" . print_r($product_by_title, true) . "</pre>";
} else {
    echo "NOT FOUND";
}

// Check what URL parameters are being passed
echo "<h2>URL Analysis:</h2>";
echo "<p>Current URL: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>Query String: " . ($_SERVER['QUERY_STRING'] ?? 'NONE') . "</p>";

if (isset($_GET['product'])) {
    echo "<p>Product parameter: " . $_GET['product'] . "</p>";
    
    // Try to find this product
    $test_product = fetchSingle("SELECT * FROM products WHERE slug = ? OR product_code = ?", [$_GET['product'], $_GET['product']]);
    if ($test_product) {
        echo "<p style='color: green;'>✓ Product found with this parameter!</p>";
        echo "<pre>" . print_r($test_product, true) . "</pre>";
    } else {
        echo "<p style='color: red;'>✗ Product NOT found with this parameter!</p>";
    }
}

// Check if the product has a proper slug
echo "<h2>Products without slugs:</h2>";
$products_no_slug = fetchAll("SELECT * FROM products WHERE slug IS NULL OR slug = ''");
if ($products_no_slug) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Product Code</th><th>Title</th><th>Status</th></tr>";
    foreach ($products_no_slug as $product) {
        echo "<tr>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . $product['product_code'] . "</td>";
        echo "<td>" . $product['title'] . "</td>";
        echo "<td>" . $product['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>All products have slugs.</p>";
}
?>
