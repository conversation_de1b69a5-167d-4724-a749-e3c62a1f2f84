<?php
// Function to convert text to bullet points (same as in product-details.php)
function formatAsBulletPoints($text) {
    if (empty($text)) {
        return '';
    }
    
    // First try to split by line breaks
    $lines = preg_split('/\r\n|\r|\n/', trim($text));
    
    // If we only have one line, try to split by bullet characters
    if (count($lines) == 1) {
        // Split by bullet characters (•, *, -, etc.) followed by space
        $lines = preg_split('/[\s]*[•\*\-\→\►\▪\▫\‣\⁃]\s*/', $text);
    }
    
    // Filter out empty lines and trim whitespace
    $lines = array_filter(array_map('trim', $lines), function($line) {
        return !empty($line);
    });
    
    if (empty($lines)) {
        return '';
    }
    
    // If there's only one meaningful line, return as paragraph
    if (count($lines) == 1) {
        return '<p>' . htmlspecialchars($lines[0]) . '</p>';
    }
    
    // Convert to bullet points
    $html = '<ul class="product-bullet-list">';
    foreach ($lines as $line) {
        // Remove any remaining bullet characters at the start
        $line = preg_replace('/^[\s\-\*\•\→\►\▪\▫\‣\⁃]+\s*/', '', $line);
        $line = trim($line);
        if (!empty($line)) {
            $html .= '<li>' . htmlspecialchars($line) . '</li>';
        }
    }
    $html .= '</ul>';
    
    return $html;
}

// Your exact text from the admin panel (with line breaks)
$your_text_with_lines = "Deeply cleanses the skin, removing dirt and impurities.
Brightens dull skin and promotes a radiant glow.
Enriched with Vitamin C and Vitamin E for antioxidant protection.
Helps restore smooth, refreshed, and revitalized skin.";

// How it might be stored in database (with bullet characters)
$your_text_with_bullets = "• Deeply cleanses the skin, removing dirt and impurities • Brightens dull skin and promotes a radiant glow • Enriched with Vitamin C and Vitamin E for antioxidant protection • Helps restore smooth, refreshed, and revitalized skin • Gentle formula suitable for daily use • Hydrates and soothes for a soft, healthy complexion";

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Your Exact Text</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* Product Description Bullet List Styling */
        .product-bullet-list {
            list-style: none;
            padding-left: 0;
            margin: 15px 0;
        }

        .product-bullet-list li {
            position: relative;
            padding: 8px 0 8px 25px;
            line-height: 1.6;
            color: #555;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .product-bullet-list li:before {
            content: "•";
            position: absolute;
            left: 0;
            top: 8px;
            color: #FF6565;
            font-weight: bold;
            font-size: 16px;
        }

        .demo-box {
            background: #fff;
            border: 2px solid #007bff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .input-box {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-line;
            margin: 10px 0;
        }

        .output-box {
            background: #fff;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>🧪 Test Your Exact Text Format</h1>
        
        <div class="demo-box">
            <h3>📝 Format 1: Your Admin Panel Input (Line Breaks)</h3>
            <p><strong>What you entered in admin panel:</strong></p>
            <div class="input-box"><?php echo htmlspecialchars($your_text_with_lines); ?></div>
            
            <p><strong>Should display as:</strong></p>
            <div class="output-box">
                <?php echo formatAsBulletPoints($your_text_with_lines); ?>
            </div>
        </div>

        <div class="demo-box">
            <h3>🔸 Format 2: Database Storage (Bullet Characters)</h3>
            <p><strong>How it might be stored in database:</strong></p>
            <div class="input-box"><?php echo htmlspecialchars($your_text_with_bullets); ?></div>
            
            <p><strong>Should display as:</strong></p>
            <div class="output-box">
                <?php echo formatAsBulletPoints($your_text_with_bullets); ?>
            </div>
        </div>

        <div class="alert alert-info">
            <h4>🔍 Debug Steps:</h4>
            <ol>
                <li><strong>Run the debug script:</strong> <a href="debug-specific-product.php" target="_blank">debug-specific-product.php</a></li>
                <li><strong>Check your actual product data</strong> to see how it's stored</li>
                <li><strong>Look for red bullet points</strong> in the output above</li>
                <li><strong>If you don't see red bullets</strong>, there might be a CSS issue</li>
            </ol>
        </div>

        <div class="alert alert-warning">
            <h4>⚠️ If Still Not Working:</h4>
            <ul>
                <li><strong>Clear browser cache</strong> - Press Ctrl+F5</li>
                <li><strong>Check browser console</strong> - Press F12 for errors</li>
                <li><strong>Verify you're viewing the right product</strong></li>
                <li><strong>Make sure the product was saved</strong> after editing</li>
            </ul>
        </div>

        <div class="alert alert-success">
            <h4>✅ Expected Result:</h4>
            <p>Each line should appear as a separate bullet point with a red bullet (•) and proper spacing, like this:</p>
            <ul class="product-bullet-list">
                <li>Deeply cleanses the skin, removing dirt and impurities</li>
                <li>Brightens dull skin and promotes a radiant glow</li>
                <li>Enriched with Vitamin C and Vitamin E for antioxidant protection</li>
                <li>Helps restore smooth, refreshed, and revitalized skin</li>
            </ul>
        </div>

        <div class="demo-box">
            <h3>🔗 Quick Links:</h3>
            <div class="row">
                <div class="col-md-4">
                    <a href="debug-specific-product.php" class="btn btn-primary" target="_blank">
                        🔍 Debug Your Products
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="admin/products.php" class="btn btn-success" target="_blank">
                        ⚙️ Admin Panel
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="hair-care.php" class="btn btn-info" target="_blank">
                        👀 View Products
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
