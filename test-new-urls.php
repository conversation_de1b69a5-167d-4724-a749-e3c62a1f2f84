<?php
require_once 'config/dbconfig.php';

echo "<h1>🧪 Test New SEO-Friendly URLs</h1>";
echo "<p>Let's verify that your new product URLs are working correctly.</p>";

try {
    // Get products with slugs
    $products = fetchAll("
        SELECT id, product_code, title, slug, price, image 
        FROM products 
        WHERE status = 'active' AND slug IS NOT NULL 
        ORDER BY title 
        LIMIT 10
    ");
    
    if (empty($products)) {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px;'>";
        echo "<h3>❌ No Products with Slugs Found</h3>";
        echo "<p>Please run the <a href='fix-urls-now.php'>fix-urls-now.php</a> script first.</p>";
        echo "</div>";
        exit;
    }

    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>✅ Found " . count($products) . " Products with SEO-Friendly URLs</h2>";
    echo "<p>Click on any product link below to test the new URL format.</p>";
    echo "</div>";

    // Display products for testing
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; margin: 20px 0;'>";
    
    foreach ($products as $product) {
        $slug_url = "product-details.php?product=" . urlencode($product['slug']);
        $code_url = "product-details.php?product=" . urlencode($product['product_code']);
        
        echo "<div style='border: 1px solid #ddd; border-radius: 10px; padding: 20px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1);'>";
        
        // Product image
        echo "<div style='text-align: center; margin-bottom: 15px;'>";
        if ($product['image']) {
            echo "<img src='" . htmlspecialchars($product['image']) . "' alt='" . htmlspecialchars($product['title']) . "' style='max-width: 100%; height: 150px; object-fit: cover; border-radius: 5px;'>";
        } else {
            echo "<div style='width: 100%; height: 150px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; border-radius: 5px; color: #6c757d;'>No Image</div>";
        }
        echo "</div>";
        
        // Product title
        echo "<h3 style='margin: 15px 0; text-align: center; color: #333;'>" . htmlspecialchars($product['title']) . "</h3>";
        
        // Price
        echo "<p style='text-align: center; font-size: 18px; color: #28a745; font-weight: bold; margin: 10px 0;'>Rs. " . number_format($product['price'], 0) . "</p>";
        
        // URL comparison
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<p style='margin: 5px 0;'><strong>🎯 New SEO URL:</strong></p>";
        echo "<p style='font-family: monospace; font-size: 12px; word-break: break-all; color: #28a745; background: white; padding: 8px; border-radius: 3px; margin: 5px 0;'>" . $slug_url . "</p>";
        
        echo "<p style='margin: 5px 0;'><strong>🔄 Old URL:</strong></p>";
        echo "<p style='font-family: monospace; font-size: 12px; word-break: break-all; color: #6c757d; background: white; padding: 8px; border-radius: 3px; margin: 5px 0;'>" . $code_url . "</p>";
        echo "</div>";
        
        // Test buttons
        echo "<div style='text-align: center; margin: 15px 0;'>";
        echo "<a href='$slug_url' target='_blank' style='background: #28a745; color: white; padding: 12px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block; font-weight: bold;'>🎯 Test New URL</a>";
        echo "<a href='$code_url' target='_blank' style='background: #6c757d; color: white; padding: 12px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>🔄 Test Old URL</a>";
        echo "</div>";
        
        echo "</div>";
    }
    
    echo "</div>";

    // Instructions
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🧪 Testing Instructions</h2>";
    echo "<ol style='font-size: 16px; line-height: 1.8;'>";
    echo "<li><strong>Click 'Test New URL'</strong> - This should open the product page with the SEO-friendly URL</li>";
    echo "<li><strong>Check the browser address bar</strong> - You should see the product name in the URL instead of PROD001, PROD002, etc.</li>";
    echo "<li><strong>Test the 'Add to Cart' button</strong> - Make sure it still works properly</li>";
    echo "<li><strong>Test 'Test Old URL'</strong> - This should still work for backward compatibility</li>";
    echo "<li><strong>Check product category pages</strong> - All links should now use the new format</li>";
    echo "</ol>";
    echo "</div>";

    // Category page links
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>📱 Test Category Pages</h2>";
    echo "<p>Visit these category pages to see all product links using the new SEO-friendly format:</p>";
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    $categories = [
        'hair-care.php' => '💇 Hair Care Products',
        'skin-care.php' => '🧴 Skin Care Products',
        'health-care.php' => '💊 Health Care Products',
        'personal-care.php' => '🧼 Personal Care Products',
        'index.php' => '🏠 Home Page'
    ];
    
    foreach ($categories as $page => $title) {
        echo "<a href='$page' target='_blank' style='background: #007bff; color: white; padding: 15px; text-decoration: none; border-radius: 5px; text-align: center; display: block; font-weight: bold;'>$title</a>";
    }
    echo "</div>";
    echo "</div>";

    // Success indicators
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>✅ What to Look For</h2>";
    echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;'>";
    
    echo "<div>";
    echo "<h4 style='color: #155724;'>✅ Success Indicators:</h4>";
    echo "<ul style='color: #155724;'>";
    echo "<li>URLs show product names (e.g., 'face-wash-vitamin-c')</li>";
    echo "<li>Product pages load correctly</li>";
    echo "<li>Add to cart functionality works</li>";
    echo "<li>Cart count updates in header</li>";
    echo "<li>Old URLs still work (backward compatibility)</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div>";
    echo "<h4 style='color: #721c24;'>❌ Issues to Watch For:</h4>";
    echo "<ul style='color: #721c24;'>";
    echo "<li>URLs still showing PROD001, PROD002, etc.</li>";
    echo "<li>Product pages showing 'Product not found'</li>";
    echo "<li>Add to cart not working</li>";
    echo "<li>Links leading to wrong products</li>";
    echo "<li>Database connection errors</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";

    // Quick stats
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>📊 Implementation Stats</h2>";
    
    $total_products = fetchSingle("SELECT COUNT(*) as count FROM products WHERE status = 'active'")['count'];
    $products_with_slugs = fetchSingle("SELECT COUNT(*) as count FROM products WHERE status = 'active' AND slug IS NOT NULL AND slug != ''")['count'];
    $completion_rate = $total_products > 0 ? round(($products_with_slugs / $total_products) * 100, 1) : 0;
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;'>";
    
    echo "<div style='background: white; padding: 20px; border-radius: 5px; text-align: center; border-left: 4px solid #007bff;'>";
    echo "<h3 style='margin: 0; color: #007bff;'>$total_products</h3>";
    echo "<p style='margin: 5px 0; color: #6c757d;'>Total Products</p>";
    echo "</div>";
    
    echo "<div style='background: white; padding: 20px; border-radius: 5px; text-align: center; border-left: 4px solid #28a745;'>";
    echo "<h3 style='margin: 0; color: #28a745;'>$products_with_slugs</h3>";
    echo "<p style='margin: 5px 0; color: #6c757d;'>With SEO URLs</p>";
    echo "</div>";
    
    echo "<div style='background: white; padding: 20px; border-radius: 5px; text-align: center; border-left: 4px solid #ffc107;'>";
    echo "<h3 style='margin: 0; color: #ffc107;'>$completion_rate%</h3>";
    echo "<p style='margin: 5px 0; color: #6c757d;'>Completion Rate</p>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p style='color: #721c24;'>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    line-height: 1.6; 
    background: #f8f9fa;
}
h1, h2, h3 { color: #333; }
a { text-decoration: none; }
a:hover { opacity: 0.9; text-decoration: underline; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
</style>
