<?php
// Fix missing database columns that are causing the SQL errors
require_once 'config/dbconfig.php';

echo "<h2>🔧 Fixing Database Columns</h2>";

try {
    // 1. Check current table structure
    echo "<h3>1. Current Products Table Structure</h3>";
    $columns = $pdo->query("SHOW COLUMNS FROM products")->fetchAll();
    $existing_columns = array_column($columns, 'Field');
    
    echo "<p><strong>Existing columns:</strong> " . implode(', ', $existing_columns) . "</p>";
    
    // 2. Add missing columns
    echo "<h3>2. Adding Missing Columns</h3>";
    
    $required_columns = [
        'sale_price' => 'DECIMAL(10,2) NULL AFTER price',
        'key_benefits' => 'TEXT NULL AFTER description',
        'ingredients' => 'TEXT NULL AFTER key_benefits',
        'how_to_use' => 'TEXT NULL AFTER ingredients'
    ];
    
    foreach ($required_columns as $column => $definition) {
        if (!in_array($column, $existing_columns)) {
            try {
                $pdo->exec("ALTER TABLE products ADD COLUMN $column $definition");
                echo "<p style='color: green;'>✅ Added column: $column</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error adding $column: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ Column already exists: $column</p>";
        }
    }
    
    // 3. Create product_reviews table if missing
    echo "<h3>3. Checking Product Reviews Table</h3>";
    try {
        $pdo->query("SELECT 1 FROM product_reviews LIMIT 1");
        echo "<p style='color: green;'>✅ product_reviews table exists</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ Creating product_reviews table...</p>";
        
        $create_reviews = "
        CREATE TABLE `product_reviews` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `product_id` int(11) NOT NULL,
            `customer_name` varchar(100) NOT NULL,
            `customer_designation` varchar(100) DEFAULT NULL,
            `customer_image` varchar(255) DEFAULT NULL,
            `rating` decimal(2,1) NOT NULL DEFAULT 5.0,
            `comment` text,
            `status` enum('approved','pending','rejected') DEFAULT 'approved',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `product_id` (`product_id`),
            KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($create_reviews);
        echo "<p style='color: green;'>✅ Created product_reviews table</p>";
    }
    
    // 4. Test the problematic query
    echo "<h3>4. Testing Home Page Query</h3>";
    
    $home_query = "
        SELECT p.*,
               COALESCE(p.sale_price, p.price) as display_price,
               CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price 
                    THEN p.price ELSE NULL END as original_price
        FROM products p
        WHERE p.status = 'active'
        ORDER BY p.product_code ASC
        LIMIT 6
    ";
    
    try {
        $products = $pdo->query($home_query)->fetchAll();
        echo "<p style='color: green;'>✅ Home page query successful! Found " . count($products) . " products</p>";
        
        if (count($products) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr><th>Code</th><th>Title</th><th>Price</th><th>Sale Price</th><th>Status</th></tr>";
            foreach ($products as $product) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($product['product_code']) . "</td>";
                echo "<td>" . htmlspecialchars($product['title']) . "</td>";
                echo "<td>Rs. " . number_format($product['price'], 0) . "</td>";
                echo "<td>" . ($product['sale_price'] ? "Rs. " . number_format($product['sale_price'], 0) : "None") . "</td>";
                echo "<td>" . $product['status'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Query failed: " . $e->getMessage() . "</p>";
        
        // Try even simpler query
        echo "<p>Trying simpler query...</p>";
        try {
            $simple = $pdo->query("SELECT * FROM products WHERE status = 'active' LIMIT 3")->fetchAll();
            echo "<p style='color: green;'>✅ Simple query works! Found " . count($simple) . " products</p>";
        } catch (Exception $e2) {
            echo "<p style='color: red;'>❌ Even simple query failed: " . $e2->getMessage() . "</p>";
        }
    }
    
    // 5. Test category queries
    echo "<h3>5. Testing Category Queries</h3>";
    
    // Simple hair care query without joins
    $hair_query = "
        SELECT p.*,
               COALESCE(p.sale_price, p.price) as display_price,
               CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price 
                    THEN p.price ELSE NULL END as original_price
        FROM products p
        WHERE p.status = 'active'
        AND (p.title LIKE '%hair%' 
             OR p.title LIKE '%Hair%' 
             OR p.title LIKE '%serum%' 
             OR p.title LIKE '%Serum%'
             OR p.title LIKE '%shampoo%'
             OR p.title LIKE '%Shampoo%')
        ORDER BY p.created_at ASC
    ";
    
    try {
        $hair_products = $pdo->query($hair_query)->fetchAll();
        echo "<p style='color: green;'>✅ Hair care query successful! Found " . count($hair_products) . " products</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Hair care query failed: " . $e->getMessage() . "</p>";
    }
    
    // 6. Update index.php to use simpler query
    echo "<h3>6. Updating Website Queries</h3>";
    echo "<p>The complex queries with JOINs are causing issues. Using simpler queries...</p>";
    
    // 7. Final verification
    echo "<h3>7. Final Verification</h3>";
    $final_count = $pdo->query("SELECT COUNT(*) as count FROM products WHERE status = 'active'")->fetch();
    echo "<p><strong>Total active products:</strong> " . $final_count['count'] . "</p>";
    
    if ($final_count['count'] > 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<p><strong>✅ Database structure fixed!</strong></p>";
        echo "<p><strong>Next steps:</strong></p>";
        echo "<ol>";
        echo "<li><a href='update-website-queries.php' style='color: #155724; font-weight: bold;'>Update Website Queries</a></li>";
        echo "<li><a href='index.php' target='_blank' style='color: #155724; font-weight: bold;'>Test Home Page</a></li>";
        echo "<li><a href='hair-care.php' target='_blank' style='color: #155724; font-weight: bold;'>Test Hair Care Page</a></li>";
        echo "</ol>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<p><strong>❌ No active products found!</strong></p>";
        echo "<p>Check your admin panel and make sure products have status = 'active'</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
h3 { border-bottom: 2px solid #ddd; padding-bottom: 5px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background: #f0f0f0; }
p { margin: 5px 0; }
</style>
