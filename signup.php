<?php
session_start();
require_once 'config/dbconfig.php';

// Redirect if already logged in
if (isset($_SESSION['customer_logged_in']) && $_SESSION['customer_logged_in'] === true) {
    header('Location: index.php');
    exit;
}

$error_message = '';
$success_message = '';

// Handle signup form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'signup') {
    $first_name = trim($_POST['first_name'] ?? '');
    $last_name = trim($_POST['last_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $agree_terms = isset($_POST['agree_terms']);
    
    // Validation
    if (empty($first_name) || empty($last_name) || empty($email) || empty($password)) {
        $error_message = 'Please fill in all required fields.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'Please enter a valid email address.';
    } elseif (strlen($password) < 6) {
        $error_message = 'Password must be at least 6 characters long.';
    } elseif ($password !== $confirm_password) {
        $error_message = 'Passwords do not match.';
    } elseif (!$agree_terms) {
        $error_message = 'Please agree to the terms and conditions.';
    } else {
        try {
            // Check if email already exists
            $existing_customer = fetchSingle("SELECT id FROM customers WHERE email = ?", [$email]);
            
            if ($existing_customer) {
                $error_message = 'An account with this email already exists. Please login instead.';
            } else {
                // Create new customer
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                $customer_id = insertData("
                    INSERT INTO customers (first_name, last_name, email, phone, password, status, email_verified) 
                    VALUES (?, ?, ?, ?, ?, 'active', 1)
                ", [$first_name, $last_name, $email, $phone, $hashed_password]);
                
                if ($customer_id) {
                    // Auto-login the new customer
                    $_SESSION['customer_logged_in'] = true;
                    $_SESSION['customer_id'] = $customer_id;
                    $_SESSION['customer_name'] = $first_name . ' ' . $last_name;
                    $_SESSION['customer_email'] = $email;
                    
                    // Redirect to intended page or homepage
                    $redirect_url = $_SESSION['redirect_after_login'] ?? 'index.php';
                    unset($_SESSION['redirect_after_login']);
                    
                    $_SESSION['signup_success'] = true;
                    header('Location: ' . $redirect_url);
                    exit;
                } else {
                    $error_message = 'Failed to create account. Please try again.';
                }
            }
        } catch (Exception $e) {
            $error_message = 'Registration failed. Please try again.';
            error_log("Signup error: " . $e->getMessage());
        }
    }
}

include 'header.php';
?>

<br><br><br>
<main class="main-content">
    <!--== Start Page Header Area Wrapper ==-->
    <section class="page-header-area pt-10 pb-9" data-bg-color="#FFF3DA">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <div class="page-header-st3-content text-center text-md-start">
                        <ol class="breadcrumb justify-content-center justify-content-md-start">
                            <li class="breadcrumb-item"><a class="text-dark" href="index.php">Home</a></li>
                            <li class="breadcrumb-item active text-dark" aria-current="page">Sign Up</li>
                        </ol>
                        <h2 class="page-header-title">Create Account</h2>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!--== End Page Header Area Wrapper ==-->

    <!--== Start Signup Area ==-->
    <section class="section-space">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-7 col-md-9">
                    <div class="signup-form-wrapper">
                        <div class="card shadow-lg border-0">
                            <div class="card-body p-5">
                                <div class="text-center mb-4">
                                    <h3 class="card-title">Join Dr. Zia Naturals</h3>
                                    <p class="text-muted">Create your account to start shopping natural products</p>
                                </div>

                                <?php if ($error_message): ?>
                                    <div class="alert alert-danger alert-dismissible fade show">
                                        <i class="fa fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                <?php endif; ?>

                                <form method="POST" class="signup-form">
                                    <input type="hidden" name="action" value="signup">
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="first_name" class="form-label">First Name *</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa fa-user"></i></span>
                                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                                       value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>" 
                                                       placeholder="First name" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="last_name" class="form-label">Last Name *</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa fa-user"></i></span>
                                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                                       value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>" 
                                                       placeholder="Last name" required>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email Address *</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fa fa-envelope"></i></span>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                                   placeholder="Enter your email" required>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="phone" class="form-label">Phone Number</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fa fa-phone"></i></span>
                                            <input type="tel" class="form-control" id="phone" name="phone" 
                                                   value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>" 
                                                   placeholder="Enter your phone number">
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="password" class="form-label">Password *</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa fa-lock"></i></span>
                                                <input type="password" class="form-control" id="password" name="password" 
                                                       placeholder="Create password" required>
                                                <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('password')">
                                                    <i class="fa fa-eye" id="password-toggle-icon"></i>
                                                </button>
                                            </div>
                                            <small class="text-muted">Minimum 6 characters</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="confirm_password" class="form-label">Confirm Password *</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa fa-lock"></i></span>
                                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                                       placeholder="Confirm password" required>
                                                <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('confirm_password')">
                                                    <i class="fa fa-eye" id="confirm-password-toggle-icon"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="agree_terms" name="agree_terms" required>
                                        <label class="form-check-label" for="agree_terms">
                                            I agree to the <a href="terms.php" target="_blank">Terms & Conditions</a> 
                                            and <a href="privacy.php" target="_blank">Privacy Policy</a>
                                        </label>
                                    </div>

                                    <button type="submit" class="btn btn-primary w-100 mb-3">
                                        <i class="fa fa-user-plus"></i> Create Account
                                    </button>
                                </form>

                                <div class="text-center">
                                    <p class="mb-0">
                                        Already have an account? 
                                        <a href="login.php" class="text-decoration-none fw-bold">Sign In</a>
                                    </p>
                                </div>

                                <hr class="my-4">

                                <div class="text-center">
                                    <p class="text-muted small">
                                        <i class="fa fa-shield"></i> Your information is secure and protected
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!--== End Signup Area ==-->
</main>

<?php include 'footer.php'; ?>

<script>
function togglePassword(fieldId) {
    const passwordInput = document.getElementById(fieldId);
    const toggleIcon = document.getElementById(fieldId + '-toggle-icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fa fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fa fa-eye';
    }
}

// Password strength indicator
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strengthIndicator = document.getElementById('password-strength');
    
    if (password.length < 6) {
        this.style.borderColor = '#dc3545';
    } else if (password.length < 8) {
        this.style.borderColor = '#ffc107';
    } else {
        this.style.borderColor = '#28a745';
    }
});

// Confirm password validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (confirmPassword && password !== confirmPassword) {
        this.style.borderColor = '#dc3545';
    } else if (confirmPassword) {
        this.style.borderColor = '#28a745';
    }
});

// Auto-focus on first name field
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('first_name').focus();
});
</script>

<style>
.signup-form-wrapper {
    margin: 40px 0;
}

.card {
    border-radius: 15px;
}

.input-group-text {
    background-color: #f8f9fa;
    border-right: none;
}

.form-control {
    border-left: none;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 12px;
    font-weight: 600;
    border-radius: 8px;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

.alert {
    border-radius: 8px;
}

@media (max-width: 768px) {
    .card-body {
        padding: 2rem !important;
    }
}
</style>
