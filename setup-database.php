<?php
// Quick database setup script
require_once 'config/dbconfig.php';

echo "<h2>🚀 Quick Database Setup</h2>";

try {
    // 1. Create products table if it doesn't exist
    echo "<h3>1. Creating Products Table</h3>";
    
    $create_products = "
    CREATE TABLE IF NOT EXISTS `products` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `product_code` varchar(50) NOT NULL UNIQUE,
        `title` varchar(255) NOT NULL,
        `collection` varchar(100) DEFAULT NULL,
        `description` text,
        `key_benefits` text,
        `ingredients` text,
        `how_to_use` text,
        `price` decimal(10,2) NOT NULL,
        `sale_price` decimal(10,2) DEFAULT NULL,
        `image` varchar(255) DEFAULT NULL,
        `shipping_info` text,
        `weight` varchar(50) DEFAULT NULL,
        `dimensions` varchar(100) DEFAULT NULL,
        `materials` text,
        `other_info` text,
        `status` enum('active','inactive') DEFAULT 'active',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `product_code` (`product_code`),
        KEY `status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($create_products);
    echo "<p style='color: green;'>✅ Products table created/verified</p>";
    
    // 2. Create categories table
    echo "<h3>2. Creating Categories Table</h3>";
    
    $create_categories = "
    CREATE TABLE IF NOT EXISTS `categories` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `description` text,
        `status` enum('active','inactive') DEFAULT 'active',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($create_categories);
    echo "<p style='color: green;'>✅ Categories table created/verified</p>";
    
    // 3. Create product_categories table
    echo "<h3>3. Creating Product-Categories Relationship Table</h3>";
    
    $create_product_categories = "
    CREATE TABLE IF NOT EXISTS `product_categories` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `product_id` int(11) NOT NULL,
        `category_id` int(11) NOT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `product_category` (`product_id`, `category_id`),
        KEY `product_id` (`product_id`),
        KEY `category_id` (`category_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($create_product_categories);
    echo "<p style='color: green;'>✅ Product-Categories table created/verified</p>";
    
    // 4. Create product_reviews table
    echo "<h3>4. Creating Product Reviews Table</h3>";
    
    $create_reviews = "
    CREATE TABLE IF NOT EXISTS `product_reviews` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `product_id` int(11) NOT NULL,
        `customer_name` varchar(100) NOT NULL,
        `customer_designation` varchar(100) DEFAULT NULL,
        `customer_image` varchar(255) DEFAULT NULL,
        `rating` decimal(2,1) NOT NULL CHECK (rating >= 1 AND rating <= 5),
        `comment` text,
        `status` enum('approved','pending','rejected') DEFAULT 'pending',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `product_id` (`product_id`),
        KEY `status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($create_reviews);
    echo "<p style='color: green;'>✅ Product Reviews table created/verified</p>";
    
    // 5. Insert default categories
    echo "<h3>5. Adding Default Categories</h3>";
    
    $categories = [
        ['Hair Care', 'Products for hair growth, hair fall control, and overall hair health'],
        ['Skin Care', 'Facial cleansers, moisturizers, and skincare products for healthy skin'],
        ['Health Care', 'Natural health and wellness products for overall well-being']
    ];
    
    foreach ($categories as $cat) {
        $existing = $pdo->prepare("SELECT id FROM categories WHERE name = ?");
        $existing->execute([$cat[0]]);
        
        if (!$existing->fetch()) {
            $stmt = $pdo->prepare("INSERT INTO categories (name, description, status) VALUES (?, ?, 'active')");
            $stmt->execute($cat);
            echo "<p>✅ Added category: " . $cat[0] . "</p>";
        } else {
            echo "<p>✅ Category already exists: " . $cat[0] . "</p>";
        }
    }
    
    // 6. Insert sample products
    echo "<h3>6. Adding Sample Products</h3>";
    
    $sample_products = [
        [
            'PROD001', 'Hair Growth Serum', 'Premium Collection',
            'Our premium hair growth serum is formulated with natural ingredients to promote healthy hair growth and reduce hair fall. Suitable for all hair types.',
            '• Promotes healthy hair growth\n• Reduces hair fall and breakage\n• Strengthens hair follicles\n• Adds natural shine and volume\n• Suitable for all hair types',
            'Natural oils, Vitamin E, Biotin, Keratin proteins, Essential amino acids, Herbal extracts',
            '1. Apply a small amount to clean, damp hair\n2. Massage gently into scalp and hair\n3. Leave for 2-3 minutes\n4. Rinse thoroughly with water\n5. Use 2-3 times per week for best results',
            1499.00, 1299.00, 'assets/images/shop/1.png'
        ],
        [
            'PROD002', 'Face Wash | Vitamin C', 'Premium Collection',
            'Gentle vitamin C face wash that cleanses and brightens your skin naturally.',
            '• Deep cleanses and purifies skin\n• Removes dirt and impurities\n• Maintains skin\'s natural moisture\n• Suitable for daily use\n• Gentle on sensitive skin',
            'Natural cleansing agents, Aloe vera extract, Vitamin C, Glycerin, Essential oils, Herbal extracts',
            '1. Wet your face with lukewarm water\n2. Apply a small amount to your palm\n3. Gently massage in circular motions\n4. Rinse thoroughly with water\n5. Pat dry with a clean towel\n6. Use twice daily for best results',
            699.00, null, 'assets/images/shop/2.png'
        ],
        [
            'PROD003', 'Kids Health Syrup', 'Premium Collection',
            'Natural health syrup specially formulated for children\'s wellness.',
            '• Boosts immune system naturally\n• Provides essential nutrients\n• Safe for regular consumption\n• Made with natural ingredients\n• No artificial preservatives',
            'Natural fruit extracts, Vitamins, Minerals, Herbal concentrates, Natural sweeteners',
            '1. Shake well before use\n2. Take 1-2 teaspoons daily\n3. Can be taken with water or directly\n4. Best taken after meals\n5. Store in a cool, dry place\n6. Consult physician for children under 2 years',
            899.00, 799.00, 'assets/images/shop/3.png'
        ]
    ];
    
    foreach ($sample_products as $product) {
        $existing = $pdo->prepare("SELECT id FROM products WHERE product_code = ?");
        $existing->execute([$product[0]]);
        
        if (!$existing->fetch()) {
            $stmt = $pdo->prepare("
                INSERT INTO products (product_code, title, collection, description, key_benefits, ingredients, how_to_use, price, sale_price, image, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
            ");
            $stmt->execute($product);
            echo "<p>✅ Added product: " . $product[1] . "</p>";
        } else {
            echo "<p>✅ Product already exists: " . $product[1] . "</p>";
        }
    }
    
    // 7. Assign products to categories
    echo "<h3>7. Assigning Products to Categories</h3>";
    
    $products = $pdo->query("SELECT * FROM products WHERE status = 'active'")->fetchAll();
    $categories_map = [];
    
    $cats = $pdo->query("SELECT * FROM categories WHERE status = 'active'")->fetchAll();
    foreach ($cats as $cat) {
        $categories_map[strtolower($cat['name'])] = $cat['id'];
    }
    
    foreach ($products as $product) {
        $title = strtolower($product['title']);
        $assigned = false;
        
        // Hair Care
        if (strpos($title, 'hair') !== false || strpos($title, 'serum') !== false) {
            if (isset($categories_map['hair care'])) {
                $stmt = $pdo->prepare("INSERT IGNORE INTO product_categories (product_id, category_id) VALUES (?, ?)");
                $stmt->execute([$product['id'], $categories_map['hair care']]);
                $assigned = true;
            }
        }
        
        // Skin Care
        if (strpos($title, 'face') !== false || strpos($title, 'wash') !== false) {
            if (isset($categories_map['skin care'])) {
                $stmt = $pdo->prepare("INSERT IGNORE INTO product_categories (product_id, category_id) VALUES (?, ?)");
                $stmt->execute([$product['id'], $categories_map['skin care']]);
                $assigned = true;
            }
        }
        
        // Health Care
        if (strpos($title, 'syrup') !== false || strpos($title, 'health') !== false || strpos($title, 'kids') !== false) {
            if (isset($categories_map['health care'])) {
                $stmt = $pdo->prepare("INSERT IGNORE INTO product_categories (product_id, category_id) VALUES (?, ?)");
                $stmt->execute([$product['id'], $categories_map['health care']]);
                $assigned = true;
            }
        }
        
        if ($assigned) {
            echo "<p>✅ Assigned categories for: " . $product['title'] . "</p>";
        }
    }
    
    echo "<h3>8. 🎉 Setup Complete!</h3>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p><strong>✅ Database setup completed successfully!</strong></p>";
    echo "<p><strong>Test your website now:</strong></p>";
    echo "<ul>";
    echo "<li><a href='index.php' target='_blank' style='color: #155724; font-weight: bold;'>🏠 Home Page</a></li>";
    echo "<li><a href='hair-care.php' target='_blank' style='color: #155724; font-weight: bold;'>💇 Hair Care Products</a></li>";
    echo "<li><a href='skin-care.php' target='_blank' style='color: #155724; font-weight: bold;'>🧴 Skin Care Products</a></li>";
    echo "<li><a href='health-care.php' target='_blank' style='color: #155724; font-weight: bold;'>💊 Health Care Products</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
h3 { border-bottom: 2px solid #ddd; padding-bottom: 5px; }
p { margin: 5px 0; }
</style>
