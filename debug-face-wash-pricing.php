<?php
require_once 'config/dbconfig.php';

echo "<h2>🔍 Face Wash Pricing Debug</h2>";

try {
    // Check Face Wash product specifically
    $face_wash = fetchAll("
        SELECT id, title, price, sale_price,
               COALESCE(sale_price, price) as display_price,
               CASE WHEN sale_price IS NOT NULL AND sale_price < price
                    THEN price ELSE NULL END as original_price
        FROM products 
        WHERE title LIKE '%face%' OR title LIKE '%Face%' OR title LIKE '%wash%' OR title LIKE '%Wash%'
        ORDER BY title ASC
    ");
    
    echo "<h3>📊 Face Wash Products in Database</h3>";
    
    if ($face_wash) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 15px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>ID</th>";
        echo "<th style='padding: 10px;'>Product Name</th>";
        echo "<th style='padding: 10px;'>Original Price</th>";
        echo "<th style='padding: 10px;'>Sale Price</th>";
        echo "<th style='padding: 10px;'>Display Price</th>";
        echo "<th style='padding: 10px;'>Status</th>";
        echo "</tr>";
        
        foreach ($face_wash as $product) {
            echo "<tr>";
            echo "<td style='padding: 10px;'>" . $product['id'] . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($product['title']) . "</td>";
            echo "<td style='padding: 10px;'>Rs. " . number_format($product['price'], 2) . "</td>";
            echo "<td style='padding: 10px;'>" . ($product['sale_price'] ? 'Rs. ' . number_format($product['sale_price'], 2) : '<span style="color: red;">NOT SET</span>') . "</td>";
            echo "<td style='padding: 10px; font-weight: bold; color: #007bff;'>Rs. " . number_format($product['display_price'], 2) . "</td>";
            
            if ($product['sale_price'] && $product['sale_price'] < $product['price']) {
                $savings = $product['price'] - $product['sale_price'];
                $percentage = round(($savings / $product['price']) * 100);
                echo "<td style='padding: 10px; color: green;'>✅ ON SALE ({$percentage}% off)</td>";
            } else {
                echo "<td style='padding: 10px; color: orange;'>⚠️ NO SALE PRICE SET</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ No Face Wash products found!</p>";
    }
    
    // Check current cart items
    echo "<h3>🛒 Current Cart Items</h3>";
    
    $session_id = session_id();
    $current_cart = fetchAll("
        SELECT ci.*, p.title, p.price, p.sale_price,
               COALESCE(COALESCE(p.sale_price, p.price)) as item_price,
               CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price
                    THEN p.price ELSE NULL END as original_price
        FROM cart_items ci
        JOIN products p ON ci.product_id = p.id
        WHERE ci.session_id = ?
    ", [$session_id]);
    
    if ($current_cart) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 15px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Product</th>";
        echo "<th style='padding: 8px;'>DB Price</th>";
        echo "<th style='padding: 8px;'>DB Sale Price</th>";
        echo "<th style='padding: 8px;'>Cart Shows</th>";
        echo "<th style='padding: 8px;'>Quantity</th>";
        echo "<th style='padding: 8px;'>Total</th>";
        echo "</tr>";
        
        foreach ($current_cart as $item) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($item['title']) . "</td>";
            echo "<td style='padding: 8px;'>Rs. " . number_format($item['price'], 2) . "</td>";
            echo "<td style='padding: 8px;'>" . ($item['sale_price'] ? 'Rs. ' . number_format($item['sale_price'], 2) : 'None') . "</td>";
            echo "<td style='padding: 8px; font-weight: bold;'>Rs. " . number_format($item['item_price'], 2) . "</td>";
            echo "<td style='padding: 8px;'>" . $item['quantity'] . "</td>";
            echo "<td style='padding: 8px;'>Rs. " . number_format($item['item_price'] * $item['quantity'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: blue;'>ℹ️ No items in current cart</p>";
    }
    
    // Test the exact cart query
    echo "<h3>🧪 Testing Cart Query</h3>";
    
    $test_cart = fetchAll("
        SELECT ci.*, p.title, p.image, p.product_code, p.price, p.sale_price,
               pv.size, pv.price as volume_price, pv.offer,
               COALESCE(pv.price, COALESCE(p.sale_price, p.price)) as item_price,
               CASE WHEN pv.id IS NULL AND p.sale_price IS NOT NULL AND p.sale_price < p.price
                    THEN p.price ELSE NULL END as original_price
        FROM cart_items ci
        JOIN products p ON ci.product_id = p.id
        LEFT JOIN product_volumes pv ON ci.volume_id = pv.id
        WHERE ci.session_id = ?
        ORDER BY ci.added_at DESC
    ", [$session_id]);
    
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>Cart Query Result:</h4>";
    if ($test_cart) {
        echo "<pre style='background: #fff; padding: 10px; border-radius: 3px;'>";
        foreach ($test_cart as $item) {
            echo "Product: " . $item['title'] . "\n";
            echo "DB Price: Rs. " . $item['price'] . "\n";
            echo "DB Sale Price: " . ($item['sale_price'] ? 'Rs. ' . $item['sale_price'] : 'NULL') . "\n";
            echo "Volume Price: " . ($item['volume_price'] ? 'Rs. ' . $item['volume_price'] : 'NULL') . "\n";
            echo "Calculated item_price: Rs. " . $item['item_price'] . "\n";
            echo "Original price: " . ($item['original_price'] ? 'Rs. ' . $item['original_price'] : 'NULL') . "\n";
            echo "---\n";
        }
        echo "</pre>";
    } else {
        echo "<p>No cart items found</p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Face Wash Pricing Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h2, h3, h4 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
        table { border-collapse: collapse; width: 100%; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; font-weight: bold; }
    </style>
</head>
<body>

<div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <h4>⚠️ If Face Wash Sale Price is NOT SET:</h4>
    <ol>
        <li><strong>Go to Admin Panel:</strong> <a href="admin/products.php" target="_blank">admin/products.php</a></li>
        <li><strong>Find Face Wash product</strong> and click Edit</li>
        <li><strong>Set Sale Price to 495</strong> (if original price is higher)</li>
        <li><strong>Save the product</strong></li>
        <li><strong>Clear cart and add product again</strong></li>
    </ol>
</div>

<div style="background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <h4>✅ If Sale Price is Set but Cart Still Shows Wrong Price:</h4>
    <ol>
        <li><strong>Clear your cart:</strong> <a href="clear-cart-and-redirect.php">Clear Cart</a></li>
        <li><strong>Add Face Wash to cart again</strong> from product page</li>
        <li><strong>Check cart.php</strong> (not product-cart.php)</li>
    </ol>
</div>

<div style="background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <h4>🚨 Important:</h4>
    <ul>
        <li><strong>Make sure you're viewing cart.php</strong> (not product-cart.php)</li>
        <li><strong>Clear old cart items</strong> that were added before the fix</li>
        <li><strong>Set sale_price in database</strong> for the product to show discount</li>
    </ul>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="admin/products.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🛠️ Admin Panel</a>
    <a href="cart.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🛒 View Cart</a>
    <a href="clear-cart-and-redirect.php" style="background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🗑️ Clear Cart</a>
</div>

</body>
</html>
