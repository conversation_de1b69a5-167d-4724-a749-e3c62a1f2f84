<?php
session_start();
require_once 'config/dbconfig.php';

$session_id = session_id();
$cart_items = [];
$cart_total = 0;
$shipping_fee = 200;
$error_message = '';
$success_message = '';

// Get cart items
if ($session_id) {
    $cart_items = fetchAll("
        SELECT ci.*, p.title, p.image, p.product_code, p.price, p.sale_price,
               pv.size, pv.price as volume_price, pv.offer,
               COALESCE(pv.price, COALESCE(p.sale_price, p.price)) as item_price,
               CASE WHEN pv.id IS NULL AND p.sale_price IS NOT NULL AND p.sale_price < p.price
                    THEN p.price ELSE NULL END as original_price
        FROM cart_items ci
        JOIN products p ON ci.product_id = p.id
        LEFT JOIN product_volumes pv ON ci.volume_id = pv.id
        WHERE ci.session_id = ?
        ORDER BY ci.added_at DESC
    ", [$session_id]);
    
    foreach ($cart_items as $item) {
        $cart_total += $item['item_price'] * $item['quantity'];
    }
}

// Redirect if cart is empty
if (empty($cart_items)) {
    header('Location: cart.php');
    exit();
}

$final_total = $cart_total + $shipping_fee;

// Process checkout
if ($_POST) {
    $first_name = trim($_POST['first_name'] ?? '');
    $last_name = trim($_POST['last_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $city = trim($_POST['city'] ?? '');
    $postal_code = trim($_POST['postal_code'] ?? '');
    $payment_method = $_POST['payment_method'] ?? 'cod';
    $notes = trim($_POST['notes'] ?? '');
    
    // Validation
    if (empty($first_name) || empty($last_name) || empty($email) || empty($phone) || empty($address) || empty($city)) {
        $error_message = 'Please fill in all required fields.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'Please enter a valid email address.';
    } else {
        try {
            $pdo->beginTransaction();
            
            // Check if customer exists
            $customer = fetchSingle("SELECT * FROM customers WHERE email = ?", [$email]);
            
            if (!$customer) {
                // Create new customer
                $customer_id = insertData("
                    INSERT INTO customers (first_name, last_name, email, phone, address, city, postal_code, status) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, 'active')
                ", [$first_name, $last_name, $email, $phone, $address, $city, $postal_code]);
            } else {
                $customer_id = $customer['id'];
                // Update customer info
                executeQuery("
                    UPDATE customers 
                    SET first_name = ?, last_name = ?, phone = ?, address = ?, city = ?, postal_code = ?, updated_at = NOW()
                    WHERE id = ?
                ", [$first_name, $last_name, $phone, $address, $city, $postal_code, $customer_id]);
            }
            
            // Generate order number
            $order_number = 'ORD' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
            
            // Create order
            $shipping_address = "$address, $city, $postal_code";
            $billing_address = $shipping_address;
            
            $order_id = insertData("
                INSERT INTO orders (
                    customer_id, order_number, total_amount, shipping_fee, tax_amount, 
                    discount_amount, final_amount, status, payment_status, payment_method,
                    shipping_address, billing_address, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', 'pending', ?, ?, ?, ?)
            ", [
                $customer_id, $order_number, $cart_total, $shipping_fee, 0, 0, $final_total,
                $payment_method, $shipping_address, $billing_address, $notes
            ]);
            
            // Add order items
            foreach ($cart_items as $item) {
                executeQuery("
                    INSERT INTO order_items (order_id, product_id, volume_id, quantity, unit_price, total_price)
                    VALUES (?, ?, ?, ?, ?, ?)
                ", [
                    $order_id, $item['product_id'], $item['volume_id'], 
                    $item['quantity'], $item['item_price'], 
                    $item['item_price'] * $item['quantity']
                ]);
                
                // Update stock if volume exists
                if ($item['volume_id']) {
                    executeQuery("
                        UPDATE product_volumes 
                        SET stock_quantity = stock_quantity - ? 
                        WHERE id = ? AND stock_quantity >= ?
                    ", [$item['quantity'], $item['volume_id'], $item['quantity']]);
                }
            }
            
            // Clear cart
            executeQuery("DELETE FROM cart_items WHERE session_id = ?", [$session_id]);
            executeQuery("DELETE FROM cart_sessions WHERE session_id = ?", [$session_id]);

            // Clear session cart count
            unset($_SESSION['cart_count']);

            $pdo->commit();

            // Send order notification email to admin
            try {
                require_once 'order-email.php';
                sendOrderNotificationEmail($order_id);
                error_log("Order notification email sent for order #$order_number");
            } catch (Exception $e) {
                error_log("Failed to send order notification email: " . $e->getMessage());
                // Don't stop the order process if email fails
            }

            // Redirect to success page
            header("Location: order-success.php?order=$order_number");
            exit();
            
        } catch (Exception $e) {
            $pdo->rollBack();
            $error_message = 'Error processing order: ' . $e->getMessage();
        }
    }
}

include 'header.php';
?>

<!-- Custom Checkout Styling -->
<link rel="stylesheet" href="assets/css/cart-checkout-custom.css">

<br><br><br>
<main class="main-content">
    <!--== Start Page Header Area Wrapper ==-->
    <div class="page-header-area" data-bg-img="assets/images/photos/bg3.jpg">
        <div class="container pt--0 pb--0">
            <div class="row">
                <div class="col-12">
                    <div class="page-header-content">
                        <h2 class="title" data-aos="fade-down" data-aos-duration="1000">Checkout</h2>
                        <nav class="breadcrumb-area" data-aos="fade-down" data-aos-duration="1200">
                            <ul class="breadcrumb">
                                <li><a href="index.php">Home</a></li>
                                <li class="breadcrumb-sep">//</li>
                                <li><a href="cart.php">Cart</a></li>
                                <li class="breadcrumb-sep">//</li>
                                <li>Checkout</li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--== End Page Header Area Wrapper ==-->

    <!--== Start Checkout Area Wrapper ==-->
    <section class="section-space">
        <div class="container">
            <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <!-- Checkout Progress -->
            <div class="checkout-progress mb-4">
                <div class="progress-steps">
                    <div class="step active">
                        <div class="step-number">1</div>
                        <div class="step-title">Billing Details</div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-title">Order Review</div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-title">Payment</div>
                    </div>
                </div>
            </div>

            <form method="POST" id="checkout-form">
                <div class="row">
                    <div class="col-lg-7">
                        <div class="checkout-form">
                            <h3><i class="fa fa-user"></i> Billing Details</h3>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="checkout-form-list">
                                        <label>First Name <span class="required">*</span></label>
                                        <input type="text" name="first_name" value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="checkout-form-list">
                                        <label>Last Name <span class="required">*</span></label>
                                        <input type="text" name="last_name" value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="checkout-form-list">
                                        <label>Email Address <span class="required">*</span></label>
                                        <input type="email" name="email" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="checkout-form-list">
                                        <label>Phone <span class="required">*</span></label>
                                        <input type="tel" name="phone" value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="checkout-form-list">
                                        <label>Address <span class="required">*</span></label>
                                        <input type="text" name="address" placeholder="Street address" value="<?php echo htmlspecialchars($_POST['address'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="checkout-form-list">
                                        <label>Town / City <span class="required">*</span></label>
                                        <input type="text" name="city" value="<?php echo htmlspecialchars($_POST['city'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="checkout-form-list">
                                        <label>Postcode / ZIP</label>
                                        <input type="text" name="postal_code" value="<?php echo htmlspecialchars($_POST['postal_code'] ?? ''); ?>">
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="checkout-form-list">
                                        <label>Order Notes</label>
                                        <textarea name="notes" placeholder="Notes about your order, e.g. special notes for delivery."><?php echo htmlspecialchars($_POST['notes'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-5">
                        <div class="your-order">
                            <h3><i class="fa fa-shopping-bag"></i> Your Order</h3>
                            <div class="your-order-table table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th class="cart-product-name">Product</th>
                                            <th class="cart-product-total">Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($cart_items as $item): ?>
                                            <tr class="cart_item">
                                                <td class="cart-product-name">
                                                    <?php echo htmlspecialchars($item['title']); ?>
                                                    <?php if ($item['size']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($item['size']); ?></small>
                                                    <?php endif; ?>
                                                    <?php if ($item['original_price']): ?>
                                                        <br><small class="text-muted">
                                                            <span class="text-decoration-line-through">Rs. <?php echo number_format($item['original_price'], 2); ?></span>
                                                            <span class="text-danger fw-bold">Rs. <?php echo number_format($item['item_price'], 2); ?></span>
                                                        </small>
                                                    <?php else: ?>
                                                        <br><small class="text-muted">Rs. <?php echo number_format($item['item_price'], 2); ?> each</small>
                                                    <?php endif; ?>
                                                    <strong class="product-quantity"> × <?php echo $item['quantity']; ?></strong>
                                                </td>
                                                <td class="cart-product-total">
                                                    <span class="amount">Rs. <?php echo number_format($item['item_price'] * $item['quantity'], 2); ?></span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr class="cart-subtotal">
                                            <th>Cart Subtotal</th>
                                            <td><span class="amount">Rs. <?php echo number_format($cart_total, 2); ?></span></td>
                                        </tr>
                                        <tr class="shipping">
                                            <th>Shipping</th>
                                            <td>Rs. <?php echo number_format($shipping_fee, 2); ?></td>
                                        </tr>
                                        <tr class="order-total">
                                            <th>Order Total</th>
                                            <td><strong><span class="amount">Rs. <?php echo number_format($final_total, 2); ?></span></strong></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            
                            <div class="payment-method">
                                <div class="payment-accordion">
                                    <div class="payment-accordion-toggle">
                                        <input type="radio" name="payment_method" value="cod" id="cod" checked>
                                        <label for="cod"><i class="fa fa-money"></i> Cash on Delivery</label>
                                        <div class="payment-content">
                                            <p>Pay with cash upon delivery. No additional charges.</p>
                                        </div>
                                    </div>
                                    <div class="payment-accordion-toggle">
                                        <input type="radio" name="payment_method" value="bank_transfer" id="bank_transfer">
                                        <label for="bank_transfer"><i class="fa fa-bank"></i> Direct Bank Transfer</label>
                                        <div class="payment-content">
                                            <p>Make your payment directly into our bank account. Please use your Order ID as the payment reference.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="order-button-payment">
                                    <button type="submit" class="btn btn-primary btn-block">Place Order</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>
    <!--== End Checkout Area Wrapper ==-->
</main>

<?php include 'footer.php'; ?>

<script>
// Form validation and UX improvements
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('checkout-form');
    const inputs = form.querySelectorAll('input[required], textarea[required]');

    // Add real-time validation
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('error')) {
                validateField(this);
            }
        });
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        let isValid = true;

        inputs.forEach(input => {
            if (!validateField(input)) {
                isValid = false;
            }
        });

        if (!isValid) {
            e.preventDefault();
            showAlert('Please fill in all required fields correctly.', 'error');
            return;
        }

        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Processing Order...';
    });

    function validateField(field) {
        const value = field.value.trim();
        let isValid = true;

        // Remove existing error styling
        field.classList.remove('error');
        const existingError = field.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }

        // Check if required field is empty
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            showFieldError(field, 'This field is required');
        }

        // Email validation
        if (field.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                showFieldError(field, 'Please enter a valid email address');
            }
        }

        // Phone validation
        if (field.type === 'tel' && value) {
            const phoneRegex = /^[\d\s\-\+\(\)]+$/;
            if (!phoneRegex.test(value) || value.length < 10) {
                isValid = false;
                showFieldError(field, 'Please enter a valid phone number');
            }
        }

        return isValid;
    }

    function showFieldError(field, message) {
        field.classList.add('error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
    }

    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'error' ? 'danger' : 'success'}`;
        alertDiv.innerHTML = `<i class="fa fa-${type === 'error' ? 'exclamation-triangle' : 'check-circle'}"></i> ${message}`;

        const container = document.querySelector('.container');
        container.insertBefore(alertDiv, container.firstChild);

        // Auto remove after 5 seconds
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
});
</script>

<?php include 'includes/search-modal.php'; ?>
<?php include 'includes/scripts.php'; ?>

    <?php include 'includes/offcanvas-menu.php'; ?>
    <?php include 'includes/search-modal.php'; ?>

</body>
</html>
