<?php
// Debug Open Graph Tags - Show exactly what Facebook/WhatsApp sees
require_once 'config/dbconfig.php';

// Get the product parameter
$productCode = $_GET['product'] ?? 'face-wash-anti-acne-100ml';

echo "<h1>🔍 Open Graph Debug for: " . htmlspecialchars($productCode) . "</h1>";

// Fetch product from database - same logic as product-details.php
$product = null;

// Try to fetch by slug first
if (!empty($productCode)) {
    $product = fetchSingle("
        SELECT p.*,
               COALESCE(p.sale_price, p.price) as display_price,
               CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price
                    THEN p.price ELSE NULL END as original_price
        FROM products p
        WHERE p.slug = ? AND p.status = 'active'
    ", [$productCode]);
}

// If not found by slug, try by product_code
if (!$product && !empty($productCode)) {
    $product = fetchSingle("
        SELECT p.*,
               COALESCE(p.sale_price, p.price) as display_price,
               CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price
                    THEN p.price ELSE NULL END as original_price
        FROM products p
        WHERE p.product_code = ? AND p.status = 'active'
    ", [$productCode]);
}

if (!$product) {
    echo "<div style='color: red; padding: 20px; border: 1px solid red;'>";
    echo "<h2>❌ Product Not Found!</h2>";
    echo "<p>Could not find product with identifier: <strong>" . htmlspecialchars($productCode) . "</strong></p>";
    echo "<p>This is why Facebook/WhatsApp can't show the preview!</p>";
    echo "</div>";
    exit;
}

// Prepare social media sharing data - same as product-details.php
$og_title = htmlspecialchars($product['title']);
$og_description = htmlspecialchars(strlen($product['description']) > 160 ? substr($product['description'], 0, 157) . '...' : $product['description']);
$og_image = $product['image'] ? 'https://' . $_SERVER['HTTP_HOST'] . '/' . $product['image'] : 'https://' . $_SERVER['HTTP_HOST'] . '/assets/images/shop/default.png';
$og_url = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
$og_price = $product['display_price'];

echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .debug-section { border: 1px solid #ddd; margin: 15px 0; padding: 15px; border-radius: 5px; }
    .success { background: #d4edda; border-color: #c3e6cb; }
    .error { background: #f8d7da; border-color: #f5c6cb; }
    .info { background: #d1ecf1; border-color: #bee5eb; }
    .meta-tag { background: #f8f9fa; padding: 8px; margin: 3px 0; font-family: monospace; font-size: 13px; border-left: 3px solid #007bff; }
    .image-test { max-width: 300px; border: 1px solid #ddd; }
    .test-url { background: #fff3cd; padding: 10px; font-family: monospace; word-break: break-all; }
</style>";

echo "<div class='debug-section success'>";
echo "<h2>✅ Product Found</h2>";
echo "<p><strong>ID:</strong> " . $product['id'] . "</p>";
echo "<p><strong>Title:</strong> " . htmlspecialchars($product['title']) . "</p>";
echo "<p><strong>Slug:</strong> " . htmlspecialchars($product['slug'] ?: 'No slug') . "</p>";
echo "<p><strong>Product Code:</strong> " . htmlspecialchars($product['product_code']) . "</p>";
echo "<p><strong>Image Path:</strong> " . htmlspecialchars($product['image']) . "</p>";
echo "<p><strong>Status:</strong> " . htmlspecialchars($product['status']) . "</p>";
echo "</div>";

echo "<div class='debug-section info'>";
echo "<h2>🖼️ Image Test</h2>";
if ($product['image']) {
    $imageExists = file_exists($product['image']);
    echo "<p><strong>Image URL:</strong> " . $og_image . "</p>";
    echo "<p><strong>File exists on server:</strong> " . ($imageExists ? '✅ YES' : '❌ NO') . "</p>";
    
    if ($imageExists) {
        echo "<p><strong>File size:</strong> " . number_format(filesize($product['image'])) . " bytes</p>";
        echo "<p><strong>Preview:</strong></p>";
        echo "<img src='" . $og_image . "' alt='Product Image' class='image-test'>";
    } else {
        echo "<p style='color: red;'>❌ Image file not found at: " . htmlspecialchars($product['image']) . "</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ No image set for this product</p>";
}
echo "</div>";

echo "<div class='debug-section info'>";
echo "<h2>📋 Generated Open Graph Tags</h2>";
echo "<div class='meta-tag'>&lt;meta property=\"og:type\" content=\"product\"&gt;</div>";
echo "<div class='meta-tag'>&lt;meta property=\"og:title\" content=\"" . $og_title . "\"&gt;</div>";
echo "<div class='meta-tag'>&lt;meta property=\"og:description\" content=\"" . $og_description . "\"&gt;</div>";
echo "<div class='meta-tag'>&lt;meta property=\"og:image\" content=\"" . $og_image . "\"&gt;</div>";
echo "<div class='meta-tag'>&lt;meta property=\"og:url\" content=\"" . $og_url . "\"&gt;</div>";
echo "<div class='meta-tag'>&lt;meta property=\"og:site_name\" content=\"Dr.Zia Naturals\"&gt;</div>";
echo "<div class='meta-tag'>&lt;meta property=\"product:price:amount\" content=\"" . $og_price . "\"&gt;</div>";
echo "<div class='meta-tag'>&lt;meta property=\"product:price:currency\" content=\"USD\"&gt;</div>";
echo "</div>";

echo "<div class='debug-section info'>";
echo "<h2>🔗 Test URLs</h2>";
$actualProductUrl = 'https://' . $_SERVER['HTTP_HOST'] . '/product-details.php?product=' . $productCode;
echo "<div class='test-url'><strong>Actual Product URL:</strong><br>" . $actualProductUrl . "</div>";
echo "<p><a href='" . $actualProductUrl . "' target='_blank'>🔗 Open Product Page</a></p>";

$fbDebugUrl = 'https://developers.facebook.com/tools/debug/?q=' . urlencode($actualProductUrl);
echo "<p><a href='" . $fbDebugUrl . "' target='_blank'>📘 Test in Facebook Debugger</a></p>";
echo "</div>";

// Test if we can fetch the actual page content
echo "<div class='debug-section info'>";
echo "<h2>🌐 Page Content Test</h2>";
echo "<p>Testing if the product page loads correctly...</p>";

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'user_agent' => 'facebookexternalhit/1.1 (+http://www.facebook.com/externalhit_uatext.php)'
    ]
]);

$pageContent = @file_get_contents($actualProductUrl, false, $context);
if ($pageContent) {
    echo "<p style='color: green;'>✅ Page loads successfully</p>";
    
    // Check if Open Graph tags are present
    if (strpos($pageContent, 'og:image') !== false) {
        echo "<p style='color: green;'>✅ Open Graph tags found in page</p>";
    } else {
        echo "<p style='color: red;'>❌ Open Graph tags NOT found in page</p>";
    }
    
    // Extract the og:image tag
    if (preg_match('/<meta property="og:image" content="([^"]+)"/', $pageContent, $matches)) {
        echo "<p><strong>Found og:image:</strong> " . htmlspecialchars($matches[1]) . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Could not load page - this is why Facebook/WhatsApp can't show preview!</p>";
}
echo "</div>";

echo "<div class='debug-section success'>";
echo "<h2>🛠️ Next Steps</h2>";
echo "<ol>";
echo "<li><strong>Copy the 'Actual Product URL' above</strong></li>";
echo "<li><strong>Paste it in Facebook Debugger</strong> (link provided above)</li>";
echo "<li><strong>Click 'Debug' button</strong></li>";
echo "<li><strong>If there are errors, they will show up there</strong></li>";
echo "<li><strong>Click 'Scrape Again' to force refresh</strong></li>";
echo "</ol>";
echo "</div>";
?>
