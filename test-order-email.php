<?php
require_once 'config/dbconfig.php';

echo "<h2>📧 Order Email Configuration Test</h2>";

// Check email configuration
echo "<h3>📋 Current Email Configuration</h3>";

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>✅ Updated Email Settings:</h4>";
echo "<ul>";
echo "<li><strong>SMTP Username:</strong> <EMAIL></li>";
echo "<li><strong>From Email:</strong> <EMAIL></li>";
echo "<li><strong>Admin Notification Email:</strong> <EMAIL></li>";
echo "<li><strong>Reply-To Email:</strong> <EMAIL></li>";
echo "</ul>";
echo "</div>";

// Test email function
if (isset($_POST['test_email'])) {
    echo "<h3>🧪 Testing Email...</h3>";
    
    try {
        require_once 'order-email.php';
        
        // Get a recent order to test with
        $test_order = fetchSingle("
            SELECT * FROM orders 
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        
        if ($test_order) {
            $result = sendOrderNotificationEmail($test_order['id']);
            
            if ($result) {
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>✅ Email Test Successful!</h4>";
                echo "<p>Test email sent to <strong><EMAIL></strong></p>";
                echo "<p>Check your inbox for the order notification.</p>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>❌ Email Test Failed!</h4>";
                echo "<p>There was an issue sending the test email.</p>";
                echo "</div>";
            }
        } else {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h4>⚠️ No Orders Found</h4>";
            echo "<p>No orders found to test with. Place a test order first.</p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>❌ Email Test Error!</h4>";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
}

// Check recent orders
try {
    $recent_orders = fetchAll("
        SELECT id, order_number, customer_name, total_amount, created_at, status
        FROM orders 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    
    echo "<h3>📦 Recent Orders</h3>";
    
    if ($recent_orders) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 15px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Order #</th>";
        echo "<th style='padding: 8px;'>Customer</th>";
        echo "<th style='padding: 8px;'>Amount</th>";
        echo "<th style='padding: 8px;'>Date</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "</tr>";
        
        foreach ($recent_orders as $order) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($order['order_number']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($order['customer_name']) . "</td>";
            echo "<td style='padding: 8px;'>Rs. " . number_format($order['total_amount'], 2) . "</td>";
            echo "<td style='padding: 8px;'>" . date('M j, Y g:i A', strtotime($order['created_at'])) . "</td>";
            echo "<td style='padding: 8px;'>" . ucfirst($order['status']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: blue;'>ℹ️ No orders found yet.</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error loading orders: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Order Email Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h2, h3, h4 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
        table { border-collapse: collapse; width: 100%; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; font-weight: bold; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>

<div style="background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <h4>✅ Email Configuration Updated!</h4>
    <p><strong>All order notifications will now be sent to:</strong> <EMAIL></p>
    <ul>
        <li>✅ SMTP credentials updated</li>
        <li>✅ From address updated</li>
        <li>✅ Admin notification address updated</li>
        <li>✅ Reply-to address updated</li>
    </ul>
</div>

<div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <h4>🧪 Test Email Notification</h4>
    <p>Click the button below to send a test email <NAME_EMAIL>:</p>
    <form method="POST">
        <button type="submit" name="test_email">📧 Send Test Email</button>
    </form>
    <p><small><strong>Note:</strong> This will use the most recent order for testing.</small></p>
</div>

<div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <h4>📋 How Order Emails Work:</h4>
    <ol>
        <li><strong>Customer places order</strong> on checkout page</li>
        <li><strong>Order is saved</strong> to database</li>
        <li><strong>Email notification sent</strong> to <EMAIL></li>
        <li><strong>Email includes:</strong> Order details, customer info, product list, total amount</li>
    </ol>
</div>

<div style="background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <h4>⚠️ Important Notes:</h4>
    <ul>
        <li><strong>Gmail App Password:</strong> Make sure the app password is <NAME_EMAIL></li>
        <li><strong>2-Factor Authentication:</strong> Must be enabled on the Gmail account</li>
        <li><strong>Less Secure Apps:</strong> Should be disabled (use app password instead)</li>
        <li><strong>Check Spam Folder:</strong> First few emails might go to spam</li>
    </ul>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="checkout.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🛒 Test Checkout</a>
    <a href="admin/orders.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">📦 View Orders</a>
    <a href="cart.php" style="background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🛒 Add to Cart</a>
</div>

<div style="background: #d1ecf1; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center;">
    <h3>🎉 Email Configuration Complete!</h3>
    <p>Order notifications will now be sent to <strong><EMAIL></strong></p>
    <p>Place a test order to verify the email system is working!</p>
</div>

</body>
</html>
