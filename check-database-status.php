<?php
require_once 'config/dbconfig.php';

echo "<h1>🔍 Database Status Check</h1>";
echo "<p>Let's check the current state of your database and products table.</p>";

try {
    // Check if slug column exists
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>📊 Products Table Structure</h2>";
    
    $columns = fetchAll("SHOW COLUMNS FROM products");
    
    echo "<table style='width: 100%; border-collapse: collapse; margin: 15px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='border: 1px solid #ddd; padding: 10px; text-align: left;'>Column</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px; text-align: left;'>Type</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px; text-align: left;'>Null</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px; text-align: left;'>Key</th>";
    echo "</tr>";
    
    $has_slug = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'slug') {
            $has_slug = true;
        }
        
        $row_color = ($column['Field'] === 'slug') ? 'background: #d4edda;' : '';
        echo "<tr style='$row_color'>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $column['Field'] . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $column['Type'] . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $column['Null'] . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $column['Key'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if ($has_slug) {
        echo "<p style='color: green;'>✅ Slug column exists!</p>";
    } else {
        echo "<p style='color: red;'>❌ Slug column is missing!</p>";
    }
    echo "</div>";

    // Check products data
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>📦 Products Data</h2>";
    
    if ($has_slug) {
        $products = fetchAll("SELECT id, product_code, title, slug FROM products WHERE status = 'active' ORDER BY id LIMIT 10");
    } else {
        $products = fetchAll("SELECT id, product_code, title FROM products WHERE status = 'active' ORDER BY id LIMIT 10");
    }
    
    if (!empty($products)) {
        echo "<table style='width: 100%; border-collapse: collapse; margin: 15px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='border: 1px solid #ddd; padding: 10px; text-align: left;'>ID</th>";
        echo "<th style='border: 1px solid #ddd; padding: 10px; text-align: left;'>Product Code</th>";
        echo "<th style='border: 1px solid #ddd; padding: 10px; text-align: left;'>Title</th>";
        if ($has_slug) {
            echo "<th style='border: 1px solid #ddd; padding: 10px; text-align: left;'>Slug</th>";
        }
        echo "</tr>";
        
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $product['id'] . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $product['product_code'] . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($product['title']) . "</td>";
            if ($has_slug) {
                $slug_status = empty($product['slug']) ? '<span style="color: red;">❌ Empty</span>' : '<span style="color: green;">✅ ' . htmlspecialchars($product['slug']) . '</span>';
                echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $slug_status . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠ No products found in database</p>";
    }
    echo "</div>";

    // Action recommendations
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🎯 Next Steps</h2>";
    
    if (!$has_slug) {
        echo "<p><strong>❌ Slug column is missing. You need to:</strong></p>";
        echo "<ol>";
        echo "<li><a href='implement-product-slugs.php' style='color: #007bff; font-weight: bold;'>Run Step 1: Add Slug Column</a></li>";
        echo "<li>Then update product links</li>";
        echo "<li>Then test the URLs</li>";
        echo "</ol>";
    } else {
        // Check if slugs are populated
        $empty_slugs = fetchSingle("SELECT COUNT(*) as count FROM products WHERE slug IS NULL OR slug = ''")['count'];
        
        if ($empty_slugs > 0) {
            echo "<p><strong>⚠ Slug column exists but $empty_slugs products have empty slugs. You need to:</strong></p>";
            echo "<ol>";
            echo "<li><a href='implement-product-slugs.php' style='color: #007bff; font-weight: bold;'>Run Step 1: Generate Slugs</a></li>";
            echo "<li>Then update product links</li>";
            echo "<li>Then test the URLs</li>";
            echo "</ol>";
        } else {
            echo "<p><strong>✅ Slugs exist! Now you need to:</strong></p>";
            echo "<ol>";
            echo "<li><a href='update-product-links.php' style='color: #28a745; font-weight: bold;'>Run Step 2: Update Product Links</a></li>";
            echo "<li>Then test the URLs</li>";
            echo "</ol>";
        }
    }
    echo "</div>";

    // Quick action buttons
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center;'>";
    echo "<h2>🚀 Quick Actions</h2>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    echo "<a href='implement-product-slugs.php' style='background: #007bff; color: white; padding: 15px; text-decoration: none; border-radius: 5px; display: block;'>📊 Step 1: Add Slugs</a>";
    echo "<a href='update-product-links.php' style='background: #28a745; color: white; padding: 15px; text-decoration: none; border-radius: 5px; display: block;'>🔗 Step 2: Update Links</a>";
    echo "<a href='test-cart-with-slugs.php' style='background: #17a2b8; color: white; padding: 15px; text-decoration: none; border-radius: 5px; display: block;'>🧪 Step 3: Test URLs</a>";
    echo "<a href='setup-seo-friendly-urls.php' style='background: #6c757d; color: white; padding: 15px; text-decoration: none; border-radius: 5px; display: block;'>📋 Master Guide</a>";
    
    echo "</div>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px;'>";
    echo "<h3>❌ Database Error</h3>";
    echo "<p style='color: #721c24;'>" . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection in config/dbconfig.php</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
table { font-size: 14px; }
a { text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
