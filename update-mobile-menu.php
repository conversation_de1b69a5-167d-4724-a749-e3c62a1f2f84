<?php
// <PERSON><PERSON>t to update mobile menu across all pages
echo "<h2>🔧 Updating Mobile Menu Across All Pages</h2>";

// List of files that contain the mobile menu
$files_to_update = [
    'contact.php',
    'my-account.php',
    'hair-care.php',
    'skin-care.php',
    'health-care.php',
    'personal-care.php',
    'product-details.php',
    'cart.php',
    'checkout.php',
    'login.php',
    'signup.php',
    'search.php',
    'product-left-sidebar.php'
];

// New mobile menu structure
$new_menu = '                <div id="offcanvasNav" class="offcanvas-menu-nav">
                    <ul>
                        <li class="offcanvas-nav-parent"><a class="offcanvas-nav-item" href="index.php">Home</a></li>
                        <li class="offcanvas-nav-parent"><a class="offcanvas-nav-item" href="about-us.php">About</a></li>
                        <li class="offcanvas-nav-parent"><a class="offcanvas-nav-item" href="hair-care.php">Hair Care</a></li>
                        <li class="offcanvas-nav-parent"><a class="offcanvas-nav-item" href="skin-care.php">Skin Care</a></li>
                        <li class="offcanvas-nav-parent"><a class="offcanvas-nav-item" href="health-care.php">Health Care</a></li>
                        <li class="offcanvas-nav-parent"><a class="offcanvas-nav-item" href="personal-care.php">Personal Care</a></li>
                        <li class="offcanvas-nav-parent"><a class="offcanvas-nav-item" href="contact.php">Contact</a></li>
                        <li class="offcanvas-nav-parent"><a class="offcanvas-nav-item" href="#">Account</a>
                            <ul>
                                <li><a href="cart.php">Shopping Cart</a></li>
                                <li><a href="checkout.php">Checkout</a></li>
                                <li><a href="my-account.php">My Account</a></li>
                                <li><a href="login.php">Login</a></li>
                                <li><a href="register.php">Register</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>';

$updated_count = 0;
$error_count = 0;

foreach ($files_to_update as $file) {
    if (file_exists($file)) {
        echo "<h3>📄 Processing: $file</h3>";
        
        $content = file_get_contents($file);
        
        // Pattern to match the old mobile menu structure
        $pattern = '/<div id="offcanvasNav" class="offcanvas-menu-nav">.*?<\/div>/s';
        
        if (preg_match($pattern, $content)) {
            // Replace the old menu with new menu
            $new_content = preg_replace($pattern, $new_menu, $content);
            
            if ($new_content !== $content) {
                if (file_put_contents($file, $new_content)) {
                    echo "<p style='color: green;'>✅ Updated mobile menu in $file</p>";
                    $updated_count++;
                } else {
                    echo "<p style='color: red;'>❌ Failed to write to $file</p>";
                    $error_count++;
                }
            } else {
                echo "<p style='color: orange;'>⚠ No changes needed in $file</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ No mobile menu found in $file (might use includes)</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ File not found: $file</p>";
        $error_count++;
    }
}

echo "<hr>";
echo "<h3>📊 Update Summary</h3>";
echo "<p><strong>Files processed:</strong> " . count($files_to_update) . "</p>";
echo "<p><strong>Successfully updated:</strong> $updated_count</p>";
echo "<p><strong>Errors:</strong> $error_count</p>";

if ($updated_count > 0) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p><strong>🎉 Mobile menu updated successfully!</strong></p>";
    echo "<p><strong>New mobile menu structure:</strong></p>";
    echo "<ul>";
    echo "<li>✅ <strong>Home</strong> - Direct link to homepage</li>";
    echo "<li>✅ <strong>About</strong> - About us page</li>";
    echo "<li>✅ <strong>Hair Care</strong> - Hair care products</li>";
    echo "<li>✅ <strong>Skin Care</strong> - Skin care products</li>";
    echo "<li>✅ <strong>Health Care</strong> - Health care products</li>";
    echo "<li>✅ <strong>Contact</strong> - Contact page</li>";
    echo "<li>✅ <strong>Account</strong> - Dropdown with:</li>";
    echo "<ul>";
    echo "<li>• Shopping Cart</li>";
    echo "<li>• Checkout</li>";
    echo "<li>• My Account</li>";
    echo "<li>• Login</li>";
    echo "<li>• Register</li>";
    echo "</ul>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p><strong>⚠ No files were updated</strong></p>";
    echo "<p>This might be because:</p>";
    echo "<ul>";
    echo "<li>Files are using includes for mobile menu</li>";
    echo "<li>Mobile menu structure is different than expected</li>";
    echo "<li>Files already have the updated menu</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h3>🧪 Test Your Mobile Menu</h3>";
echo "<p><strong>To test the updated mobile menu:</strong></p>";
echo "<ol>";
echo "<li>Open your website on mobile or resize browser window</li>";
echo "<li>Click the hamburger menu button (☰)</li>";
echo "<li>Verify the new menu structure appears</li>";
echo "<li>Test navigation links work correctly</li>";
echo "</ol>";

echo "<h3>🔗 Quick Links</h3>";
echo "<p><a href='index.php' target='_blank'>🏠 Test Home Page</a></p>";
echo "<p><a href='hair-care.php' target='_blank'>💇 Test Hair Care Page</a></p>";
echo "<p><a href='about-us.php' target='_blank'>ℹ Test About Page</a></p>";
echo "<p><a href='contact.php' target='_blank'>📞 Test Contact Page</a></p>";

// Show current menu structure for verification
echo "<h3>📋 Current Mobile Menu Structure</h3>";
echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-line;'>";
echo "MENU\n";
echo "├── Home\n";
echo "├── About\n";
echo "├── Hair Care\n";
echo "├── Skin Care\n";
echo "├── Health Care\n";
echo "├── Contact\n";
echo "└── Account\n";
echo "    ├── Shopping Cart\n";
echo "    ├── Checkout\n";
echo "    ├── My Account\n";
echo "    ├── Login\n";
echo "    └── Register\n";
echo "</div>";

echo "<div style='background: #e3f2fd; border: 1px solid #bbdefb; color: #1565c0; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<p><strong>📱 Mobile Menu Features:</strong></p>";
echo "<ul>";
echo "<li>✅ <strong>Clean structure</strong> - No unnecessary submenus</li>";
echo "<li>✅ <strong>Direct navigation</strong> - Main pages accessible with one tap</li>";
echo "<li>✅ <strong>Account section</strong> - All user-related pages grouped together</li>";
echo "<li>✅ <strong>Consistent with desktop</strong> - Matches main navigation</li>";
echo "<li>✅ <strong>Mobile-friendly</strong> - Easy to use on touch devices</li>";
echo "</ul>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
hr { margin: 30px 0; border: none; border-top: 2px solid #eee; }
</style>
