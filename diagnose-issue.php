<?php
// Diagnose why products show in admin but not on website
require_once 'config/dbconfig.php';

echo "<h2>🔍 Diagnosing Product Display Issue</h2>";
echo "<p>Products show in admin panel but not on website...</p>";

try {
    // 1. Check basic database connection
    echo "<h3>1. Database Connection Test</h3>";
    $test = $pdo->query("SELECT 1 as test")->fetch();
    echo "<p style='color: green;'>✅ Database connection working</p>";
    
    // 2. Count total products
    echo "<h3>2. Products Count</h3>";
    $total = $pdo->query("SELECT COUNT(*) as count FROM products")->fetch();
    echo "<p><strong>Total products in database:</strong> " . $total['count'] . "</p>";
    
    $active = $pdo->query("SELECT COUNT(*) as count FROM products WHERE status = 'active'")->fetch();
    echo "<p><strong>Active products:</strong> " . $active['count'] . "</p>";
    
    if ($active['count'] == 0) {
        echo "<p style='color: red;'>❌ No active products found! This is the problem.</p>";
        echo "<p><strong>Solution:</strong> Products might have status = 'inactive' or different status values.</p>";
        
        // Check what status values exist
        $statuses = $pdo->query("SELECT status, COUNT(*) as count FROM products GROUP BY status")->fetchAll();
        echo "<p><strong>Product statuses:</strong></p>";
        foreach ($statuses as $status) {
            echo "<p>• " . $status['status'] . ": " . $status['count'] . " products</p>";
        }
    }
    
    // 3. Test the exact query from index.php
    echo "<h3>3. Testing Home Page Query</h3>";
    $home_query = "
        SELECT p.*,
               AVG(pr.rating) as avg_rating,
               COUNT(pr.id) as review_count,
               COALESCE(p.sale_price, p.price) as display_price,
               CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price 
                    THEN p.price ELSE NULL END as original_price
        FROM products p
        LEFT JOIN product_reviews pr ON p.id = pr.product_id AND pr.status = 'approved'
        WHERE p.status = 'active'
        GROUP BY p.id
        ORDER BY p.product_code ASC
        LIMIT 6
    ";
    
    try {
        $home_products = $pdo->query($home_query)->fetchAll();
        echo "<p><strong>Home page query result:</strong> " . count($home_products) . " products</p>";
        
        if (count($home_products) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Code</th><th>Title</th><th>Price</th><th>Status</th><th>Image</th></tr>";
            foreach ($home_products as $product) {
                echo "<tr>";
                echo "<td>" . $product['product_code'] . "</td>";
                echo "<td>" . htmlspecialchars($product['title']) . "</td>";
                echo "<td>Rs. " . $product['price'] . "</td>";
                echo "<td>" . $product['status'] . "</td>";
                echo "<td>" . ($product['image'] ? "✅" : "❌") . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: red;'>❌ Home page query returned no results!</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error in home page query: " . $e->getMessage() . "</p>";
        
        // Try simpler query
        echo "<p>Trying simpler query...</p>";
        $simple_products = $pdo->query("SELECT * FROM products WHERE status = 'active' LIMIT 5")->fetchAll();
        echo "<p><strong>Simple query result:</strong> " . count($simple_products) . " products</p>";
    }
    
    // 4. Check if product_reviews table exists
    echo "<h3>4. Checking Related Tables</h3>";
    try {
        $pdo->query("SELECT 1 FROM product_reviews LIMIT 1");
        echo "<p style='color: green;'>✅ product_reviews table exists</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ product_reviews table missing - this might cause query issues</p>";
        echo "<p>Creating product_reviews table...</p>";
        
        $create_reviews = "
        CREATE TABLE IF NOT EXISTS `product_reviews` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `product_id` int(11) NOT NULL,
            `customer_name` varchar(100) NOT NULL,
            `rating` decimal(2,1) NOT NULL,
            `comment` text,
            `status` enum('approved','pending','rejected') DEFAULT 'pending',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ";
        
        $pdo->exec($create_reviews);
        echo "<p style='color: green;'>✅ Created product_reviews table</p>";
    }
    
    // 5. Test category queries
    echo "<h3>5. Testing Category Queries</h3>";
    
    // Hair Care
    $hair_query = "
        SELECT DISTINCT p.*,
               GROUP_CONCAT(c.name SEPARATOR ', ') as categories,
               COALESCE(p.sale_price, p.price) as display_price,
               CASE WHEN p.sale_price IS NOT NULL AND p.sale_price < p.price 
                    THEN p.price ELSE NULL END as original_price
        FROM products p
        LEFT JOIN product_categories pc ON p.id = pc.product_id
        LEFT JOIN categories c ON pc.category_id = c.id
        WHERE p.status = 'active'
        AND (c.name = 'Hair Care'
             OR p.title LIKE '%hair%'
             OR p.title LIKE '%Hair%'
             OR p.title LIKE '%shampoo%'
             OR p.title LIKE '%Shampoo%'
             OR p.title LIKE '%serum%'
             OR p.title LIKE '%Serum%')
        GROUP BY p.id
        ORDER BY p.created_at ASC
    ";
    
    try {
        $hair_products = $pdo->query($hair_query)->fetchAll();
        echo "<p><strong>Hair Care query:</strong> " . count($hair_products) . " products</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Hair Care query error: " . $e->getMessage() . "</p>";
        
        // Try without categories
        $simple_hair = $pdo->query("SELECT * FROM products WHERE status = 'active' AND (title LIKE '%hair%' OR title LIKE '%Hair%' OR title LIKE '%serum%' OR title LIKE '%Serum%')")->fetchAll();
        echo "<p><strong>Simple Hair Care query:</strong> " . count($simple_hair) . " products</p>";
    }
    
    // 6. Check table structure
    echo "<h3>6. Checking Table Structure</h3>";
    $columns = $pdo->query("SHOW COLUMNS FROM products")->fetchAll();
    $column_names = array_column($columns, 'Field');
    
    $required_fields = ['key_benefits', 'ingredients', 'how_to_use', 'sale_price'];
    $missing_fields = [];
    
    foreach ($required_fields as $field) {
        if (!in_array($field, $column_names)) {
            $missing_fields[] = $field;
        }
    }
    
    if (!empty($missing_fields)) {
        echo "<p style='color: orange;'>⚠ Missing fields: " . implode(', ', $missing_fields) . "</p>";
        echo "<p>Adding missing fields...</p>";
        
        foreach ($missing_fields as $field) {
            switch ($field) {
                case 'key_benefits':
                case 'ingredients':
                case 'how_to_use':
                    $pdo->exec("ALTER TABLE products ADD COLUMN $field TEXT NULL");
                    break;
                case 'sale_price':
                    $pdo->exec("ALTER TABLE products ADD COLUMN $field DECIMAL(10,2) NULL");
                    break;
            }
            echo "<p>✅ Added field: $field</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ All required fields exist</p>";
    }
    
    // 7. Final test
    echo "<h3>7. Final Test</h3>";
    $final_test = $pdo->query("SELECT * FROM products WHERE status = 'active' LIMIT 3")->fetchAll();
    echo "<p><strong>Final test result:</strong> " . count($final_test) . " products found</p>";
    
    if (count($final_test) > 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<p><strong>✅ Products found! The issue might be in the website code.</strong></p>";
        echo "<p><strong>Next steps:</strong></p>";
        echo "<ul>";
        echo "<li><a href='index.php' target='_blank'>Test Home Page</a></li>";
        echo "<li><a href='hair-care.php' target='_blank'>Test Hair Care Page</a></li>";
        echo "<li>Check browser console for JavaScript errors</li>";
        echo "<li>Check if images are loading properly</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<p><strong>❌ No active products found!</strong></p>";
        echo "<p><strong>Solution:</strong> Update product status to 'active'</p>";
        echo "<p><a href='#' onclick='fixProductStatus()' style='background: #dc3545; color: white; padding: 8px 12px; text-decoration: none; border-radius: 4px;'>Fix Product Status</a></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<script>
function fixProductStatus() {
    if (confirm('This will set all products to active status. Continue?')) {
        fetch('fix-product-status.php')
        .then(response => response.text())
        .then(data => {
            alert('Product status updated! Refresh this page to see results.');
            location.reload();
        })
        .catch(error => {
            alert('Error updating status: ' + error);
        });
    }
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
h3 { border-bottom: 2px solid #ddd; padding-bottom: 5px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background: #f0f0f0; }
</style>
