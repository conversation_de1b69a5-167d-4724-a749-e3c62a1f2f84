<?php
require_once '../config/dbconfig.php';

// Generate new password hash
$password = 'admin123';
$hash = password_hash($password, PASSWORD_DEFAULT);

echo "<h2>Password Hash Generator</h2>";
echo "<p><strong>Password:</strong> $password</p>";
echo "<p><strong>New Hash:</strong> $hash</p>";

// Check if admin user exists
$admin = fetchSingle("SELECT * FROM admin_users WHERE username = 'admin'", []);

if ($admin) {
    echo "<h3>Current Admin User:</h3>";
    echo "<p><strong>Username:</strong> " . htmlspecialchars($admin['username']) . "</p>";
    echo "<p><strong>Email:</strong> " . htmlspecialchars($admin['email']) . "</p>";
    echo "<p><strong>Status:</strong> " . htmlspecialchars($admin['status']) . "</p>";
    echo "<p><strong>Current Hash:</strong> " . htmlspecialchars($admin['password']) . "</p>";
    
    // Test password verification
    $verify_result = password_verify($password, $admin['password']);
    echo "<p><strong>Password Verification:</strong> " . ($verify_result ? 'SUCCESS' : 'FAILED') . "</p>";
    
    if (!$verify_result) {
        echo "<h3>Updating Password Hash...</h3>";
        $updated = updateData("UPDATE admin_users SET password = ? WHERE username = 'admin'", [$hash]);
        if ($updated) {
            echo "<p style='color: green;'>✅ Password updated successfully!</p>";
            echo "<p>You can now login with:</p>";
            echo "<p><strong>Username:</strong> admin</p>";
            echo "<p><strong>Password:</strong> admin123</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to update password</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ Password is already correct!</p>";
    }
} else {
    echo "<h3>Admin User Not Found - Creating New Admin User...</h3>";
    
    $inserted = insertData("
        INSERT INTO admin_users (username, email, password, full_name, role, status) 
        VALUES (?, ?, ?, ?, ?, ?)
    ", ['admin', '<EMAIL>', $hash, 'Administrator', 'super_admin', 'active']);
    
    if ($inserted) {
        echo "<p style='color: green;'>✅ Admin user created successfully!</p>";
        echo "<p>Login credentials:</p>";
        echo "<p><strong>Username:</strong> admin</p>";
        echo "<p><strong>Password:</strong> admin123</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create admin user</p>";
    }
}

echo "<hr>";
echo "<p><a href='login.php'>← Back to Login</a></p>";
?>
