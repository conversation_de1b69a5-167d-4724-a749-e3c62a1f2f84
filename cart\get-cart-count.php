<?php
session_start();
require_once '../config/dbconfig.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    $cart_count = 0;
    $session_id = session_id();
    
    if ($session_id) {
        $cart_count_result = fetchSingle("SELECT SUM(quantity) as total FROM cart_items WHERE session_id = ?", [$session_id]);
        $cart_count = $cart_count_result['total'] ?? 0;
    }
    
    echo json_encode([
        'success' => true,
        'cart_count' => (int)$cart_count
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'cart_count' => 0
    ]);
}
?>
