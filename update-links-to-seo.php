<?php
require_once 'config/dbconfig.php';

echo "<h1>🔗 Update Product Links to SEO-Friendly URLs</h1>";
echo "<p>This will update your category pages to use SEO-friendly product URLs.</p>";

try {
    // Files to update
    $files_to_update = [
        'index.php' => '🏠 Home Page',
        'hair-care.php' => '💇 Hair Care',
        'skin-care.php' => '🧴 Skin Care', 
        'health-care.php' => '💊 Health Care',
        'personal-care.php' => '🧼 Personal Care'
    ];
    
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>📝 Updating Product Links</h2>";
    
    foreach ($files_to_update as $file => $title) {
        if (!file_exists($file)) {
            echo "<p style='color: red;'>❌ $title ($file) - File not found</p>";
            continue;
        }
        
        // Read file content
        $content = file_get_contents($file);
        
        // Replace product_code with slug in product links
        $old_pattern = 'href="product-details.php?product=<?php echo htmlspecialchars($product[\'product_code\']); ?>"';
        $new_pattern = 'href="product-details.php?product=<?php echo htmlspecialchars($product[\'slug\'] ?: $product[\'product_code\']); ?>"';
        
        $updated_content = str_replace($old_pattern, $new_pattern, $content);
        
        // Check if any changes were made
        if ($content !== $updated_content) {
            // Backup original file
            $backup_name = $file . '.backup.' . date('Y-m-d-H-i-s');
            file_put_contents($backup_name, $content);
            
            // Write updated content
            if (file_put_contents($file, $updated_content)) {
                echo "<p style='color: green;'>✅ $title ($file) - Updated successfully</p>";
                echo "<p style='color: #6c757d; margin-left: 20px;'>Backup saved as: $backup_name</p>";
            } else {
                echo "<p style='color: red;'>❌ $title ($file) - Failed to update</p>";
            }
        } else {
            echo "<p style='color: #6c757d;'>ℹ $title ($file) - No changes needed</p>";
        }
    }
    echo "</div>";
    
    // Test the updated links
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🧪 Test Your Updated Pages</h2>";
    echo "<p>Your pages now use SEO-friendly URLs. Click to test:</p>";
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
    foreach ($files_to_update as $file => $title) {
        if (file_exists($file)) {
            echo "<a href='$file' target='_blank' style='background: #28a745; color: white; padding: 15px; text-decoration: none; border-radius: 5px; text-align: center; display: block; font-weight: bold;'>$title</a>";
        }
    }
    echo "</div>";
    echo "</div>";
    
    // Show example URLs
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>📋 URL Examples</h2>";
    echo "<p>Your product links now look like this:</p>";
    
    $sample_products = fetchAll("SELECT product_code, title, slug FROM products WHERE status = 'active' AND slug IS NOT NULL LIMIT 3");
    
    foreach ($sample_products as $product) {
        echo "<div style='background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<h4 style='margin: 0 0 10px 0;'>" . htmlspecialchars($product['title']) . "</h4>";
        echo "<p style='margin: 5px 0;'><strong>❌ Old:</strong> <span style='color: #dc3545; font-family: monospace;'>product-details.php?product=" . $product['product_code'] . "</span></p>";
        echo "<p style='margin: 5px 0;'><strong>✅ New:</strong> <a href='product-details.php?product=" . $product['slug'] . "' target='_blank' style='color: #28a745; font-family: monospace; font-weight: bold;'>product-details.php?product=" . $product['slug'] . "</a></p>";
        echo "</div>";
    }
    echo "</div>";
    
    // Success message
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center;'>";
    echo "<h2>🎉 Update Complete!</h2>";
    echo "<p style='font-size: 18px; color: #155724;'>Your product links are now SEO-friendly!</p>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<h3>✅ What's Working:</h3>";
    echo "<ul style='text-align: left; display: inline-block;'>";
    echo "<li>✅ All pages work with .php extension</li>";
    echo "<li>✅ Product links use descriptive URLs</li>";
    echo "<li>✅ Old URLs still work (backward compatibility)</li>";
    echo "<li>✅ Cart functionality preserved</li>";
    echo "<li>✅ No .htaccess conflicts</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p style='color: #721c24;'>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
a { text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
