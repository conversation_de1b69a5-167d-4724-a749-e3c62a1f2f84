<!--== Start Aside Search Form ==-->
<aside class="aside-search-box-wrapper offcanvas offcanvas-top" tabindex="-1" id="AsideOffcanvasSearch" aria-labelledby="offcanvasTopLabel">
    <div class="offcanvas-header">
        <h5 class="d-none" id="offcanvasTopLabel">Aside Search</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"><i class="fa fa-close"></i></button>
    </div>
    <div class="offcanvas-body">
        <div class="container pt--0 pb--0">
            <div class="search-box-form-wrap">
                <div class="search-note">
                    <p>Start typing and press Enter to search</p>
                </div>
                <form action="search-simple.php" method="GET" id="searchForm">
                    <div class="aside-search-form position-relative">
                        <label for="SearchInput" class="visually-hidden">Search</label>
                        <input id="SearchInput" 
                               name="q" 
                               type="search" 
                               class="form-control" 
                               placeholder="Search entire store…"
                               autocomplete="off"
                               required>
                        <button class="search-button" type="submit"><i class="fa fa-search"></i></button>
                    </div>
                </form>
                
                <!-- Search Suggestions -->
                <div class="search-suggestions mt-3" id="searchSuggestions" style="display: none;">
                    <div class="suggestions-header">
                        <small class="text-muted">Popular searches:</small>
                    </div>
                    <div class="suggestions-list">
                        <a href="search-simple.php?q=hair serum" class="suggestion-item">Hair Serum</a>
                        <a href="search-simple.php?q=face wash" class="suggestion-item">Face Wash</a>
                        <a href="search-simple.php?q=natural oil" class="suggestion-item">Natural Oil</a>
                        <a href="search-simple.php?q=organic" class="suggestion-item">Organic Products</a>
                        <a href="search-simple.php?q=skin care" class="suggestion-item">Skin Care</a>
                        <a href="search-simple.php?q=hair care" class="suggestion-item">Hair Care</a>
                    </div>
                </div>
                
                <!-- Quick Categories -->
                <div class="quick-categories mt-4">
                    <div class="categories-header mb-2">
                        <small class="text-muted">Browse by category:</small>
                    </div>
                    <div class="categories-grid">
                        <a href="hair-care.php" class="category-quick-link">
                            <i class="fa fa-scissors"></i>
                            <span>Hair Care</span>
                        </a>
                        <a href="skin-care.php" class="category-quick-link">
                            <i class="fa fa-heart"></i>
                            <span>Skin Care</span>
                        </a>
                        <a href="health-care.php" class="category-quick-link">
                            <i class="fa fa-medkit"></i>
                            <span>Health Care</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</aside>
<!--== End Aside Search Form ==-->

<style>
/* Search Suggestions Styles */
.search-suggestions {
    border-top: 1px solid #eee;
    padding-top: 15px;
}

.suggestions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.suggestion-item {
    display: inline-block;
    padding: 6px 12px;
    background: #f8f9fa;
    color: #495057;
    text-decoration: none;
    border-radius: 15px;
    font-size: 12px;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.suggestion-item:hover {
    background: #FF6565;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

/* Quick Categories Styles */
.quick-categories {
    border-top: 1px solid #eee;
    padding-top: 15px;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-top: 8px;
}

.category-quick-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 10px;
    background: #f8f9fa;
    color: #495057;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.category-quick-link:hover {
    background: #FF6565;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(255, 101, 101, 0.3);
}

.category-quick-link i {
    font-size: 18px;
    margin-bottom: 5px;
}

.category-quick-link span {
    font-size: 11px;
    font-weight: 500;
    text-align: center;
}

/* Mobile Responsive */
@media (max-width: 767px) {
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }
    
    .category-quick-link {
        padding: 12px 8px;
    }
    
    .category-quick-link i {
        font-size: 16px;
    }
    
    .category-quick-link span {
        font-size: 10px;
    }
    
    .suggestion-item {
        font-size: 11px;
        padding: 5px 10px;
    }
}

/* Search Input Focus Effects */
.aside-search-form .form-control:focus {
    border-color: #FF6565;
    box-shadow: 0 0 0 0.2rem rgba(255, 101, 101, 0.25);
}

/* Animation for suggestions */
.search-suggestions {
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('SearchInput');
    const searchSuggestions = document.getElementById('searchSuggestions');
    const searchForm = document.getElementById('searchForm');
    
    // Show suggestions when input is focused
    searchInput.addEventListener('focus', function() {
        if (searchSuggestions) {
            searchSuggestions.style.display = 'block';
        }
    });
    
    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.search-box-form-wrap')) {
            if (searchSuggestions) {
                searchSuggestions.style.display = 'none';
            }
        }
    });
    
    // Handle form submission
    searchForm.addEventListener('submit', function(e) {
        const query = searchInput.value.trim();
        if (!query) {
            e.preventDefault();
            searchInput.focus();
            return false;
        }
        
        // Close the search modal
        const searchModal = bootstrap.Offcanvas.getInstance(document.getElementById('AsideOffcanvasSearch'));
        if (searchModal) {
            searchModal.hide();
        }
    });
    
    // Handle Enter key
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            const query = this.value.trim();
            if (query) {
                searchForm.submit();
            }
        }
    });
    
    // Auto-complete functionality (basic)
    searchInput.addEventListener('input', function() {
        const query = this.value.toLowerCase().trim();
        
        // Simple auto-complete suggestions based on common terms
        const commonTerms = [
            'hair serum', 'face wash', 'natural oil', 'organic', 
            'skin care', 'hair care', 'health care', 'shampoo',
            'conditioner', 'moisturizer', 'cream', 'lotion'
        ];
        
        if (query.length > 2) {
            const matches = commonTerms.filter(term => 
                term.toLowerCase().includes(query)
            );
            
            if (matches.length > 0 && searchSuggestions) {
                const suggestionsList = searchSuggestions.querySelector('.suggestions-list');
                suggestionsList.innerHTML = '';
                
                matches.slice(0, 6).forEach(match => {
                    const link = document.createElement('a');
                    link.href = `search-simple.php?q=${encodeURIComponent(match)}`;
                    link.className = 'suggestion-item';
                    link.textContent = match;
                    suggestionsList.appendChild(link);
                });
                
                searchSuggestions.style.display = 'block';
            }
        }
    });
});
</script>
