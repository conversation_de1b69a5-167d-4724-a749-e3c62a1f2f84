<?php
require_once 'config/dbconfig.php';

echo "<h1>🔧 Fix MOSSFIRE Spray Slug Issue</h1>";

try {
    // First, let's see what we have
    echo "<h2>Current MOSSFIRE Products:</h2>";
    $mossfire_products = fetchAll("SELECT id, title, slug, product_code FROM products WHERE title LIKE '%MOSSFIRE%' ORDER BY id");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th>ID</th><th>Title</th><th>Current Slug</th><th>Product Code</th></tr>";
    
    foreach ($mossfire_products as $product) {
        echo "<tr>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . htmlspecialchars($product['title']) . "</td>";
        echo "<td><strong>" . ($product['slug'] ?: 'NO SLUG') . "</strong></td>";
        echo "<td>" . $product['product_code'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Now let's fix the slugs
    echo "<h2>🔧 Fixing Slugs:</h2>";
    
    foreach ($mossfire_products as $product) {
        $title = $product['title'];
        
        // Create specific slugs based on the product title
        if (stripos($title, 'LOTION') !== false) {
            $new_slug = 'mossfire-mosquito-repellent-lotion';
        } elseif (stripos($title, 'SPRAY') !== false) {
            $new_slug = 'mossfire-mosquito-repellent-spray';
        } else {
            // Fallback: create slug from title
            $new_slug = strtolower($title);
            $new_slug = preg_replace('/[^a-z0-9]+/', '-', $new_slug);
            $new_slug = trim($new_slug, '-');
        }
        
        // Update the slug
        $result = executeQuery("UPDATE products SET slug = ? WHERE id = ?", [$new_slug, $product['id']]);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Updated '{$title}' to slug: <strong>{$new_slug}</strong></p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to update '{$title}'</p>";
        }
    }
    
    // Verify the fix
    echo "<h2>✅ Verification - After Fix:</h2>";
    $updated_products = fetchAll("SELECT id, title, slug, product_code FROM products WHERE title LIKE '%MOSSFIRE%' ORDER BY id");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #e8f5e8;'><th>ID</th><th>Title</th><th>New Slug</th><th>Test Link</th></tr>";
    
    foreach ($updated_products as $product) {
        echo "<tr>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . htmlspecialchars($product['title']) . "</td>";
        echo "<td><strong>" . $product['slug'] . "</strong></td>";
        
        $test_url = "product-details.php?product=" . urlencode($product['slug']);
        echo "<td><a href='{$test_url}' target='_blank' style='color: #007bff; font-weight: bold;'>🔗 Test</a></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 Fix Applied!</h3>";
    echo "<p><strong>What was fixed:</strong></p>";
    echo "<ul>";
    echo "<li>LOTION product now has slug: <code>mossfire-mosquito-repellent-lotion</code></li>";
    echo "<li>SPRAY product now has slug: <code>mossfire-mosquito-repellent-spray</code></li>";
    echo "</ul>";
    echo "<p><strong>Test the fix:</strong></p>";
    echo "<ol>";
    echo "<li>Go to <a href='personal-care.php' target='_blank'>Personal Care page</a></li>";
    echo "<li>Click on the SPRAY product - it should now show the spray page</li>";
    echo "<li>Click on the LOTION product - it should show the lotion page</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
