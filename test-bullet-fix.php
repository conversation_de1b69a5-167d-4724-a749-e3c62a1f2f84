<?php
// Updated function to handle bullet characters in text
function formatAsBulletPoints($text) {
    if (empty($text)) {
        return '';
    }
    
    // First try to split by line breaks
    $lines = preg_split('/\r\n|\r|\n/', trim($text));
    
    // If we only have one line, try to split by bullet characters
    if (count($lines) == 1) {
        // Split by bullet characters (•, *, -, etc.) followed by space
        $lines = preg_split('/[\s]*[•\*\-\→\►\▪\▫\‣\⁃]\s*/', $text);
    }
    
    // Filter out empty lines and trim whitespace
    $lines = array_filter(array_map('trim', $lines), function($line) {
        return !empty($line);
    });
    
    if (empty($lines)) {
        return '';
    }
    
    // If there's only one meaningful line, return as paragraph
    if (count($lines) == 1) {
        return '<p>' . htmlspecialchars($lines[0]) . '</p>';
    }
    
    // Convert to bullet points
    $html = '<ul class="product-bullet-list">';
    foreach ($lines as $line) {
        // Remove any remaining bullet characters at the start
        $line = preg_replace('/^[\s\-\*\•\→\►\▪\▫\‣\⁃]+\s*/', '', $line);
        $line = trim($line);
        if (!empty($line)) {
            $html .= '<li>' . htmlspecialchars($line) . '</li>';
        }
    }
    $html .= '</ul>';
    
    return $html;
}

// Test with the exact text from your screenshot
$test_text = "• Deeply cleanses the skin, removing dirt and impurities • Brightens dull skin and promotes a radiant glow • Enriched with Vitamin C and Vitamin E for antioxidant protection • Helps restore smooth, refreshed, and revitalized skin • Gentle formula suitable for daily use • Hydrates and soothes for a soft, healthy complexion";

echo "<h2>🔸 Bullet Points Fix Test</h2>";
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Bullet Points Fix Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* Product Description Bullet List Styling */
        .product-bullet-list {
            list-style: none;
            padding-left: 0;
            margin: 15px 0;
        }

        .product-bullet-list li {
            position: relative;
            padding: 8px 0 8px 25px;
            line-height: 1.6;
            color: #555;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .product-bullet-list li:before {
            content: "•";
            position: absolute;
            left: 0;
            top: 8px;
            color: #FF6565;
            font-weight: bold;
            font-size: 16px;
        }

        .demo-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="demo-section">
            <h3>📝 Original Text (from your screenshot):</h3>
            <div style="background: #fff; border: 1px solid #ddd; padding: 15px; border-radius: 5px;">
                <?php echo htmlspecialchars($test_text); ?>
            </div>
        </div>

        <div class="demo-section">
            <h3>✨ Fixed Output (should show as proper bullet points):</h3>
            <div style="background: #fff; border: 1px solid #ddd; padding: 15px; border-radius: 5px;">
                <?php echo formatAsBulletPoints($test_text); ?>
            </div>
        </div>

        <div class="demo-section">
            <h3>🔍 How the function works:</h3>
            <ol>
                <li><strong>Detects bullet characters</strong> (•) in the text</li>
                <li><strong>Splits the text</strong> by these bullet characters</li>
                <li><strong>Creates proper HTML list</strong> with CSS styling</li>
                <li><strong>Applies red bullet points</strong> with proper spacing</li>
            </ol>
        </div>

        <div class="demo-section">
            <h3>📋 Test with different formats:</h3>
            
            <h5>Format 1: Line breaks</h5>
            <div style="background: #fff; border: 1px solid #ddd; padding: 15px; border-radius: 5px; margin: 10px 0;">
                <?php 
                $format1 = "Deeply cleanses the skin\nBrightens dull skin\nEnriched with vitamins\nGentle formula";
                echo formatAsBulletPoints($format1);
                ?>
            </div>

            <h5>Format 2: Bullet characters (•)</h5>
            <div style="background: #fff; border: 1px solid #ddd; padding: 15px; border-radius: 5px; margin: 10px 0;">
                <?php 
                $format2 = "• Deeply cleanses the skin • Brightens dull skin • Enriched with vitamins • Gentle formula";
                echo formatAsBulletPoints($format2);
                ?>
            </div>

            <h5>Format 3: Dash bullets (-)</h5>
            <div style="background: #fff; border: 1px solid #ddd; padding: 15px; border-radius: 5px; margin: 10px 0;">
                <?php 
                $format3 = "- Deeply cleanses the skin - Brightens dull skin - Enriched with vitamins - Gentle formula";
                echo formatAsBulletPoints($format3);
                ?>
            </div>
        </div>

        <div class="alert alert-success">
            <h4><i class="fas fa-check-circle"></i> ✅ Fixed!</h4>
            <p>The function now handles both:</p>
            <ul>
                <li><strong>Line breaks</strong> (when you enter each point on a new line in admin)</li>
                <li><strong>Bullet characters</strong> (when text has • symbols in it)</li>
            </ul>
            <p class="mb-0">Your product descriptions should now display as proper bullet points!</p>
        </div>

        <div class="demo-section">
            <h3>🧪 Test Your Products:</h3>
            <p>Visit any product page to see the updated bullet points, or:</p>
            <div class="row">
                <div class="col-md-6">
                    <a href="hair-care.php" class="btn btn-primary" target="_blank">
                        <i class="fas fa-eye"></i> View Hair Care Products
                    </a>
                </div>
                <div class="col-md-6">
                    <a href="skin-care.php" class="btn btn-success" target="_blank">
                        <i class="fas fa-eye"></i> View Skin Care Products
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
