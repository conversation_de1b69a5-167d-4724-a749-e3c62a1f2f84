<?php
/**
 * Live Site Diagnostic Tool
 * For troubleshooting drzianaturals.com issues
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Dr. Zia Naturals - Live Site Diagnostic</h1>";
echo "<p><strong>Site URL:</strong> drzianaturals.com</p>";
echo "<hr>";

// Test 1: Database Connection
echo "<h2>1. Database Connection Test</h2>";
try {
    require_once 'config/dbconfig.php';
    
    if (testConnection()) {
        echo "<p style='color: green;'>✅ Database connection successful!</p>";
        echo "<p><strong>Database:</strong> " . DB_NAME . "</p>";
        echo "<p><strong>Host:</strong> " . DB_HOST . "</p>";
        echo "<p><strong>Username:</strong> " . DB_USERNAME . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Database connection failed!</p>";
        echo "<p>Check your cPanel database credentials.</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "<hr>";

// Test 2: Check Tables
echo "<h2>2. Database Tables Check</h2>";
try {
    $tables = fetchAll("SHOW TABLES");
    if ($tables && count($tables) > 0) {
        echo "<p style='color: green;'>✅ Found " . count($tables) . " tables in database</p>";
        echo "<details><summary>Click to view all tables</summary><ul>";
        foreach ($tables as $table) {
            $table_name = array_values($table)[0];
            echo "<li>$table_name</li>";
        }
        echo "</ul></details>";
    } else {
        echo "<p style='color: red;'>❌ No tables found! Database may be empty.</p>";
        echo "<p><strong>Solution:</strong> Import your database SQL file through cPanel phpMyAdmin</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking tables: " . $e->getMessage() . "</p>";
}

echo "<hr>";

// Test 3: Admin Users Check
echo "<h2>3. Admin Users Check</h2>";
try {
    $admin_users = fetchAll("SELECT * FROM admin_users");
    if ($admin_users && count($admin_users) > 0) {
        echo "<p style='color: green;'>✅ Found " . count($admin_users) . " admin user(s)</p>";
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Status</th><th>Password Test</th></tr>";
        
        foreach ($admin_users as $admin) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($admin['id']) . "</td>";
            echo "<td>" . htmlspecialchars($admin['username']) . "</td>";
            echo "<td>" . htmlspecialchars($admin['email']) . "</td>";
            echo "<td>" . htmlspecialchars($admin['status']) . "</td>";
            
            // Test password
            $password_test = password_verify('admin123', $admin['password']);
            echo "<td>" . ($password_test ? '<span style="color: green;">✅ admin123</span>' : '<span style="color: red;">❌ Wrong</span>') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ No admin users found!</p>";
        echo "<p><strong>Solution:</strong> <a href='#create-admin'>Create admin user below</a></p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking admin users: " . $e->getMessage() . "</p>";
    echo "<p>admin_users table may not exist.</p>";
}

echo "<hr>";

// Test 4: Products Check
echo "<h2>4. Products Check</h2>";
try {
    $products = fetchAll("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
    $product_count = $products[0]['count'] ?? 0;
    
    if ($product_count > 0) {
        echo "<p style='color: green;'>✅ Found $product_count active products</p>";
        
        // Show sample products
        $sample_products = fetchAll("SELECT id, product_code, title, price, image FROM products WHERE status = 'active' LIMIT 5");
        echo "<h3>Sample Products:</h3>";
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Code</th><th>Title</th><th>Price</th><th>Image</th></tr>";
        
        foreach ($sample_products as $product) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($product['id']) . "</td>";
            echo "<td>" . htmlspecialchars($product['product_code']) . "</td>";
            echo "<td>" . htmlspecialchars($product['title']) . "</td>";
            echo "<td>Rs. " . number_format($product['price'], 2) . "</td>";
            echo "<td>" . htmlspecialchars($product['image'] ?: 'No image') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ No active products found!</p>";
        echo "<p><strong>Solution:</strong> Add products through admin panel or import product data</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking products: " . $e->getMessage() . "</p>";
    echo "<p>products table may not exist.</p>";
}

echo "<hr>";

// Test 5: File Permissions
echo "<h2>5. File Permissions Check</h2>";
$important_files = [
    'config/dbconfig.php',
    'admin/login.php',
    'admin/dashboard.php',
    'index.php',
    'hair-care.php'
];

foreach ($important_files as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        $perms_octal = substr(sprintf('%o', $perms), -4);
        echo "<p>✅ $file - Permissions: $perms_octal</p>";
    } else {
        echo "<p style='color: red;'>❌ $file - File not found!</p>";
    }
}

echo "<hr>";

// Test 6: PHP Configuration
echo "<h2>6. PHP Configuration</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server:</strong> " . $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown' . "</p>";
echo "<p><strong>Current Directory:</strong> " . __DIR__ . "</p>";

// Check required extensions
$required_extensions = ['pdo', 'pdo_mysql', 'mysqli', 'json'];
echo "<h3>Required PHP Extensions:</h3>";
foreach ($required_extensions as $ext) {
    $loaded = extension_loaded($ext);
    echo "<p>" . ($loaded ? '✅' : '❌') . " $ext</p>";
}

echo "<hr>";

// Quick Fix Section
echo "<h2 id='create-admin'>🔧 Quick Fixes</h2>";

// Create admin user form
echo "<h3>Create Admin User</h3>";
if ($_POST['action'] ?? '' === 'create_admin') {
    try {
        $username = 'admin';
        $password = 'admin123';
        $email = '<EMAIL>';
        $full_name = 'Administrator';
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // Check if admin_users table exists
        $table_exists = fetchSingle("SHOW TABLES LIKE 'admin_users'");
        
        if (!$table_exists) {
            // Create admin_users table
            executeQuery("
                CREATE TABLE admin_users (
                    id int(11) NOT NULL AUTO_INCREMENT,
                    username varchar(50) NOT NULL UNIQUE,
                    email varchar(100) NOT NULL UNIQUE,
                    password varchar(255) NOT NULL,
                    full_name varchar(100) NOT NULL,
                    role enum('admin','super_admin') DEFAULT 'admin',
                    status enum('active','inactive') DEFAULT 'active',
                    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");
            echo "<p style='color: green;'>✅ Created admin_users table</p>";
        }
        
        // Insert admin user
        $result = insertData("
            INSERT INTO admin_users (username, email, password, full_name, role, status) 
            VALUES (?, ?, ?, ?, 'super_admin', 'active')
            ON DUPLICATE KEY UPDATE password = VALUES(password)
        ", [$username, $email, $hashed_password, $full_name]);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Admin user created successfully!</p>";
            echo "<p><strong>Username:</strong> admin</p>";
            echo "<p><strong>Password:</strong> admin123</p>";
            echo "<p><a href='admin/login.php'>Try logging in now</a></p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create admin user</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error creating admin user: " . $e->getMessage() . "</p>";
    }
}

echo "<form method='POST'>";
echo "<input type='hidden' name='action' value='create_admin'>";
echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Create Admin User (admin/admin123)</button>";
echo "</form>";

echo "<hr>";

// Navigation links
echo "<h2>🔗 Quick Links</h2>";
echo "<p><a href='admin/login.php'>Admin Login</a></p>";
echo "<p><a href='admin/test-database.php'>Database Test</a></p>";
echo "<p><a href='index.php'>Homepage</a></p>";
echo "<p><a href='hair-care.php'>Hair Care Products</a></p>
<p><a href='skin-care.php'>Skin Care Products</a></p>
<p><a href='health-care.php'>Health Care Products</a></p>";

?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    line-height: 1.6; 
}
table { 
    border-collapse: collapse; 
    width: 100%; 
    margin: 10px 0; 
}
th, td { 
    border: 1px solid #ddd; 
    padding: 8px; 
    text-align: left; 
}
th { 
    background-color: #f2f2f2; 
}
details {
    margin: 10px 0;
}
summary {
    cursor: pointer;
    font-weight: bold;
}
hr {
    margin: 30px 0;
    border: none;
    border-top: 2px solid #eee;
}
</style>
