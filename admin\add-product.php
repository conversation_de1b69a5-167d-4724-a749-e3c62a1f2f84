<?php
session_start();
require_once '../config/dbconfig.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit();
}

$success_message = '';
$error_message = '';

// Get categories for dropdown
$categories = fetchAll("SELECT * FROM categories WHERE status = 'active' ORDER BY name");

if ($_POST) {
    $product_code = trim($_POST['product_code'] ?? '');
    $title = trim($_POST['title'] ?? '');
    $collection = trim($_POST['collection'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $key_benefits = trim($_POST['key_benefits'] ?? '');
    $ingredients = trim($_POST['ingredients'] ?? '');
    $how_to_use = trim($_POST['how_to_use'] ?? '');
    $price = floatval($_POST['price'] ?? 0);
    $sale_price = !empty($_POST['sale_price']) ? floatval($_POST['sale_price']) : null;
    $shipping_info = trim($_POST['shipping_info'] ?? '');
    $weight = trim($_POST['weight'] ?? '');
    $dimensions = trim($_POST['dimensions'] ?? '');
    $materials = trim($_POST['materials'] ?? '');
    $other_info = trim($_POST['other_info'] ?? '');
    $status = $_POST['status'] ?? 'active';
    $selected_categories = $_POST['categories'] ?? [];
    
    // Handle image upload
    $image_path = '';
    if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
        $upload_dir = '../assets/images/shop/';
        $file_extension = pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
        $new_filename = 'product_' . time() . '.' . $file_extension;
        $upload_path = $upload_dir . $new_filename;
        
        // Check if upload directory exists
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
            $image_path = 'assets/images/shop/' . $new_filename;
        } else {
            $error_message = 'Failed to upload image.';
        }
    }
    
    if (empty($error_message)) {
        try {
            // Start transaction
            $pdo->beginTransaction();
            
            // Insert product
            $sql = "INSERT INTO products (product_code, title, collection, description, key_benefits, ingredients, how_to_use, price, sale_price, image, shipping_info, weight, dimensions, materials, other_info, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $product_id = insertData($sql, [$product_code, $title, $collection, $description, $key_benefits, $ingredients, $how_to_use, $price, $sale_price, $image_path, $shipping_info, $weight, $dimensions, $materials, $other_info, $status]);
            
            if ($product_id) {
                // Insert product categories with automatic mapping
                if (!empty($selected_categories)) {
                    // Get category names for mapping
                    $category_mapping = [];
                    foreach ($selected_categories as $category_id) {
                        $category = fetchSingle("SELECT name FROM categories WHERE id = ?", [$category_id]);
                        if ($category) {
                            $category_mapping[$category_id] = $category['name'];
                        }
                    }

                    // Auto-map categories to main categories
                    $auto_mapped_categories = [];
                    foreach ($category_mapping as $cat_id => $cat_name) {
                        $auto_mapped_categories[] = $cat_id; // Add original category

                        // Auto-mapping logic
                        switch (strtolower($cat_name)) {
                            case 'face care':
                            case 'anti-acne':
                                // Face Care and Anti-Acne products should also be in Skin Care
                                $skin_care = fetchSingle("SELECT id FROM categories WHERE name = 'Skin Care'");
                                if ($skin_care && !in_array($skin_care['id'], $auto_mapped_categories)) {
                                    $auto_mapped_categories[] = $skin_care['id'];
                                }
                                break;

                            case 'health care':
                                // Health Care products stay in Health Care only
                                break;

                            case 'hair care':
                                // Hair Care products stay in Hair Care only
                                break;

                            case 'skin care':
                                // Skin Care products stay in Skin Care only
                                break;

                            case 'personal care':
                                // Personal Care products stay in Personal Care only
                                break;
                        }
                    }

                    // Remove duplicates and insert all categories
                    $auto_mapped_categories = array_unique($auto_mapped_categories);
                    foreach ($auto_mapped_categories as $category_id) {
                        executeQuery("INSERT INTO product_categories (product_id, category_id) VALUES (?, ?)", [$product_id, $category_id]);
                    }
                }
                
                // Insert product volumes if provided
                if (!empty($_POST['volumes'])) {
                    foreach ($_POST['volumes'] as $volume) {
                        if (!empty($volume['size']) && !empty($volume['price'])) {
                            executeQuery("INSERT INTO product_volumes (product_id, size, price, offer, stock_quantity) VALUES (?, ?, ?, ?, ?)", 
                                [$product_id, $volume['size'], $volume['price'], $volume['offer'] ?? '', $volume['stock'] ?? 0]);
                        }
                    }
                }
                
                $pdo->commit();
                $success_message = 'Product added successfully!';
                
                // Clear form data
                $_POST = [];
            } else {
                throw new Exception('Failed to insert product');
            }
        } catch (Exception $e) {
            $pdo->rollBack();
            $error_message = 'Error adding product: ' . $e->getMessage();
        }
    }
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Add New Product</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="products.php" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Products
                    </a>
                </div>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="product_code" class="form-label">Product Code *</label>
                                    <input type="text" class="form-control" id="product_code" name="product_code" 
                                           value="<?php echo htmlspecialchars($_POST['product_code'] ?? ''); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Product Title *</label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="collection" class="form-label">Collection</label>
                                    <input type="text" class="form-control" id="collection" name="collection"
                                           value="<?php echo htmlspecialchars($_POST['collection'] ?? 'Premium Collection'); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="price" class="form-label">Original Price (Rs.) *</label>
                                    <input type="number" step="0.01" class="form-control" id="price" name="price"
                                           value="<?php echo htmlspecialchars($_POST['price'] ?? ''); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="sale_price" class="form-label">Sale Price (Rs.)</label>
                                    <input type="number" step="0.01" class="form-control" id="sale_price" name="sale_price"
                                           value="<?php echo htmlspecialchars($_POST['sale_price'] ?? ''); ?>"
                                           placeholder="Leave empty for no sale">
                                    <div class="form-text">Enter a lower price to show as sale/discount</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description *</label>
                            <textarea class="form-control" id="description" name="description" rows="4" required><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="key_benefits" class="form-label">Key Benefits</label>
                            <textarea class="form-control" id="key_benefits" name="key_benefits" rows="4"
                                      placeholder="Enter key benefits (one per line)..."><?php echo htmlspecialchars($_POST['key_benefits'] ?? ''); ?></textarea>
                            <div class="form-text">List the main benefits of this product. Use bullet points or separate lines.</div>
                        </div>

                        <div class="mb-3">
                            <label for="ingredients" class="form-label">Ingredients</label>
                            <textarea class="form-control" id="ingredients" name="ingredients" rows="3"
                                      placeholder="Enter main ingredients..."><?php echo htmlspecialchars($_POST['ingredients'] ?? ''); ?></textarea>
                            <div class="form-text">List the key ingredients used in this product.</div>
                        </div>

                        <div class="mb-3">
                            <label for="how_to_use" class="form-label">How to Use</label>
                            <textarea class="form-control" id="how_to_use" name="how_to_use" rows="4"
                                      placeholder="Enter usage instructions..."><?php echo htmlspecialchars($_POST['how_to_use'] ?? ''); ?></textarea>
                            <div class="form-text">Provide step-by-step instructions on how to use this product.</div>
                        </div>

                        <div class="mb-3">
                            <label for="image" class="form-label">Product Image</label>
                            <input type="file" class="form-control" id="image" name="image" accept="image/*">
                            <small class="form-text text-muted">Upload product image (JPG, PNG, GIF)</small>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="weight" class="form-label">Weight</label>
                                    <input type="text" class="form-control" id="weight" name="weight" 
                                           value="<?php echo htmlspecialchars($_POST['weight'] ?? ''); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="dimensions" class="form-label">Dimensions</label>
                                    <input type="text" class="form-control" id="dimensions" name="dimensions" 
                                           value="<?php echo htmlspecialchars($_POST['dimensions'] ?? ''); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="materials" class="form-label">Materials/Ingredients</label>
                            <textarea class="form-control" id="materials" name="materials" rows="2"><?php echo htmlspecialchars($_POST['materials'] ?? ''); ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="shipping_info" class="form-label">Shipping Information</label>
                            <input type="text" class="form-control" id="shipping_info" name="shipping_info" 
                                   value="<?php echo htmlspecialchars($_POST['shipping_info'] ?? 'Shipping from Pakistan, Shipping Fees Rs. 200'); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="other_info" class="form-label">Other Information</label>
                            <textarea class="form-control" id="other_info" name="other_info" rows="2"><?php echo htmlspecialchars($_POST['other_info'] ?? ''); ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="categories" class="form-label">Categories</label>
                                    <select class="form-control" id="categories" name="categories[]" multiple>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <small class="form-text text-muted">Hold Ctrl to select multiple categories</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header">
                                <h5>Product Volumes/Variants</h5>
                            </div>
                            <div class="card-body">
                                <div id="volumes-container">
                                    <div class="volume-row row mb-3">
                                        <div class="col-md-3">
                                            <input type="text" class="form-control" name="volumes[0][size]" placeholder="Size (e.g., 100ml)">
                                        </div>
                                        <div class="col-md-3">
                                            <input type="number" step="0.01" class="form-control" name="volumes[0][price]" placeholder="Price">
                                        </div>
                                        <div class="col-md-3">
                                            <input type="text" class="form-control" name="volumes[0][offer]" placeholder="Offer (optional)">
                                        </div>
                                        <div class="col-md-2">
                                            <input type="number" class="form-control" name="volumes[0][stock]" placeholder="Stock" value="0">
                                        </div>
                                        <div class="col-md-1">
                                            <button type="button" class="btn btn-danger btn-sm remove-volume">×</button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-secondary" id="add-volume">Add Volume</button>
                            </div>
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Add Product
                            </button>
                            <a href="products.php" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
let volumeIndex = 1;

document.getElementById('add-volume').addEventListener('click', function() {
    const container = document.getElementById('volumes-container');
    const newRow = document.createElement('div');
    newRow.className = 'volume-row row mb-3';
    newRow.innerHTML = `
        <div class="col-md-3">
            <input type="text" class="form-control" name="volumes[${volumeIndex}][size]" placeholder="Size (e.g., 100ml)">
        </div>
        <div class="col-md-3">
            <input type="number" step="0.01" class="form-control" name="volumes[${volumeIndex}][price]" placeholder="Price">
        </div>
        <div class="col-md-3">
            <input type="text" class="form-control" name="volumes[${volumeIndex}][offer]" placeholder="Offer (optional)">
        </div>
        <div class="col-md-2">
            <input type="number" class="form-control" name="volumes[${volumeIndex}][stock]" placeholder="Stock" value="0">
        </div>
        <div class="col-md-1">
            <button type="button" class="btn btn-danger btn-sm remove-volume">×</button>
        </div>
    `;
    container.appendChild(newRow);
    volumeIndex++;
});

document.addEventListener('click', function(e) {
    if (e.target.classList.contains('remove-volume')) {
        e.target.closest('.volume-row').remove();
    }
});
</script>

<?php include 'includes/footer.php'; ?>
